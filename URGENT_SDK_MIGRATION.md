# 🚨 URGENT: Deprecated SDK Migration Required

## Critical Issue
The Vertex AI SDK generative AI module was **deprecated on June 24, 2025** (last month). The Query Intelligence service is still using the deprecated SDK and needs immediate migration.

## Current Status
- **Deprecated SDK in use**: `google-cloud-aiplatform==1.99.0`
- **Service affected**: Query Intelligence (Python)
- **Migration guide exists**: `/services/query-intelligence/SDK_MIGRATION_GUIDE.md`
- **Migration NOT implemented**: Code still uses deprecated SDK

## Why This is Critical
1. **Deprecated SDK may stop working at any time**
2. **New features and bug fixes only in new SDK**
3. **Security updates not available for deprecated SDK**
4. **May explain recent API errors and failures**

## Immediate Actions Required

### 1. Execute the Migration (TODAY)
```bash
cd services/query-intelligence
poetry remove google-cloud-aiplatform
poetry add google-genai
```

### 2. Implement New LLMServiceV2
The migration guide already provides the complete implementation at:
`/services/query-intelligence/SDK_MIGRATION_GUIDE.md`

### 3. Test and Deploy
- Run the test script provided in the guide
- Use feature flag for gradual rollout
- Monitor for any issues

## Impact if Not Addressed
- Service may fail completely when deprecated SDK is shut down
- No access to latest Gemini models
- Security vulnerabilities
- Performance degradation

## Model Update Note
The new SDK uses `gemini-2.0-flash-exp` (not 2.5 as mentioned). This is the latest available model.

## Next Steps
1. Stop all other work and prioritize this migration
2. Follow the migration guide step-by-step
3. Test thoroughly before full deployment
4. Update all documentation to reflect new SDK

This is not optional - the deprecated SDK could stop working at any moment!