warning: method `partition_results` is never used
   --> src/services/analyzer.rs:616:8
    |
33  | impl AnalysisService {
    | -------------------- method in this implementation
...
616 |     fn partition_results(
    |        ^^^^^^^^^^^^^^^^^
    |
    = note: `#[warn(dead_code)]` on by default

warning: function `validate_jwt_token` is never used
   --> src/api/middleware/auth.rs:854:10
    |
854 | async fn validate_jwt_token(token: &str, state: &AppState) -> Result<(String, i64), String> {
    |          ^^^^^^^^^^^^^^^^^^

warning: trait `DurationExt` is never used
    --> src/api/middleware/auth.rs:1085:7
     |
1085 | trait DurationExt {
     |       ^^^^^^^^^^^

warning: field `pool_config` is never read
   --> src/parser/mod.rs:146:5
    |
142 | pub struct TreeSitterParser {
    |            ---------------- field in this struct
...
146 |     pool_config: ParserPoolConfig,
    |     ^^^^^^^^^^^

warning: method `read_file_streaming` is never used
    --> src/parser/mod.rs:1210:14
     |
240  | impl TreeSitterParser {
     | --------------------- method in this implementation
...
1210 |     async fn read_file_streaming(&self, file_path: &Path, file_size: u64) -> Result<String, ParseError> {
     |              ^^^^^^^^^^^^^^^^^^^

warning: `analysis-engine` (lib) generated 5 warnings
   Compiling analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
warning: unused variable: `config`
  --> src/main.rs:43:9
   |
43 |     let config = config::ServiceConfig::from_env()?;
   |         ^^^^^^ help: if this is intentional, prefix it with an underscore: `_config`
   |
   = note: `#[warn(unused_variables)]` on by default

warning: `analysis-engine` (bin "analysis-engine") generated 1 warning
    Finished `bench` profile [optimized] target(s) in 2m 00s
     Running benches/analysis_bench.rs (target/release/deps/analysis_bench-6ba1a262a61d3584)
Gnuplot not found, using plotters backend
Benchmarking ast_parsing/parse_file/small
Benchmarking ast_parsing/parse_file/small: Warming up for 3.0000 s
Benchmarking ast_parsing/parse_file/small: Collecting 100 samples in estimated 5.7173 s (15k iterations)
Benchmarking ast_parsing/parse_file/small: Analyzing
ast_parsing/parse_file/small
                        time:   [335.98 µs 348.32 µs 364.09 µs]
Found 12 outliers among 100 measurements (12.00%)
  3 (3.00%) high mild
  9 (9.00%) high severe
Benchmarking ast_parsing/parse_file/medium
Benchmarking ast_parsing/parse_file/medium: Warming up for 3.0000 s
Benchmarking ast_parsing/parse_file/medium: Collecting 100 samples in estimated 7.4946 s (10k iterations)
Benchmarking ast_parsing/parse_file/medium: Analyzing
ast_parsing/parse_file/medium
                        time:   [716.20 µs 744.73 µs 779.55 µs]
Found 12 outliers among 100 measurements (12.00%)
  5 (5.00%) high mild
  7 (7.00%) high severe
Benchmarking ast_parsing/parse_file/large_10k
Benchmarking ast_parsing/parse_file/large_10k: Warming up for 3.0000 s

Warning: Unable to complete 100 samples in 5.0s. You may wish to increase target time to 8.9s, or reduce sample count to 50.
Benchmarking ast_parsing/parse_file/large_10k: Collecting 100 samples in estimated 8.8690 s (100 iterations)
Benchmarking ast_parsing/parse_file/large_10k: Analyzing
ast_parsing/parse_file/large_10k
                        time:   [86.016 ms 88.242 ms 90.899 ms]
Found 14 outliers among 100 measurements (14.00%)
  3 (3.00%) high mild
  11 (11.00%) high severe

Benchmarking pattern_detection/detect_patterns_medium
Benchmarking pattern_detection/detect_patterns_medium: Warming up for 3.0000 s
Benchmarking pattern_detection/detect_patterns_medium: Collecting 100 samples in estimated 5.0320 s (444k iterations)
Benchmarking pattern_detection/detect_patterns_medium: Analyzing
pattern_detection/detect_patterns_medium
                        time:   [10.769 µs 10.990 µs 11.281 µs]
Found 9 outliers among 100 measurements (9.00%)
  2 (2.00%) high mild
  7 (7.00%) high severe
Benchmarking pattern_detection/detect_patterns_large
Benchmarking pattern_detection/detect_patterns_large: Warming up for 3.0000 s

Warning: Unable to complete 100 samples in 5.0s. You may wish to increase target time to 5.7s, enable flat sampling, or reduce sample count to 60.
Benchmarking pattern_detection/detect_patterns_large: Collecting 100 samples in estimated 5.7322 s (5050 iterations)
Benchmarking pattern_detection/detect_patterns_large: Analyzing
pattern_detection/detect_patterns_large
                        time:   [1.0739 ms 1.1058 ms 1.1456 ms]
Found 11 outliers among 100 measurements (11.00%)
  5 (5.00%) high mild
  6 (6.00%) high severe

Benchmarking language_detection/detect_languages
Benchmarking language_detection/detect_languages: Warming up for 3.0000 s

Warning: Unable to complete 100 samples in 5.0s. You may wish to increase target time to 7.9s, enable flat sampling, or reduce sample count to 50.
Benchmarking language_detection/detect_languages: Collecting 100 samples in estimated 7.8745 s (5050 iterations)
Benchmarking language_detection/detect_languages: Analyzing
language_detection/detect_languages
                        time:   [1.5199 ms 1.5495 ms 1.5864 ms]
Found 7 outliers among 100 measurements (7.00%)
  1 (1.00%) high mild
  6 (6.00%) high severe
Benchmarking language_detection/detect_languages_with_stats
Benchmarking language_detection/detect_languages_with_stats: Warming up for 3.0000 s

Warning: Unable to complete 100 samples in 5.0s. You may wish to increase target time to 7.8s, enable flat sampling, or reduce sample count to 50.
Benchmarking language_detection/detect_languages_with_stats: Collecting 100 samples in estimated 7.8476 s (5050 iterations)
Benchmarking language_detection/detect_languages_with_stats: Analyzing
language_detection/detect_languages_with_stats
                        time:   [1.7691 ms 1.8033 ms 1.8444 ms]
Found 31 outliers among 100 measurements (31.00%)
  12 (12.00%) low severe
  5 (5.00%) low mild
  6 (6.00%) high mild
  8 (8.00%) high severe

Benchmarking concurrent_parsing/parse_50_files_sequential
Benchmarking concurrent_parsing/parse_50_files_sequential: Warming up for 3.0000 s

Warning: Unable to complete 10 samples in 5.0s. You may wish to increase target time to 8.8s or enable flat sampling.
Benchmarking concurrent_parsing/parse_50_files_sequential: Collecting 10 samples in estimated 8.8408 s (55 iterations)
Benchmarking concurrent_parsing/parse_50_files_sequential: Analyzing
concurrent_parsing/parse_50_files_sequential
                        time:   [151.09 ms 153.14 ms 155.98 ms]
Found 1 outliers among 10 measurements (10.00%)
  1 (10.00%) high severe
Benchmarking concurrent_parsing/parse_50_files_parallel
Benchmarking concurrent_parsing/parse_50_files_parallel: Warming up for 3.0000 s
Benchmarking concurrent_parsing/parse_50_files_parallel: Collecting 10 samples in estimated 6.5748 s (110 iterations)
Benchmarking concurrent_parsing/parse_50_files_parallel: Analyzing
concurrent_parsing/parse_50_files_parallel
                        time:   [60.149 ms 62.038 ms 66.375 ms]
Found 1 outliers among 10 measurements (10.00%)
  1 (10.00%) high severe

Benchmarking memory_usage/parse_lines/1000
Benchmarking memory_usage/parse_lines/1000: Warming up for 3.0000 s
Benchmarking memory_usage/parse_lines/1000: Collecting 10 samples in estimated 5.4872 s (605 iterations)
Benchmarking memory_usage/parse_lines/1000: Analyzing
memory_usage/parse_lines/1000
                        time:   [9.0306 ms 9.2640 ms 9.6235 ms]
Benchmarking memory_usage/parse_lines/10000
Benchmarking memory_usage/parse_lines/10000: Warming up for 3.0000 s
Benchmarking memory_usage/parse_lines/10000: Collecting 10 samples in estimated 9.8243 s (110 iterations)
Benchmarking memory_usage/parse_lines/10000: Analyzing
memory_usage/parse_lines/10000
                        time:   [86.007 ms 86.916 ms 88.913 ms]
Found 1 outliers among 10 measurements (10.00%)
  1 (10.00%) high severe
Benchmarking memory_usage/parse_lines/50000
Benchmarking memory_usage/parse_lines/50000: Warming up for 3.0000 s
Benchmarking memory_usage/parse_lines/50000: Collecting 10 samples in estimated 6.5546 s (30 iterations)
Benchmarking memory_usage/parse_lines/50000: Analyzing
memory_usage/parse_lines/50000
                        time:   [216.13 ms 221.13 ms 225.92 ms]

