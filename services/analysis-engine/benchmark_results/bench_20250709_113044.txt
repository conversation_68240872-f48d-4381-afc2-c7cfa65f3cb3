   Compiling futures-io v0.3.31
   Compiling num-traits v0.2.19
   Compiling regex-automata v0.4.9
   Compiling url v2.5.4
   Compiling getrandom v0.1.16
   Compiling anstyle v1.0.11
   Compiling either v1.15.0
   Compiling utf8parse v0.2.2
   Compiling anstyle-query v1.1.3
   Compiling colorchoice v1.0.4
   Compiling is_terminal_polyfill v1.70.1
   Compiling toml_datetime v0.6.11
   Compiling anstyle-parse v0.2.7
   Compiling serde_spanned v0.6.9
   Compiling tree-sitter-php v0.23.11
   Compiling futures-util v0.3.31
   Compiling rayon v1.10.0
   Compiling tree-sitter-yaml v0.6.1
   Compiling tree-sitter-html v0.20.4
   Compiling anstream v0.6.19
   Compiling tree-sitter-json v0.21.0
   Compiling tree-sitter-md v0.3.2
   Compiling tree-sitter-ruby v0.23.1
   Compiling tree-sitter-bash v0.23.3
   Compiling tree-sitter-css v0.21.1
   Compiling heck v0.5.0
   Compiling clap_lex v0.7.5
   Compiling toml_write v0.1.2
   Compiling winnow v0.7.11
   Compiling strsim v0.11.1
   Compiling rand_core v0.5.1
   Compiling clap_derive v4.5.40
   Compiling clap_builder v4.5.40
   Compiling rand_chacha v0.2.2
   Compiling num-integer v0.1.46
   Compiling num-complex v0.4.6
   Compiling num-bigint v0.4.6
   Compiling num-iter v0.1.45
   Compiling toml_edit v0.22.27
   Compiling simple_asn1 v0.6.3
   Compiling bigdecimal v0.4.8
   Compiling hyper v1.6.0
   Compiling tower v0.5.2
   Compiling axum-core v0.4.5
   Compiling tower-http v0.6.6
   Compiling tower v0.4.13
   Compiling regex v1.11.1
   Compiling jsonwebtoken v9.3.1
   Compiling tree-sitter v0.25.6
   Compiling h2 v0.3.26
   Compiling hyper-util v0.1.14
   Compiling axum v0.7.9
   Compiling futures-executor v0.3.31
   Compiling futures v0.3.31
   Compiling globset v0.4.16
   Compiling env_logger v0.8.4
   Compiling tokio-tungstenite v0.26.2
   Compiling ignore v0.4.23
   Compiling num-rational v0.4.2
   Compiling ahash v0.8.12
   Compiling hyper-tls v0.6.0
   Compiling reqwest v0.12.22
   Compiling hyper-timeout v0.5.2
   Compiling half v2.6.0
   Compiling plotters-backend v0.3.7
   Compiling http-types v2.12.0
   Compiling ciborium-io v0.2.2
   Compiling fastrand v1.9.0
   Compiling predicates-core v1.0.9
   Compiling waker-fn v1.2.0
   Compiling parking v2.2.1
   Compiling bit-vec v0.6.3
   Compiling redis v0.25.4
   Compiling bit-set v0.5.3
   Compiling axum v0.8.4
   Compiling futures-lite v1.13.0
   Compiling governor v0.6.3
   Compiling ciborium-ll v0.2.2
   Compiling plotters-svg v0.3.7
   Compiling tracing-subscriber v0.3.19
   Compiling tokei v12.1.2
   Compiling num v0.4.3
   Compiling chrono v0.4.41
   Compiling tonic v0.12.3
   Compiling google-cloud-metadata v0.5.1
   Compiling google-cloud-auth v0.17.2
   Compiling hyper v0.14.32
   Compiling reqwest-middleware v0.4.2
   Compiling google-cloud-auth v0.16.0
   Compiling google-cloud-storage v0.24.0
   Compiling google-cloud-gax v0.19.2
   Compiling google-cloud-googleapis v0.16.1
   Compiling tokio-tungstenite v0.27.0
   Compiling git2 v0.19.0
   Compiling toml v0.8.23
   Compiling clap v4.5.40
   Compiling rand v0.7.3
   Compiling itertools v0.10.5
   Compiling hyper-tls v0.5.0
   Compiling reqwest v0.11.27
   Compiling serde_qs v0.8.5
   Compiling crossbeam-queue v0.3.12
   Compiling sqlparser v0.39.0
   Compiling nom v8.0.0
   Compiling quick-xml v0.31.0
   Compiling google-cloud-longrunning v0.21.0
   Compiling google-cloud-spanner v0.33.0
   Compiling google-cloud-pubsub v0.30.0
   Compiling deadpool-runtime v0.1.4
   Compiling termtree v0.5.1
   Compiling cast v0.3.0
   Compiling retain_mut v0.1.9
   Compiling infer v0.2.3
   Compiling deadpool v0.9.5
   Compiling criterion-plot v0.5.0
   Compiling predicates-tree v1.0.12
   Compiling fraction v0.15.3
   Compiling plotters v0.3.7
   Compiling iso8601 v0.6.3
   Compiling ciborium v0.2.2
   Compiling fancy-regex v0.13.0
   Compiling analysis-engine v0.1.0 (/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine)
   Compiling predicates v3.1.3
   Compiling mockall_derive v0.12.1
   Compiling assert-json-diff v2.0.2
   Compiling tinytemplate v1.2.1
   Compiling is-terminal v0.4.16
   Compiling oorandom v11.1.5
   Compiling bytecount v0.6.9
   Compiling fragile v2.0.1
   Compiling num-cmp v0.1.0
   Compiling anes v0.1.6
   Compiling downcast v0.11.0
   Compiling jsonschema v0.18.3
   Compiling criterion v0.5.1
   Compiling wiremock v0.5.22
   Compiling mockall v0.12.1
warning: method `partition_results` is never used
   --> src/services/analyzer.rs:616:8
    |
33  | impl AnalysisService {
    | -------------------- method in this implementation
...
616 |     fn partition_results(
    |        ^^^^^^^^^^^^^^^^^
    |
    = note: `#[warn(dead_code)]` on by default

warning: function `validate_jwt_token` is never used
   --> src/api/middleware/auth.rs:854:10
    |
854 | async fn validate_jwt_token(token: &str, state: &AppState) -> Result<(String, i64), String> {
    |          ^^^^^^^^^^^^^^^^^^

warning: trait `DurationExt` is never used
    --> src/api/middleware/auth.rs:1085:7
     |
1085 | trait DurationExt {
     |       ^^^^^^^^^^^

warning: field `pool_config` is never read
   --> src/parser/mod.rs:146:5
    |
142 | pub struct TreeSitterParser {
    |            ---------------- field in this struct
...
146 |     pool_config: ParserPoolConfig,
    |     ^^^^^^^^^^^

warning: method `read_file_streaming` is never used
    --> src/parser/mod.rs:1210:14
     |
240  | impl TreeSitterParser {
     | --------------------- method in this implementation
...
1210 |     async fn read_file_streaming(&self, file_path: &Path, file_size: u64) -> Result<String, ParseError> {
     |              ^^^^^^^^^^^^^^^^^^^

warning: `analysis-engine` (lib) generated 5 warnings
warning: unused import: `analysis_engine::models::FileAnalysis`
 --> benches/analysis_bench.rs:7:5
  |
7 | use analysis_engine::models::FileAnalysis;
  |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

error[E0277]: the trait bound `FromFn<..., ..., ..., _>: Service<...>` is not satisfied
   --> src/main.rs:88:16
    |
88  |         .layer(axum::middleware::from_fn_with_state(state.clone(), api::middleware::auth_middleware))
    |          ----- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ unsatisfied trait bound
    |          |
    |          required by a bound introduced by this call
    |
    = help: the trait `tower_service::Service<axum::http::Request<Body>>` is not implemented for `FromFn<fn(State<...>, ..., ...) -> ... {auth_middleware}, ..., ..., _>`
    = help: the following other types implement trait `tower_service::Service<Request>`:
              axum::middleware::FromFn<F, S, I, (T1, T2)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4, T5)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4, T5, T6)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4, T5, T6, T7)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4, T5, T6, T7, T8)>
              axum::middleware::FromFn<F, S, I, (T1, T2, T3, T4, T5, T6, T7, T8, T9)>
            and 8 others
note: required by a bound in `Router::<S>::layer`
   --> /Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/axum-0.8.4/src/routing/mod.rs:306:21
    |
303 |     pub fn layer<L>(self, layer: L) -> Router<S>
    |            ----- required by a bound in this associated function
...
306 |         L::Service: Service<Request> + Clone + Send + Sync + 'static,
    |                     ^^^^^^^^^^^^^^^^ required by this bound in `Router::<S>::layer`
    = note: the full name for the type has been written to '/Users/<USER>/Documents/GitHub/episteme/services/analysis-engine/target/release/deps/analysis_engine-6e6e3f5769d2903f.long-type-7680230653294562480.txt'
    = note: consider using `--verbose` to print the full type name to the console

error[E0599]: no method named `detect_languages` found for struct `LanguageDetector` in the current scope
   --> benches/analysis_bench.rs:221:35
    |
221 |             let result = detector.detect_languages(temp_dir.path());
    |                                   ^^^^^^^^^^^^^^^^
    |
help: there is a method `detect_languages_with_stats` with a similar name
    |
221 |             let result = detector.detect_languages_with_stats(temp_dir.path());
    |                                                   +++++++++++

For more information about this error, try `rustc --explain E0277`.
error: could not compile `analysis-engine` (bin "analysis-engine") due to 1 previous error
warning: build failed, waiting for other jobs to finish...
For more information about this error, try `rustc --explain E0599`.
warning: `analysis-engine` (bench "analysis_bench") generated 1 warning
error: could not compile `analysis-engine` (bench "analysis_bench") due to 1 previous error; 1 warning emitted
