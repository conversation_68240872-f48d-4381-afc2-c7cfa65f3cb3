# Update Analysis Engine to Use Gemini Flash 2.5

## Current Issue
The Analysis Engine is currently using `text-embedding-004` for generating embeddings, but it should be using Gemini Flash 2.5 as per the platform requirements.

## Required Changes

### 1. Update Embeddings Service

The embeddings service needs to be updated to use Gemini Flash 2.5 instead of text-embedding-004.

**File**: `src/services/embeddings.rs`

**Changes needed**:
1. Update the endpoint URL from:
   ```
   https://{location}-aiplatform.googleapis.com/v1/projects/{project}/locations/{location}/publishers/google/models/text-embedding-004:predict
   ```
   To:
   ```
   https://{location}-aiplatform.googleapis.com/v1/projects/{project}/locations/{location}/publishers/google/models/gemini-2.0-flash-exp:generateContent
   ```

2. Update the request/response structures to match Gemini's API format
3. Update the embedding dimension constant (Gemini Flash 2.5 may have different dimensions)
4. Modify the request payload to use Gemini's content generation format with embedding instructions

### 2. Update Request Structure

Gemini Flash uses a different API structure:

```rust
#[derive(Debug, Serialize)]
struct GeminiRequest {
    contents: Vec<Content>,
    generation_config: GenerationConfig,
    system_instruction: Option<SystemInstruction>,
}

#[derive(Debug, Serialize)]
struct Content {
    parts: Vec<Part>,
}

#[derive(Debug, Serialize)]
struct Part {
    text: String,
}

#[derive(Debug, Serialize)]
struct GenerationConfig {
    temperature: f32,
    top_p: f32,
    top_k: i32,
    max_output_tokens: i32,
    response_mime_type: String,
}
```

### 3. Update the Embedding Generation Logic

Since Gemini Flash 2.5 is a generative model, not a dedicated embedding model, you'll need to:

1. Use a specific prompt to generate embeddings:
   ```rust
   let prompt = format!(
       "Generate a dense numerical embedding vector for the following code snippet. \
        Return only a JSON array of 768 floating-point numbers between -1 and 1 that \
        captures the semantic meaning of this code:\n\n{}",
       code_content
   );
   ```

2. Parse the JSON response to extract the embedding vector
3. Handle potential parsing errors gracefully

### 4. Update Model References

Update all references from "text-embedding-004" to "gemini-2.0-flash-exp" or the appropriate Gemini Flash 2.5 model name.

### 5. Test and Validate

1. Ensure the new embeddings maintain semantic quality
2. Verify the embedding dimensions match expectations
3. Test performance and latency
4. Validate circuit breaker behavior with the new endpoint

## Alternative Approach

If Gemini Flash 2.5 doesn't provide suitable embeddings, consider:
1. Using Gemini Flash 2.5 for code understanding and analysis
2. Keeping text-embedding-004 specifically for vector embeddings
3. Using both models for their respective strengths

## Note
The exact model name might be `gemini-2.5-flash-001` or similar - verify the correct model ID with Google Cloud documentation.