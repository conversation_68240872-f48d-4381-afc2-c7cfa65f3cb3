use anyhow::Result;
use crate::models::{Symbol, SymbolType, Range, Position, ParseError, ParseErrorType};

/// SQL parser adapter using sqlparser crate
pub struct SqlAdapter;

impl SqlAdapter {
    pub fn parse_sql(content: &str) -> Result<Vec<Symbol>, ParseError> {
        use sqlparser::dialect::{GenericDialect, PostgreSqlDialect, MySqlDialect, SQLiteDialect};
        use sqlparser::parser::Parser;
        
        // Try multiple dialects for broader compatibility
        let dialects: Vec<Box<dyn sqlparser::dialect::Dialect>> = vec![
            Box::new(GenericDialect {}),
            Box::new(PostgreSqlDialect {}),
            Box::new(MySqlDialect {}),
            Box::new(SQLiteDialect {}),
        ];
        
        let mut symbols = Vec::new();
        let mut parse_success = false;
        
        for dialect in dialects {
            match Parser::parse_sql(&*dialect, content) {
                Ok(statements) => {
                    parse_success = true;
                    for statement in statements {
                        extract_sql_symbols(&statement, &mut symbols);
                    }
                    break;
                }
                Err(_) => continue,
            }
        }
        
        if !parse_success {
            return Err(ParseError {
                file_path: String::new(),
                error_type: ParseErrorType::ParseError,
                message: "Failed to parse SQL with any dialect".to_string(),
                position: None,
            });
        }
        
        Ok(symbols)
    }
}

fn extract_sql_symbols(statement: &sqlparser::ast::Statement, symbols: &mut Vec<Symbol>) {
    use sqlparser::ast::*;
    
    match statement {
        Statement::CreateTable { name, .. } => {
            symbols.push(Symbol {
                name: name.to_string(),
                symbol_type: SymbolType::Class, // Tables as classes
                range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 0, column: 0, byte: 0 },
            },
                visibility: None,
                signature: None,
                documentation: None,
            });
        }
        Statement::CreateView { name, .. } => {
            symbols.push(Symbol {
                name: name.to_string(),
                symbol_type: SymbolType::Class, // Views as classes
                range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 0, column: 0, byte: 0 },
            },
                visibility: None,
                signature: None,
                documentation: None,
            });
        }
        Statement::CreateFunction { name, .. } => {
            symbols.push(Symbol {
                name: name.to_string(),
                symbol_type: SymbolType::Function,
                range: Range {
                    start: Position { line: 0, column: 0, byte: 0 },
                    end: Position { line: 0, column: 0, byte: 0 },
                },
                visibility: None,
                signature: None,
                documentation: None,
            });
        }
        Statement::CreateProcedure { name, .. } => {
            symbols.push(Symbol {
                name: name.to_string(),
                symbol_type: SymbolType::Function,
                range: Range {
                    start: Position { line: 0, column: 0, byte: 0 },
                    end: Position { line: 0, column: 0, byte: 0 },
                },
                visibility: None,
                signature: None,
                documentation: None,
            });
        }
        Statement::CreateIndex { name, .. } => {
            if let Some(name) = name {
                symbols.push(Symbol {
                    name: name.to_string(),
                    symbol_type: SymbolType::Variable, // Indexes as variables
                    range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 0, column: 0, byte: 0 },
            },
                    visibility: None,
                    signature: None,
                    documentation: None,
                });
            }
        }
        _ => {
            // Handle other statement types as needed
        }
    }
}

/// XML parser adapter using quick-xml crate
pub struct XmlAdapter;

impl XmlAdapter {
    pub fn parse_xml(content: &str) -> Result<Vec<Symbol>, ParseError> {
        use quick_xml::events::Event;
        use quick_xml::Reader;
        
        let mut reader = Reader::from_str(content);
        reader.trim_text(true);
        
        let mut symbols = Vec::new();
        let mut buf = Vec::new();
        let mut current_line = 1;
        let mut element_stack = Vec::new();
        
        loop {
            match reader.read_event_into(&mut buf) {
                Ok(Event::Start(ref e)) => {
                    let name = String::from_utf8_lossy(e.name().as_ref()).to_string();
                    let position = reader.buffer_position();
                    
                    // Track nested structure
                    element_stack.push(name.clone());
                    
                    // Add element as symbol
                    symbols.push(Symbol {
                        name: name.clone(),
                        symbol_type: if element_stack.len() == 1 {
                            SymbolType::Namespace // Root elements
                        } else {
                            SymbolType::Class // Nested elements
                        },
                        range: Range {
                            start: Position {
                                line: current_line as u32,
                                column: 0,
                                byte: position as u32,
                            },
                            end: Position {
                                line: current_line as u32,
                                column: 0,
                                byte: position as u32,
                            },
                        },
                        visibility: None,
                        signature: None,
                        documentation: None,
                    });
                    
                    // Extract attributes as variables
                    for attr in e.attributes() {
                        if let Ok(attr) = attr {
                            let attr_name = String::from_utf8_lossy(attr.key.as_ref()).to_string();
                            symbols.push(Symbol {
                                name: format!("{}.{}", name, attr_name),
                                symbol_type: SymbolType::Variable,
                                range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 0, column: 0, byte: 0 },
            },
                                visibility: None,
                                signature: None,
                                documentation: None,
                            });
                        }
                    }
                }
                Ok(Event::End(_)) => {
                    element_stack.pop();
                }
                Ok(Event::Text(e)) => {
                    // Count newlines in text
                    let text = e.unescape().unwrap_or_default();
                    current_line += text.matches('\n').count() as u32;
                }
                Ok(Event::Eof) => break,
                Err(e) => {
                    return Err(ParseError {
                        file_path: String::new(),
                        error_type: ParseErrorType::ParseError,
                        message: format!("XML parse error: {}", e),
                        position: Some(Position {
                            line: current_line,
                            column: 0,
                            byte: 0,
                        }),
                    });
                }
                _ => {}
            }
            
            buf.clear();
        }
        
        Ok(symbols)
    }
}

/// TOML parser adapter
pub struct TomlAdapter;

impl TomlAdapter {
    pub fn parse_toml(content: &str) -> Result<Vec<Symbol>, ParseError> {
        match content.parse::<toml::Value>() {
            Ok(value) => {
                let mut symbols = Vec::new();
                extract_toml_symbols(&value, "", &mut symbols);
                Ok(symbols)
            }
            Err(e) => Err(ParseError {
                file_path: String::new(),
                error_type: ParseErrorType::ParseError,
                message: format!("TOML parse error: {}", e),
                position: None,
            }),
        }
    }
}

fn extract_toml_symbols(value: &toml::Value, prefix: &str, symbols: &mut Vec<Symbol>) {
    match value {
        toml::Value::Table(table) => {
            for (key, val) in table {
                let full_key = if prefix.is_empty() {
                    key.clone()
                } else {
                    format!("{}.{}", prefix, key)
                };
                
                symbols.push(Symbol {
                    name: full_key.clone(),
                    symbol_type: match val {
                        toml::Value::Table(_) => SymbolType::Namespace,
                        toml::Value::Array(_) => SymbolType::Class,
                        _ => SymbolType::Variable,
                    },
                    range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 0, column: 0, byte: 0 },
            },
                    visibility: None,
                    signature: None,
                    documentation: None,
                });
                
                // Recursively extract nested tables
                if let toml::Value::Table(_) = val {
                    extract_toml_symbols(val, &full_key, symbols);
                }
            }
        }
        _ => {
            // Top-level non-table values
            if prefix.is_empty() {
                symbols.push(Symbol {
                    name: "root".to_string(),
                    symbol_type: SymbolType::Variable,
                    range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: 0, column: 0, byte: 0 },
            },
                    visibility: None,
                    signature: None,
                    documentation: None,
                });
            }
        }
    }
}

// Range already has Default impl in models

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_sql_parsing() {
        let sql = r#"
            CREATE TABLE users (
                id INT PRIMARY KEY,
                name VARCHAR(100)
            );
            
            CREATE VIEW active_users AS
            SELECT * FROM users WHERE active = true;
            
            CREATE FUNCTION get_user_name(user_id INT)
            RETURNS VARCHAR(100);
        "#;
        
        let symbols = SqlAdapter::parse_sql(sql).unwrap();
        assert!(symbols.len() >= 3);
        
        let names: Vec<&str> = symbols.iter().map(|s| s.name.as_str()).collect();
        assert!(names.contains(&"users"));
        assert!(names.contains(&"active_users"));
    }
    
    #[test]
    fn test_xml_parsing() {
        let xml = r#"
            <root>
                <users>
                    <user id="1" name="John">
                        <email><EMAIL></email>
                    </user>
                </users>
            </root>
        "#;
        
        let symbols = XmlAdapter::parse_xml(xml).unwrap();
        assert!(symbols.len() > 0);
        
        let names: Vec<&str> = symbols.iter().map(|s| s.name.as_str()).collect();
        assert!(names.contains(&"root"));
        assert!(names.contains(&"users"));
        assert!(names.contains(&"user"));
    }
    
    #[test]
    fn test_toml_parsing() {
        let toml = r#"
            [package]
            name = "my-app"
            version = "1.0.0"
            
            [dependencies]
            serde = "1.0"
            tokio = { version = "1.0", features = ["full"] }
        "#;
        
        let symbols = TomlAdapter::parse_toml(toml).unwrap();
        assert!(symbols.len() > 0);
        
        let names: Vec<&str> = symbols.iter().map(|s| s.name.as_str()).collect();
        assert!(names.contains(&"package"));
        assert!(names.contains(&"package.name"));
        assert!(names.contains(&"dependencies"));
    }
}