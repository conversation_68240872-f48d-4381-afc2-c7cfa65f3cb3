use anyhow::{Context, Result};
use std::path::Path;
use std::collections::HashMap;
use std::sync::Arc;
use tree_sitter::{Parser, Node, Language};
use crate::models::{FileAnalysis, AstNode, Position, Range, Symbol, SymbolType, FileMetrics, ParseError, ParseErrorType, CodeChunk, ChunkType};
use sha2::{Sha256, Digest};
use tokio::io::{AsyncReadExt, BufReader, AsyncBufReadExt};
use tokio::fs::File;
use tokio::sync::Semaphore;
use crossbeam_queue::SegQueue;
use tracing::debug;

mod adapters;
use adapters::{SqlAdapter, XmlAdapter, TomlAdapter};



// Import the language constants from the tree-sitter crates
use tree_sitter_rust::LANGUAGE as RUST_LANGUAGE;
use tree_sitter_python::LANGUAGE as PYTHON_LANGUAGE;
use tree_sitter_javascript::LANGUAGE as JAVASCRIPT_LANGUAGE;
use tree_sitter_typescript::LANGUAGE_TYPESCRIPT;
use tree_sitter_go::LANGUAGE as GO_LANGUAGE;
use tree_sitter_java::LANGUAGE as JAVA_LANGUAGE;
use tree_sitter_c::LANGUAGE as C_LANGUAGE;
use tree_sitter_cpp::LANGUAGE as CPP_LANGUAGE;
// Additional languages - adding stable ones
use tree_sitter_html::language as HTML_LANGUAGE;
use tree_sitter_css::language as CSS_LANGUAGE;
use tree_sitter_json::language as JSON_LANGUAGE;
use tree_sitter_yaml::language as YAML_LANGUAGE;
// use tree_sitter_php::LANGUAGE_PHP as PHP_LANGUAGE;
use tree_sitter_ruby::LANGUAGE as RUBY_LANGUAGE;
// // Mobile languages
// use tree_sitter_swift::LANGUAGE as SWIFT_LANGUAGE;
// use tree_sitter_kotlin::LANGUAGE as KOTLIN_LANGUAGE;
// use tree_sitter_objc::LANGUAGE as OBJC_LANGUAGE;
// // Data languages
// use tree_sitter_sql::LANGUAGE as SQL_LANGUAGE;
// use tree_sitter_r::LANGUAGE as R_LANGUAGE;
// use tree_sitter_julia::LANGUAGE as JULIA_LANGUAGE;
// // Functional languages
// use tree_sitter_haskell::LANGUAGE as HASKELL_LANGUAGE;
// use tree_sitter_scala::LANGUAGE as SCALA_LANGUAGE;
// use tree_sitter_clojure::LANGUAGE as CLOJURE_LANGUAGE;
// use tree_sitter_erlang::LANGUAGE as ERLANG_LANGUAGE;
// use tree_sitter_elixir::LANGUAGE as ELIXIR_LANGUAGE;
// Other languages
use tree_sitter_bash::LANGUAGE as BASH_LANGUAGE;
// use tree_sitter_xml::language as XML_LANGUAGE;
use tree_sitter_md::LANGUAGE as MARKDOWN_LANGUAGE;

// Use the already declared adapters module

/// A pool of parsers for a specific language to reduce allocation overhead
#[derive(Debug)]
struct ParserPool {
    /// Queue of available parsers
    parsers: SegQueue<Parser>,
    /// Language for this pool
    language: Language,
    /// Semaphore to limit concurrent parser usage
    semaphore: Arc<Semaphore>,
    /// Maximum number of parsers in the pool
    max_size: usize,
    /// Current number of parsers created
    current_size: Arc<std::sync::atomic::AtomicUsize>,
}

impl ParserPool {
    /// Create a new parser pool for a language
    fn new(language: Language, max_size: usize) -> Result<Self> {
        // Pre-populate with one parser
        let mut parser = Parser::new();
        parser.set_language(&language)
            .context("Failed to set language for parser")?;

        let pool = Self {
            parsers: SegQueue::new(),
            language,
            semaphore: Arc::new(Semaphore::new(max_size)),
            max_size,
            current_size: Arc::new(std::sync::atomic::AtomicUsize::new(0)),
        };

        pool.parsers.push(parser);
        pool.current_size.store(1, std::sync::atomic::Ordering::Relaxed);

        Ok(pool)
    }

    /// Get a parser from the pool, creating one if necessary
    async fn get_parser(&self) -> Result<Parser> {
        // Acquire semaphore permit to limit concurrent usage
        let _permit = self.semaphore.acquire().await
            .map_err(|_| anyhow::anyhow!("Failed to acquire parser semaphore"))?;

        // Try to get an existing parser from the queue
        if let Some(parser) = self.parsers.pop() {
            return Ok(parser);
        }

        // If no parser available and we haven't reached max size, create a new one
        let current = self.current_size.load(std::sync::atomic::Ordering::Relaxed);
        if current < self.max_size {
            let mut parser = Parser::new();
            parser.set_language(&self.language)
                .context("Failed to set language for new parser")?;
            self.current_size.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
            debug!("Created new parser for language, total: {}", current + 1);
            return Ok(parser);
        }

        // If we've reached max size, wait for a parser to become available
        // This is a fallback that should rarely happen due to semaphore limiting
        tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;

        // Try again with a timeout to avoid infinite recursion
        let mut attempts = 0;
        loop {
            if let Some(parser) = self.parsers.pop() {
                return Ok(parser);
            }

            attempts += 1;
            if attempts > 100 {
                return Err(anyhow::anyhow!("Timeout waiting for parser to become available"));
            }

            tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        }
    }

    /// Return a parser to the pool
    fn return_parser(&self, parser: Parser) {
        self.parsers.push(parser);
    }
}

/// Enhanced TreeSitter parser with memory pooling
pub struct TreeSitterParser {
    /// Parser pools for each language
    parser_pools: HashMap<String, Arc<ParserPool>>,
    /// Configuration for pool sizes
    pool_config: ParserPoolConfig,
}

/// Configuration for parser pools
#[derive(Debug, Clone)]
pub struct ParserPoolConfig {
    /// Maximum parsers per language pool
    pub max_parsers_per_language: usize,
    /// Initial parsers to create per language
    pub initial_parsers_per_language: usize,
}

impl Default for ParserPoolConfig {
    fn default() -> Self {
        Self {
            max_parsers_per_language: num_cpus::get().max(4), // At least 4, up to CPU count
            initial_parsers_per_language: 1,
        }
    }
}

/// Configuration for streaming file processing
#[derive(Debug, Clone)]
pub struct StreamingConfig {
    /// Size threshold above which to use streaming (bytes)
    pub streaming_threshold: u64,
    /// Maximum file size to process (bytes)
    pub max_file_size: u64,
    /// Buffer size for reading chunks (bytes)
    pub buffer_size: usize,
    /// Maximum memory usage for content accumulation (bytes)
    pub max_memory_usage: u64,
    /// Whether to enable incremental parsing for very large files
    pub incremental_parsing: bool,
}

impl Default for StreamingConfig {
    fn default() -> Self {
        Self {
            streaming_threshold: 10 * 1024 * 1024, // 10MB
            max_file_size: 500 * 1024 * 1024,      // 500MB
            buffer_size: 64 * 1024,                // 64KB
            max_memory_usage: 100 * 1024 * 1024,   // 100MB
            incremental_parsing: true,
        }
    }
}

/// Represents a chunk of file content for streaming processing
#[derive(Debug, Clone)]
pub struct ContentChunk {
    pub data: String,
    pub offset: usize,
    pub line_start: usize,
    pub is_final: bool,
}



/// Streaming hash calculator that can process content incrementally
pub struct StreamingHasher {
    hasher: Sha256,
    total_bytes: u64,
}

impl StreamingHasher {
    pub fn new() -> Self {
        Self {
            hasher: Sha256::new(),
            total_bytes: 0,
        }
    }

    pub fn update(&mut self, data: &[u8]) {
        self.hasher.update(data);
        self.total_bytes += data.len() as u64;
    }

    pub fn finalize(self) -> (String, u64) {
        let hash = format!("{:x}", self.hasher.finalize());
        (hash, self.total_bytes)
    }
}

/// Metrics for a content chunk
#[derive(Debug, Clone)]
struct ChunkMetrics {
    line_count: usize,
    char_count: usize,
    comment_lines: usize,
    blank_lines: usize,
    code_lines: usize,
}

impl TreeSitterParser {
    pub fn new() -> Result<Self> {
        Self::new_with_config(ParserPoolConfig::default())
    }

    pub fn new_with_config(config: ParserPoolConfig) -> Result<Self> {
        let mut parser_pools = HashMap::new();

        let languages = vec![
            // Original languages - these work without version conflicts
            ("rust", RUST_LANGUAGE.into()),
            ("javascript", JAVASCRIPT_LANGUAGE.into()),
            ("typescript", LANGUAGE_TYPESCRIPT.into()),
            ("python", PYTHON_LANGUAGE.into()),
            ("go", GO_LANGUAGE.into()),
            ("java", JAVA_LANGUAGE.into()),
            ("c", C_LANGUAGE.into()),
            ("cpp", CPP_LANGUAGE.into()),
            // Additional stable languages
            ("html", HTML_LANGUAGE().into()),
            ("css", CSS_LANGUAGE().into()),
            ("json", JSON_LANGUAGE().into()),
            ("yaml", YAML_LANGUAGE().into()),
            // ("php", PHP_LANGUAGE().into()), // Commented out due to version conflicts
            ("ruby", RUBY_LANGUAGE.into()),
            // ("swift", SWIFT_LANGUAGE.into()),
            // ("kotlin", KOTLIN_LANGUAGE.into()),
            // ("objc", OBJC_LANGUAGE.into()),
            // ("sql", SQL_LANGUAGE.into()),
            // ("r", R_LANGUAGE.into()),
            // ("julia", JULIA_LANGUAGE.into()),
            // ("haskell", HASKELL_LANGUAGE.into()),
            // ("scala", SCALA_LANGUAGE.into()),
            // ("clojure", CLOJURE_LANGUAGE.into()),
            // ("erlang", ERLANG_LANGUAGE.into()),
            // ("elixir", ELIXIR_LANGUAGE.into()),
            ("bash", BASH_LANGUAGE.into()),
            // ("xml", XML_LANGUAGE.into()),
            ("markdown", MARKDOWN_LANGUAGE.into()),
        ];

        for (lang_name, language) in languages {
            let pool = ParserPool::new(language, config.max_parsers_per_language)
                .context(format!("Failed to create parser pool for {}", lang_name))?;
            parser_pools.insert(lang_name.to_string(), Arc::new(pool));
        }

        debug!("Created parser pools for {} languages with max {} parsers each",
               parser_pools.len(), config.max_parsers_per_language);

        Ok(Self {
            parser_pools,
            pool_config: config,
        })
    }

    pub async fn parse_file(&self, file_path: &Path) -> Result<FileAnalysis, ParseError> {
        self.parse_file_with_config(file_path, &StreamingConfig::default()).await
    }

    pub async fn parse_file_with_config(&self, file_path: &Path, config: &StreamingConfig) -> Result<FileAnalysis, ParseError> {
        // Check file size to determine processing strategy
        let metadata = tokio::fs::metadata(file_path).await.map_err(|e| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::Other,
            message: format!("Failed to get file metadata: {}", e),
            position: None,
        })?;

        let file_size = metadata.len();

        // Check if file is too large
        if file_size > config.max_file_size {
            return Err(ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::FileTooLarge,
                message: format!("File too large: {} bytes (max: {} bytes)", file_size, config.max_file_size),
                position: None,
            });
        }

        if file_size > config.streaming_threshold {
            // Use streaming for large files
            self.parse_file_streaming(file_path, file_size, config).await
        } else {
            // Regular processing for smaller files
            self.parse_file_regular(file_path).await
        }
    }

    async fn parse_file_regular(&self, file_path: &Path) -> Result<FileAnalysis, ParseError> {
        let content = tokio::fs::read_to_string(file_path).await.map_err(|e| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::Other,
            message: e.to_string(),
            position: None,
        })?;
        
        self.parse_content(file_path, &content).await
    }

    async fn parse_content(&self, file_path: &Path, content: &str) -> Result<FileAnalysis, ParseError> {
        let language = self.detect_language(file_path)?;

        // Handle SQL, XML, and TOML with custom adapters
        match language {
            "sql" => return self.parse_sql_content(file_path, content).await,
            "xml" => return self.parse_xml_content(file_path, content).await,
            "toml" => return self.parse_toml_content(file_path, content).await,
            _ => {}
        }

        let parser_pool = self.parser_pools.get(language).ok_or_else(|| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::UnsupportedLanguage,
            message: format!("Unsupported language: {}", language),
            position: None,
        })?;

        // Get a parser from the pool
        let mut parser = parser_pool.get_parser().await.map_err(|e| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::Other,
            message: format!("Failed to get parser from pool: {}", e),
            position: None,
        })?;

        // Parse the content
        let tree = parser.parse(&content, None).ok_or_else(|| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::ParseError,
            message: "Failed to parse file".to_string(),
            position: None,
        })?;

        // Return parser to pool for reuse
        parser_pool.return_parser(parser);
        
        let root_node = tree.root_node();
        let ast = self.build_ast(&root_node, &content);
        let symbols = self.extract_symbols(&root_node, &content);
        let metadata = self.extract_metadata(&content);

        // Calculate content hash
        let mut hasher = Sha256::new();
        hasher.update(content.as_bytes());
        let content_hash = format!("{:x}", hasher.finalize());
        
        // Get file size
        let file_meta = tokio::fs::metadata(file_path).await.ok();
        let size_bytes = file_meta.as_ref().map(|m| m.len());
        
        // Extract chunks for embedding
        let chunks = self.extract_chunks(&ast, &content, language);
        
        Ok(FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language: language.to_string(),
            content_hash,
            size_bytes,
            ast,
            metrics: metadata,
            chunks: Some(chunks),
            symbols: Some(symbols),
        })
    }

    /// Parse large files using streaming to minimize memory usage
    async fn parse_file_streaming(&self, file_path: &Path, file_size: u64, config: &StreamingConfig) -> Result<FileAnalysis, ParseError> {
        let _language = self.detect_language(file_path)?;

        // For very large files, we need to balance between memory usage and parsing accuracy
        // Strategy: Read file in chunks, but still need full content for tree-sitter parsing
        // We'll use a hybrid approach: stream for hash calculation and basic metrics,
        // but load content in manageable chunks for AST parsing

        if file_size > config.max_memory_usage {
            // For extremely large files, use chunk-based processing
            self.parse_file_chunked(file_path, file_size, config).await
        } else {
            // For moderately large files, use streaming read but full AST parsing
            self.parse_file_streaming_hybrid(file_path, file_size, config).await
        }
    }

    /// Hybrid streaming approach: stream for I/O but full parsing
    async fn parse_file_streaming_hybrid(&self, file_path: &Path, file_size: u64, config: &StreamingConfig) -> Result<FileAnalysis, ParseError> {
        let _language = self.detect_language(file_path)?;

        // Stream the file content while calculating hash and basic metrics
        let content_chunks = self.create_content_stream(file_path, config).await?;
        let mut streaming_hasher = StreamingHasher::new();
        let mut accumulated_content = String::with_capacity(std::cmp::min(file_size as usize, config.max_memory_usage as usize));
        let mut line_count = 0;
        let mut _char_count = 0;

        for chunk in content_chunks {
            // Update hash incrementally
            streaming_hasher.update(chunk.data.as_bytes());

            // Count lines and characters
            line_count += chunk.data.lines().count();
            _char_count += chunk.data.chars().count();

            // Accumulate content for AST parsing (with memory limit check)
            if accumulated_content.len() + chunk.data.len() <= config.max_memory_usage as usize {
                accumulated_content.push_str(&chunk.data);
            } else {
                // If we exceed memory limit, truncate and add warning
                tracing::warn!("File {} too large for full AST parsing, truncating at {} bytes",
                             file_path.display(), accumulated_content.len());
                break;
            }
        }

        let (content_hash, total_bytes) = streaming_hasher.finalize();

        // Parse the accumulated content (may be truncated for very large files)
        let analysis = self.parse_content(file_path, &accumulated_content).await?;

        // Override the hash and size with streaming results
        Ok(FileAnalysis {
            path: analysis.path,
            language: analysis.language,
            content_hash,
            size_bytes: Some(total_bytes),
            ast: analysis.ast,
            metrics: FileMetrics {
                lines_of_code: analysis.metrics.lines_of_code,
                total_lines: Some(line_count as u32),
                complexity: analysis.metrics.complexity,
                maintainability_index: analysis.metrics.maintainability_index,
                function_count: analysis.metrics.function_count,
                class_count: analysis.metrics.class_count,
                comment_ratio: analysis.metrics.comment_ratio,
            },
            chunks: analysis.chunks,
            symbols: analysis.symbols,
        })
    }

    /// Process extremely large files in chunks without loading full content
    async fn parse_file_chunked(&self, file_path: &Path, _file_size: u64, config: &StreamingConfig) -> Result<FileAnalysis, ParseError> {
        let language = self.detect_language(file_path)?;

        // For extremely large files, we can't do full AST parsing
        // Instead, we'll do statistical analysis and pattern-based extraction
        let content_chunks = self.create_content_stream(file_path, config).await?;
        let mut streaming_hasher = StreamingHasher::new();

        // Metrics tracking
        let mut line_count = 0;
        let mut _char_count = 0;
        let mut comment_lines = 0;
        let mut _blank_lines = 0;
        let mut code_lines = 0;

        // Symbol extraction using regex patterns (language-specific)
        let mut symbols = Vec::new();
        let mut chunks = Vec::new();

        let mut current_line = 0;
        let mut current_offset = 0;

        for chunk in content_chunks {

            // Update hash incrementally
            streaming_hasher.update(chunk.data.as_bytes());

            // Process chunk for metrics and symbols
            let chunk_metrics = self.analyze_chunk(&chunk.data, language, current_line);
            line_count += chunk_metrics.line_count;
            _char_count += chunk_metrics.char_count;
            comment_lines += chunk_metrics.comment_lines;
            _blank_lines += chunk_metrics.blank_lines;
            code_lines += chunk_metrics.code_lines;

            // Extract symbols from chunk using pattern matching
            let chunk_symbols = self.extract_symbols_from_chunk(&chunk.data, language, current_line, current_offset);
            symbols.extend(chunk_symbols);

            // Create code chunks for embedding
            let code_chunks = self.create_chunks_from_content(&chunk.data, language, current_line, current_offset);
            chunks.extend(code_chunks);

            current_line += chunk.data.lines().count();
            current_offset += chunk.data.len();
        }

        let (content_hash, total_bytes) = streaming_hasher.finalize();

        // Create a minimal AST node for the root
        let root_ast = AstNode {
            node_type: "source_file".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { line: line_count as u32, column: 0, byte: total_bytes as u32 },
            },
            children: Vec::new(), // No detailed AST for chunked processing
            properties: None,
            text: None,
        };

        Ok(FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language: language.to_string(),
            content_hash,
            size_bytes: Some(total_bytes),
            ast: root_ast,
            metrics: FileMetrics {
                lines_of_code: code_lines as u32,
                total_lines: Some(line_count as u32),
                complexity: 1, // Minimal complexity for chunked processing
                maintainability_index: 50.0, // Default maintainability
                function_count: None,
                class_count: None,
                comment_ratio: if line_count > 0 { Some(comment_lines as f64 / line_count as f64) } else { None },
            },
            chunks: Some(chunks),
            symbols: Some(symbols),
        })
    }

    /// Create a stream of content chunks from a file
    async fn create_content_stream(&self, file_path: &Path, config: &StreamingConfig) -> Result<Vec<ContentChunk>, ParseError> {
        let file = File::open(file_path).await.map_err(|e| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::Other,
            message: format!("Failed to open file: {}", e),
            position: None,
        })?;

        let reader = BufReader::with_capacity(config.buffer_size, file);
        let mut lines = reader.lines();
        let mut chunks = Vec::new();
        let mut current_offset = 0;
        let mut current_line = 0;
        let mut buffer = String::new();
        let chunk_size = config.buffer_size;

        loop {
            buffer.clear();
            let mut bytes_read = 0;
            let start_line = current_line;

            // Read lines until we reach the chunk size
            while bytes_read < chunk_size {
                match lines.next_line().await {
                    Ok(Some(line)) => {
                        if !buffer.is_empty() {
                            buffer.push('\n');
                            bytes_read += 1;
                        }
                        buffer.push_str(&line);
                        bytes_read += line.len();
                        current_line += 1;
                    }
                    Ok(None) => {
                        // End of file
                        if !buffer.is_empty() {
                            chunks.push(ContentChunk {
                                data: buffer.clone(),
                                offset: current_offset,
                                line_start: start_line,
                                is_final: true,
                            });
                        }
                        return Ok(chunks);
                    }
                    Err(e) => {
                        return Err(ParseError {
                            file_path: file_path.to_string_lossy().to_string(),
                            error_type: ParseErrorType::Other,
                            message: format!("Failed to read line: {}", e),
                            position: None,
                        });
                    }
                }
            }

            if !buffer.is_empty() {
                chunks.push(ContentChunk {
                    data: buffer.clone(),
                    offset: current_offset,
                    line_start: start_line,
                    is_final: false,
                });
                current_offset += buffer.len();
            }
        }
    }

    /// Analyze a chunk of content for basic metrics
    fn analyze_chunk(&self, content: &str, language: &str, _start_line: usize) -> ChunkMetrics {
        let lines: Vec<&str> = content.lines().collect();
        let line_count = lines.len();
        let char_count = content.chars().count();

        let mut comment_lines = 0;
        let mut blank_lines = 0;
        let mut code_lines = 0;

        // Language-specific comment detection
        let (single_comment, multi_comment_start, multi_comment_end) = match language {
            "rust" | "javascript" | "typescript" | "java" | "c" | "cpp" | "go" => ("//", "/*", "*/"),
            "python" | "ruby" => ("#", "\"\"\"", "\"\"\""),
            "html" | "xml" => ("", "<!--", "-->"),
            "css" => ("", "/*", "*/"),
            _ => ("//", "/*", "*/"), // Default
        };

        let mut in_multiline_comment = false;

        for line in lines {
            let trimmed = line.trim();

            if trimmed.is_empty() {
                blank_lines += 1;
            } else if !single_comment.is_empty() && trimmed.starts_with(single_comment) {
                comment_lines += 1;
            } else if !multi_comment_start.is_empty() && (trimmed.starts_with(multi_comment_start) || in_multiline_comment) {
                comment_lines += 1;
                if trimmed.contains(multi_comment_end) {
                    in_multiline_comment = false;
                } else if trimmed.starts_with(multi_comment_start) {
                    in_multiline_comment = true;
                }
            } else {
                code_lines += 1;
            }
        }

        ChunkMetrics {
            line_count,
            char_count,
            comment_lines,
            blank_lines,
            code_lines,
        }
    }

    /// Extract symbols from a chunk using pattern matching
    fn extract_symbols_from_chunk(&self, content: &str, language: &str, start_line: usize, start_offset: usize) -> Vec<Symbol> {
        let mut symbols = Vec::new();

        // Language-specific regex patterns for symbol extraction
        let patterns = match language {
            "rust" => vec![
                (r"fn\s+(\w+)", SymbolType::Function),
                (r"struct\s+(\w+)", SymbolType::Class),
                (r"enum\s+(\w+)", SymbolType::Class),
                (r"trait\s+(\w+)", SymbolType::Interface),
                (r"impl.*?(\w+)", SymbolType::Class),
                (r"const\s+(\w+)", SymbolType::Variable),
                (r"static\s+(\w+)", SymbolType::Variable),
            ],
            "python" => vec![
                (r"def\s+(\w+)", SymbolType::Function),
                (r"class\s+(\w+)", SymbolType::Class),
                (r"async\s+def\s+(\w+)", SymbolType::Function),
            ],
            "javascript" | "typescript" => vec![
                (r"function\s+(\w+)", SymbolType::Function),
                (r"class\s+(\w+)", SymbolType::Class),
                (r"const\s+(\w+)\s*=", SymbolType::Variable),
                (r"let\s+(\w+)\s*=", SymbolType::Variable),
                (r"var\s+(\w+)\s*=", SymbolType::Variable),
            ],
            "java" => vec![
                (r"public\s+class\s+(\w+)", SymbolType::Class),
                (r"private\s+class\s+(\w+)", SymbolType::Class),
                (r"protected\s+class\s+(\w+)", SymbolType::Class),
                (r"public\s+\w+\s+(\w+)\s*\(", SymbolType::Function),
                (r"private\s+\w+\s+(\w+)\s*\(", SymbolType::Function),
                (r"protected\s+\w+\s+(\w+)\s*\(", SymbolType::Function),
            ],
            _ => vec![], // No patterns for unsupported languages
        };

        // Simple pattern matching without regex for now
        for (pattern_prefix, symbol_type) in patterns {
            let prefix = pattern_prefix.split('(').next().unwrap_or("").trim();

            for (line_idx, line) in content.lines().enumerate() {
                let trimmed_line = line.trim();
                if trimmed_line.starts_with(prefix) {
                    // Extract symbol name using simple string parsing
                    if let Some(name) = extract_symbol_name(trimmed_line, prefix) {
                        symbols.push(Symbol {
                            name,
                            symbol_type: symbol_type.clone(),
                            range: Range {
                                start: Position {
                                    line: (start_line + line_idx) as u32,
                                    column: 0,
                                    byte: start_offset as u32,
                                },
                                end: Position {
                                    line: (start_line + line_idx) as u32,
                                    column: line.len() as u32,
                                    byte: (start_offset + line.len()) as u32,
                                },
                            },
                            visibility: None,
                            signature: None,
                            documentation: None,
                        });
                    }
                }
            }
        }

        symbols
    }

    /// Create code chunks from content for embedding
    fn create_chunks_from_content(&self, content: &str, language: &str, start_line: usize, start_offset: usize) -> Vec<CodeChunk> {
        let mut chunks = Vec::new();
        let lines: Vec<&str> = content.lines().collect();

        // Create chunks based on logical boundaries (functions, classes, etc.)
        const CHUNK_SIZE: usize = 50; // Lines per chunk
        const OVERLAP: usize = 5;     // Overlapping lines between chunks

        let mut i = 0;
        while i < lines.len() {
            let end = std::cmp::min(i + CHUNK_SIZE, lines.len());
            let chunk_lines = &lines[i..end];
            let chunk_content = chunk_lines.join("\n");

            if !chunk_content.trim().is_empty() {
                let content_len = chunk_content.len();
                chunks.push(CodeChunk {
                    chunk_id: format!("chunk_{}_{}", start_line + i, start_line + end - 1),
                    content: chunk_content,
                    range: Range {
                        start: Position {
                            line: (start_line + i) as u32,
                            column: 0,
                            byte: start_offset as u32,
                        },
                        end: Position {
                            line: (start_line + end - 1) as u32,
                            column: 0,
                            byte: (start_offset + content_len) as u32,
                        },
                    },
                    chunk_type: ChunkType::Function, // Could be enhanced with better detection
                    language: Some(language.to_string()),
                    context: None, // Could be populated with more context
                });
            }

            // Move forward with overlap
            i += CHUNK_SIZE - OVERLAP;
            if i >= lines.len() {
                break;
            }
        }

        chunks
    }

    fn detect_language<'a>(&self, file_path: &'a Path) -> Result<&'a str, ParseError> {
        let extension = file_path.extension().and_then(|s| s.to_str()).unwrap_or("");
        match extension {
            "rs" => Ok("rust"),
            "py" => Ok("python"),
            "js" | "mjs" => Ok("javascript"),
            "ts" | "tsx" => Ok("typescript"),
            "go" => Ok("go"),
            "java" => Ok("java"),
            "c" | "h" => Ok("c"),
            "cpp" | "cc" | "cxx" | "hpp" => Ok("cpp"),
            "php" => Ok("php"),
            "rb" => Ok("ruby"),
            "swift" => Ok("swift"),
            "kt" | "kts" => Ok("kotlin"),
            "m" | "mm" => Ok("objc"),
            "scala" => Ok("scala"),
            "hs" => Ok("haskell"),
            "ex" | "exs" => Ok("elixir"),
            "sh" | "bash" => Ok("bash"),
            "html" | "htm" => Ok("html"),
            "css" | "scss" | "sass" => Ok("css"),
            "json" => Ok("json"),
            "yaml" | "yml" => Ok("yaml"),
            "xml" => Ok("xml"),
            "md" | "markdown" => Ok("markdown"),
            "sql" => Ok("sql"),
            "toml" => Ok("toml"),
            "r" | "R" => Ok("r"),
            "jl" => Ok("julia"),
            "clj" | "cljs" | "cljc" => Ok("clojure"),
            "erl" | "hrl" => Ok("erlang"),
            _ => Err(ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::UnsupportedLanguage,
                message: format!("Unsupported file extension: {}", extension),
                position: None,
            }),
        }
    }

    fn build_ast(&self, node: &Node, source: &str) -> AstNode {
        self.build_ast_optimized(node, source, 0, 100) // Max depth of 100
    }
    
    fn build_ast_optimized(&self, node: &Node, source: &str, depth: usize, max_depth: usize) -> AstNode {
        // Optimization: Skip deep traversal for performance
        if depth > max_depth {
            return AstNode {
                node_type: "truncated".to_string(),
                name: Some("...".to_string()),
                range: Range {
                    start: self.convert_position(node.start_position()),
                    end: self.convert_position(node.end_position()),
                },
                children: vec![],
                properties: None,
                text: Some("/* Deep tree truncated for performance */".to_string()),
            };
        }
        
        // Optimization: Only extract text for leaf nodes or important nodes
        let should_extract_text = node.child_count() == 0 || matches!(
            node.kind(),
            "string_literal" | "number_literal" | "identifier" | "comment"
        );
        
        let text = if should_extract_text {
            node.utf8_text(source.as_bytes()).ok().map(|s| s.to_string())
        } else {
            None
        };
        
        let name = node.child_by_field_name("name")
            .and_then(|n| n.utf8_text(source.as_bytes()).ok())
            .map(|s| s.to_string());
        
        // Optimization: Use iterator with early termination for large child lists
        let children: Vec<AstNode> = if node.child_count() > 1000 {
            // For extremely large nodes, sample children
            node.children(&mut node.walk())
                .step_by(10) // Take every 10th child
                .take(100)   // Maximum 100 children
                .map(|child| self.build_ast_optimized(&child, source, depth + 1, max_depth))
                .collect()
        } else {
            node.children(&mut node.walk())
                .map(|child| self.build_ast_optimized(&child, source, depth + 1, max_depth))
                .collect()
        };
        
        AstNode {
            node_type: node.kind().to_string(),
            name,
            range: Range {
                start: Position { 
                    line: node.start_position().row as u32,
                    column: node.start_position().column as u32,
                    byte: node.start_byte() as u32,
                },
                end: Position {
                    line: node.end_position().row as u32,
                    column: node.end_position().column as u32,
                    byte: node.end_byte() as u32,
                },
            },
            children,
            properties: None,
            text,
        }
    }

    fn extract_symbols(&self, node: &Node, source: &str) -> Vec<Symbol> {
        let mut symbols = Vec::new();
        self.traverse_for_symbols(node, source, &mut symbols);
        symbols
    }

    fn traverse_for_symbols(&self, node: &Node, source: &str, symbols: &mut Vec<Symbol>) {
        self.traverse_for_symbols_optimized(node, source, symbols, 0, 50);
    }
    
    fn traverse_for_symbols_optimized(&self, node: &Node, source: &str, symbols: &mut Vec<Symbol>, depth: usize, max_depth: usize) {
        // Optimization: Stop at maximum depth to prevent stack overflow
        if depth > max_depth {
            return;
        }
        
        // Optimization: Early exit for nodes that can't contain symbols
        let node_kind = node.kind();
        if matches!(node_kind, "comment" | "string" | "string_literal" | "number" | "number_literal") {
            return;
        }
        
        let symbol_type = match node_kind {
            "function_item" | "function_declaration" | "method_definition" | "function_definition" => Some(SymbolType::Function),
            "struct_item" | "class_declaration" | "class_definition" => Some(SymbolType::Class),
            "let_declaration" | "const_item" | "variable_declarator" | "const_declaration" => Some(SymbolType::Variable),
            "trait_item" | "interface_declaration" => Some(SymbolType::Interface),
            _ => None,
        };

        if let Some(st) = symbol_type {
            let name_node = node.child_by_field_name("name").or_else(|| node.child_by_field_name("identifier"));
            if let Some(name_node) = name_node {
                if let Ok(name_text) = name_node.utf8_text(source.as_bytes()) {
                    // Optimization: Skip anonymous symbols
                    if !name_text.is_empty() && !name_text.starts_with('_') {
                        symbols.push(Symbol {
                            name: name_text.to_string(),
                            symbol_type: st,
                            range: Range {
                                start: self.convert_position(node.start_position()),
                                end: self.convert_position(node.end_position()),
                            },
                            visibility: None,
                            signature: None,
                            documentation: None,
                        });
                    }
                }
            }
        }

        // Optimization: Use cursor for more efficient traversal
        let mut cursor = node.walk();
        for child in node.children(&mut cursor) {
            self.traverse_for_symbols_optimized(&child, source, symbols, depth + 1, max_depth);
        }
    }

    fn convert_position(&self, point: tree_sitter::Point) -> Position {
        Position {
            line: point.row as u32,
            column: point.column as u32,
            byte: 0, // Tree-sitter doesn't provide byte offset directly in Point - will be set during parsing
        }
    }

    fn extract_metadata(&self, content: &str) -> FileMetrics {
        let lines: Vec<&str> = content.lines().collect();
        let total_lines = lines.len() as u32;
        let mut lines_of_code = 0u32;
        let mut comment_lines = 0u32;
        
        for line in &lines {
            let trimmed = line.trim();
            if !trimmed.is_empty() {
                if trimmed.starts_with("//") || trimmed.starts_with("#") || trimmed.starts_with("/*") || trimmed.starts_with("*") {
                    comment_lines += 1;
                } else {
                    lines_of_code += 1;
                }
            }
        }
        
        let comment_ratio = if lines_of_code > 0 {
            Some(comment_lines as f64 / lines_of_code as f64)
        } else {
            None
        };
        
        FileMetrics {
            lines_of_code,
            total_lines: Some(total_lines),
            complexity: 0, // Will be calculated by metrics service
            maintainability_index: 0.0, // Will be calculated by metrics service
            function_count: None,
            class_count: None,
            comment_ratio,
        }
    }
    
    fn extract_chunks(&self, node: &AstNode, source: &str, language: &str) -> Vec<CodeChunk> {
        let mut chunks = Vec::new();
        let mut chunk_counter = 0;
        
        self.extract_chunks_recursive(node, source, language, &mut chunks, &mut chunk_counter);
        chunks
    }
    
    fn extract_chunks_recursive(&self, node: &AstNode, source: &str, language: &str, chunks: &mut Vec<CodeChunk>, counter: &mut usize) {
        // Determine if this node should be a chunk
        let chunk_type = match node.node_type.as_str() {
            "function_item" | "function_declaration" => Some(ChunkType::Function),
            "struct_item" | "class_declaration" => Some(ChunkType::Class),
            "impl_item" | "method_declaration" => Some(ChunkType::Method),
            "block_statement" | "block" => Some(ChunkType::Block),
            "comment" | "line_comment" | "block_comment" => Some(ChunkType::Comment),
            "use_declaration" | "import_statement" | "import_declaration" => Some(ChunkType::Import),
            _ => None,
        };
        
        if let Some(ct) = chunk_type {
            *counter += 1;
            let chunk_id = format!("chunk_{:016x}", counter);
            
            // For optimized AST, we may need to extract text from source using range
            let text = if let Some(ref node_text) = node.text {
                node_text.clone()
            } else if !source.is_empty() {
                // Extract text from source using byte positions
                // This is a fallback for when the AST node doesn't have text
                let start_byte = node.range.start.byte as usize;
                let end_byte = node.range.end.byte as usize;
                
                if start_byte < source.len() && end_byte <= source.len() && start_byte < end_byte {
                    source[start_byte..end_byte].to_string()
                } else {
                    // If byte positions are not set correctly, try to extract by line/column
                    String::new()
                }
            } else {
                String::new()
            };
            
            if !text.is_empty() && text.len() <= 8192 { // Max length from contract
                chunks.push(CodeChunk {
                    chunk_id,
                    content: text,
                    range: node.range.clone(),
                    chunk_type: ct,
                    language: Some(language.to_string()),
                    context: None,
                });
            }
        }
        
        // Recurse into children
        for child in &node.children {
            self.extract_chunks_recursive(child, source, language, chunks, counter);
        }
    }
    
    async fn parse_sql_content(&self, file_path: &Path, content: &str) -> Result<FileAnalysis, ParseError> {
        // Use SQL adapter for parsing
        let symbols = SqlAdapter::parse_sql(content)?;
        let chunks = vec![]; // SQL chunks extraction not implemented yet
        let metadata = self.extract_metadata(content);
        
        // Calculate content hash
        let mut hasher = Sha256::new();
        hasher.update(content.as_bytes());
        let content_hash = format!("{:x}", hasher.finalize());
        
        // Get file size
        let file_meta = tokio::fs::metadata(file_path).await.ok();
        let size_bytes = file_meta.as_ref().map(|m| m.len());
        
        // Create a minimal AST for SQL
        let ast = AstNode {
            node_type: "sql_file".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { 
                    line: content.lines().count() as u32, 
                    column: 0, 
                    byte: content.len() as u32 
                },
            },
            children: Vec::new(),
            properties: None,
            text: None,
        };
        
        Ok(FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language: "sql".to_string(),
            content_hash,
            size_bytes,
            ast,
            metrics: metadata,
            chunks: Some(chunks),
            symbols: Some(symbols),
        })
    }
    
    async fn parse_xml_content(&self, file_path: &Path, content: &str) -> Result<FileAnalysis, ParseError> {
        // Use XML adapter for parsing
        let symbols = XmlAdapter::parse_xml(content)?;
        let chunks = vec![]; // XML chunks extraction not implemented yet
        let metadata = self.extract_metadata(content);
        
        // Calculate content hash
        let mut hasher = Sha256::new();
        hasher.update(content.as_bytes());
        let content_hash = format!("{:x}", hasher.finalize());
        
        // Get file size
        let file_meta = tokio::fs::metadata(file_path).await.ok();
        let size_bytes = file_meta.as_ref().map(|m| m.len());
        
        // Create a minimal AST for XML
        let ast = AstNode {
            node_type: "xml_document".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { 
                    line: content.lines().count() as u32, 
                    column: 0, 
                    byte: content.len() as u32 
                },
            },
            children: Vec::new(),
            properties: None,
            text: None,
        };
        
        Ok(FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language: "xml".to_string(),
            content_hash,
            size_bytes,
            ast,
            metrics: metadata,
            chunks: Some(chunks),
            symbols: Some(symbols),
        })
    }
    
    async fn parse_toml_content(&self, file_path: &Path, content: &str) -> Result<FileAnalysis, ParseError> {
        // Use TOML adapter for parsing
        let symbols = TomlAdapter::parse_toml(content)?;
        let chunks = vec![]; // TOML chunks extraction not implemented yet
        let metadata = self.extract_metadata(content);
        
        // Calculate content hash
        let mut hasher = Sha256::new();
        hasher.update(content.as_bytes());
        let content_hash = format!("{:x}", hasher.finalize());
        
        // Get file size
        let file_meta = tokio::fs::metadata(file_path).await.ok();
        let size_bytes = file_meta.as_ref().map(|m| m.len());
        
        // Create a minimal AST for TOML
        let ast = AstNode {
            node_type: "toml_file".to_string(),
            name: None,
            range: Range {
                start: Position { line: 0, column: 0, byte: 0 },
                end: Position { 
                    line: content.lines().count() as u32, 
                    column: 0, 
                    byte: content.len() as u32 
                },
            },
            children: Vec::new(),
            properties: None,
            text: None,
        };
        
        Ok(FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language: "toml".to_string(),
            content_hash,
            size_bytes,
            ast,
            metrics: metadata,
            chunks: Some(chunks),
            symbols: Some(symbols),
        })
    }

    async fn read_file_streaming(&self, file_path: &Path, file_size: u64) -> Result<String, ParseError> {
        const BUFFER_SIZE: usize = 64 * 1024; // 64KB buffer
        const MAX_FILE_SIZE: u64 = 100 * 1024 * 1024; // 100MB max
        
        if file_size > MAX_FILE_SIZE {
            return Err(ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::FileTooLarge,
                message: format!("File too large: {} bytes (max: {} bytes)", file_size, MAX_FILE_SIZE),
                position: None,
            });
        }
        
        let file = File::open(file_path).await.map_err(|e| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::Other,
            message: format!("Failed to open file: {}", e),
            position: None,
        })?;
        
        let mut reader = BufReader::with_capacity(BUFFER_SIZE, file);
        let mut content = String::with_capacity(file_size as usize);
        
        // Read file in chunks
        let mut buffer = vec![0u8; BUFFER_SIZE];
        loop {
            let n = reader.read(&mut buffer).await.map_err(|e| ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::Other,
                message: format!("Failed to read file: {}", e),
                position: None,
            })?;
            
            if n == 0 {
                break;
            }
            
            // Convert bytes to string, handling UTF-8 errors
            let chunk = std::str::from_utf8(&buffer[..n]).map_err(|e| ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::Other,
                message: format!("Invalid UTF-8 in file: {}", e),
                position: None,
            })?;
            
            content.push_str(chunk);
        }
        
        tracing::info!("Successfully read large file {} ({} bytes) using streaming", 
                     file_path.display(), file_size);
        
        Ok(content)
    }
}

/// Extract symbol name from a line using simple string parsing
fn extract_symbol_name(line: &str, prefix: &str) -> Option<String> {
    let after_prefix = line.strip_prefix(prefix)?.trim();

    // Handle different patterns
    if prefix.contains("fn") || prefix.contains("function") || prefix.contains("def") {
        // Function patterns: "fn name(" or "function name(" or "def name("
        if let Some(paren_pos) = after_prefix.find('(') {
            let name = after_prefix[..paren_pos].trim();
            if !name.is_empty() && name.chars().all(|c| c.is_alphanumeric() || c == '_') {
                return Some(name.to_string());
            }
        }
    } else if prefix.contains("class") || prefix.contains("struct") || prefix.contains("enum") || prefix.contains("trait") {
        // Type patterns: "class Name" or "struct Name"
        let name = after_prefix.split_whitespace().next()?;
        if !name.is_empty() && name.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Some(name.to_string());
        }
    } else if prefix.contains("const") || prefix.contains("let") || prefix.contains("var") {
        // Variable patterns: "const name =" or "let name ="
        if let Some(eq_pos) = after_prefix.find('=') {
            let name = after_prefix[..eq_pos].trim();
            if !name.is_empty() && name.chars().all(|c| c.is_alphanumeric() || c == '_') {
                return Some(name.to_string());
            }
        }
    }

    None
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use tempfile::TempDir;

    fn create_test_file(dir: &TempDir, name: &str, content: &str) -> std::path::PathBuf {
        let file_path = dir.path().join(name);
        fs::write(&file_path, content).unwrap();
        file_path
    }

    #[tokio::test]
    async fn test_new_parser_creation() {
        let parser = TreeSitterParser::new().unwrap();
        assert_eq!(parser.parser_pools.len(), 15); // 15 tree-sitter languages (SQL and XML use adapters, PHP commented out)
        assert!(parser.parser_pools.contains_key("rust"));
        assert!(parser.parser_pools.contains_key("python"));
        assert!(parser.parser_pools.contains_key("javascript"));
        assert!(parser.parser_pools.contains_key("typescript"));
        assert!(parser.parser_pools.contains_key("go"));
        assert!(parser.parser_pools.contains_key("java"));
        assert!(parser.parser_pools.contains_key("c"));
        assert!(parser.parser_pools.contains_key("cpp"));
        assert!(parser.parser_pools.contains_key("html"));
        assert!(parser.parser_pools.contains_key("css"));
        assert!(parser.parser_pools.contains_key("json"));
        assert!(parser.parser_pools.contains_key("yaml"));
        // assert!(parser.parser_pools.contains_key("php")); // PHP commented out due to version conflicts
        assert!(parser.parser_pools.contains_key("ruby"));
        assert!(parser.parser_pools.contains_key("bash"));
        assert!(parser.parser_pools.contains_key("markdown"));
    }

    #[tokio::test]
    async fn test_parser_pool_functionality() {
        let config = ParserPoolConfig {
            max_parsers_per_language: 2,
            initial_parsers_per_language: 1,
        };
        let parser = TreeSitterParser::new_with_config(config).unwrap();

        // Test that we can get a parser from the pool
        let rust_pool = parser.parser_pools.get("rust").unwrap();
        let parser1 = rust_pool.get_parser().await.unwrap();

        // Return it to the pool
        rust_pool.return_parser(parser1);

        // Test concurrent access
        let parser1 = rust_pool.get_parser().await.unwrap();
        let parser2 = rust_pool.get_parser().await.unwrap();

        // Both should be valid parsers
        rust_pool.return_parser(parser1);
        rust_pool.return_parser(parser2);
    }

    #[tokio::test]
    async fn test_concurrent_parsing_performance() {
        use std::time::Instant;
        use tokio::task::JoinSet;

        let config = ParserPoolConfig {
            max_parsers_per_language: 4,
            initial_parsers_per_language: 1,
        };
        let parser = Arc::new(TreeSitterParser::new_with_config(config).unwrap());

        let rust_code = r#"
fn main() {
    println!("Hello, world!");
}

struct MyStruct {
    field: String,
}

impl MyStruct {
    fn new() -> Self {
        Self { field: String::new() }
    }
}
"#;

        let temp_dir = tempfile::TempDir::new().unwrap();
        let file_path = temp_dir.path().join("test.rs");
        std::fs::write(&file_path, rust_code).unwrap();

        // Test concurrent parsing with multiple tasks
        let start = Instant::now();
        let mut join_set = JoinSet::new();

        for i in 0..10 {
            let parser_clone = parser.clone();
            let file_path_clone = file_path.clone();
            join_set.spawn(async move {
                let result = parser_clone.parse_file(&file_path_clone).await;
                (i, result.is_ok())
            });
        }

        let mut results = Vec::new();
        while let Some(result) = join_set.join_next().await {
            results.push(result.unwrap());
        }

        let duration = start.elapsed();

        // All parsing should succeed
        assert_eq!(results.len(), 10);
        for (_, success) in results {
            assert!(success);
        }

        // Should complete reasonably quickly (less than 1 second for 10 concurrent parses)
        assert!(duration.as_secs() < 1);

        println!("Concurrent parsing of 10 files took: {:?}", duration);
    }

    #[tokio::test]
    async fn test_detect_language() {
        let parser = TreeSitterParser::new().unwrap();
        
        assert_eq!(parser.detect_language(Path::new("test.rs")).unwrap(), "rust");
        assert_eq!(parser.detect_language(Path::new("test.py")).unwrap(), "python");
        assert_eq!(parser.detect_language(Path::new("test.js")).unwrap(), "javascript");
        assert_eq!(parser.detect_language(Path::new("test.ts")).unwrap(), "typescript");
        assert_eq!(parser.detect_language(Path::new("test.go")).unwrap(), "go");
        assert_eq!(parser.detect_language(Path::new("test.java")).unwrap(), "java");
        assert_eq!(parser.detect_language(Path::new("test.c")).unwrap(), "c");
        assert_eq!(parser.detect_language(Path::new("test.cpp")).unwrap(), "cpp");
        
        // Test unsupported extension
        let result = parser.detect_language(Path::new("test.xyz"));
        assert!(result.is_err());
        if let Err(e) = result {
            assert_eq!(e.error_type, ParseErrorType::UnsupportedLanguage);
        }
    }

    #[tokio::test]
    async fn test_parse_rust_file() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let rust_code = r#"
fn main() {
    println!("Hello, world!");
}

struct MyStruct {
    field: String,
}

impl MyStruct {
    fn new() -> Self {
        Self { field: String::new() }
    }
}
"#;
        
        let file_path = create_test_file(&temp_dir, "test.rs", rust_code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        assert_eq!(result.language, "rust");
        assert!(!result.content_hash.is_empty());
        assert!(result.size_bytes.is_some());
        assert!(result.symbols.is_some());
        
        let symbols = result.symbols.unwrap();
        assert!(symbols.iter().any(|s| s.name == "main" && s.symbol_type == SymbolType::Function));
        assert!(symbols.iter().any(|s| s.name == "MyStruct" && s.symbol_type == SymbolType::Class));
        
        assert!(result.chunks.is_some());
        let chunks = result.chunks.unwrap();
        assert!(!chunks.is_empty());
    }

    #[tokio::test]
    async fn test_parse_python_file() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let python_code = r#"
def hello_world():
    print("Hello, world!")

class MyClass:
    def __init__(self):
        self.value = 42
    
    def get_value(self):
        return self.value

hello_world()
"#;
        
        let file_path = create_test_file(&temp_dir, "test.py", python_code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        assert_eq!(result.language, "python");
        assert!(result.metrics.lines_of_code > 0);
        assert!(result.metrics.total_lines.is_some());
    }

    #[tokio::test]
    async fn test_parse_javascript_file() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let js_code = r#"
function greet(name) {
    console.log(`Hello, ${name}!`);
}

const myObject = {
    property: "value",
    method: function() {
        return this.property;
    }
};

greet("World");
"#;
        
        let file_path = create_test_file(&temp_dir, "test.js", js_code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        assert_eq!(result.language, "javascript");
        assert!(!result.ast.children.is_empty());
    }

    #[tokio::test]
    async fn test_parse_invalid_file() {
        let parser = TreeSitterParser::new().unwrap();
        let result = parser.parse_file(Path::new("/nonexistent/file.rs")).await;
        
        assert!(result.is_err());
        if let Err(e) = result {
            assert_eq!(e.error_type, ParseErrorType::Other);
        }
    }

    #[tokio::test]
    async fn test_extract_metadata() {
        let parser = TreeSitterParser::new().unwrap();
        
        let content = r#"// This is a comment
fn main() {
    // Another comment
    println!("Hello");
    println!("World");
}
// End comment"#;
        
        let metrics = parser.extract_metadata(content);
        assert_eq!(metrics.lines_of_code, 4); // 4 non-comment lines
        assert_eq!(metrics.total_lines, Some(7));
        assert!(metrics.comment_ratio.is_some());
    }

    #[tokio::test]
    async fn test_convert_position() {
        let parser = TreeSitterParser::new().unwrap();
        let point = tree_sitter::Point { row: 10, column: 20 };
        let position = parser.convert_position(point);
        
        assert_eq!(position.line, 10);
        assert_eq!(position.column, 20);
        assert_eq!(position.byte, 0);
    }

    #[tokio::test]
    async fn test_build_ast() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let code = "fn test() { }";
        let file_path = create_test_file(&temp_dir, "test.rs", code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        assert!(!result.ast.node_type.is_empty());
        assert!(result.ast.range.start.line == 0);
    }

    #[tokio::test]
    async fn test_extract_chunks() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let code = r#"
fn function1() {
    // Function body
}

struct MyStruct {
    field: i32
}

impl MyStruct {
    fn method1(&self) -> i32 {
        self.field
    }
}
"#;
        
        let file_path = create_test_file(&temp_dir, "test.rs", code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        let chunks = result.chunks.unwrap();
        assert!(chunks.iter().any(|c| c.chunk_type == ChunkType::Function));
        assert!(chunks.iter().any(|c| c.chunk_type == ChunkType::Class));
        assert!(chunks.iter().any(|c| c.chunk_type == ChunkType::Method));
    }

    #[tokio::test]
    async fn test_multiple_language_parsing() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        // Test Go
        let go_code = r#"
package main

func main() {
    fmt.Println("Hello, Go!")
}
"#;
        let go_file = create_test_file(&temp_dir, "test.go", go_code);
        let go_result = parser.parse_file(&go_file).await.unwrap();
        assert_eq!(go_result.language, "go");
        
        // Test Java
        let java_code = r#"
public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, Java!");
    }
}
"#;
        let java_file = create_test_file(&temp_dir, "HelloWorld.java", java_code);
        let java_result = parser.parse_file(&java_file).await.unwrap();
        assert_eq!(java_result.language, "java");
        
        // Test C
        let c_code = r#"
#include <stdio.h>

int main() {
    printf("Hello, C!\n");
    return 0;
}
"#;
        let c_file = create_test_file(&temp_dir, "test.c", c_code);
        let c_result = parser.parse_file(&c_file).await.unwrap();
        assert_eq!(c_result.language, "c");
    }
    
    #[tokio::test]
    async fn test_large_file_streaming() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        // Create a large file (>10MB)
        let mut large_content = String::new();
        large_content.push_str("fn main() {\n");
        // Add enough content to exceed 10MB
        for i in 0..500_000 {
            large_content.push_str(&format!("    println!(\"Line {}\");\n", i));
        }
        large_content.push_str("}\n");
        
        // Verify the content is indeed large
        assert!(large_content.len() > 10 * 1024 * 1024);
        
        let large_file = create_test_file(&temp_dir, "large.rs", &large_content);
        
        // This should use streaming
        let result = parser.parse_file(&large_file).await;
        assert!(result.is_ok());
        
        let analysis = result.unwrap();
        assert_eq!(analysis.language, "rust");
        assert!(analysis.size_bytes.unwrap() > 10 * 1024 * 1024);
    }
    
    #[tokio::test]
    async fn test_file_too_large_error() {
        let parser = TreeSitterParser::new().unwrap();
        
        // Test the streaming method directly with a file that would be too large
        let path = Path::new("/fake/path/too_large.rs");
        let result = parser.read_file_streaming(path, 101 * 1024 * 1024).await;
        
        assert!(result.is_err());
        if let Err(e) = result {
            assert_eq!(e.error_type, ParseErrorType::FileTooLarge);
            assert!(e.message.contains("File too large"));
        }
    }
    
    #[tokio::test]
    async fn test_parse_sql_file() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let sql_code = r#"
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE
);

CREATE VIEW active_users AS
SELECT * FROM users WHERE active = true;

CREATE INDEX idx_users_email ON users(email);
"#;
        
        let file_path = create_test_file(&temp_dir, "test.sql", sql_code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        assert_eq!(result.language, "sql");
        assert!(!result.content_hash.is_empty());
        assert!(result.symbols.is_some());
        
        let symbols = result.symbols.unwrap();
        assert!(symbols.iter().any(|s| s.name == "users" && s.symbol_type == SymbolType::Class));
        assert!(symbols.iter().any(|s| s.name == "active_users" && s.symbol_type == SymbolType::Class));
        
        // SQL adapter doesn't implement chunks yet
        assert!(result.chunks.is_some());
        let chunks = result.chunks.unwrap();
        assert_eq!(chunks.len(), 0); // SQL chunks not implemented in adapter
    }
    
    #[tokio::test]
    async fn test_parse_xml_file() {
        let parser = TreeSitterParser::new().unwrap();
        let temp_dir = TempDir::new().unwrap();
        
        let xml_code = r#"<?xml version="1.0" encoding="UTF-8"?>
<project>
    <name>Test Project</name>
    <version>1.0.0</version>
    <dependencies>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>example-lib</artifactId>
            <version>2.0.0</version>
        </dependency>
    </dependencies>
    <build>
        <sourceDirectory>src/main/java</sourceDirectory>
    </build>
</project>"#;
        
        let file_path = create_test_file(&temp_dir, "pom.xml", xml_code);
        let result = parser.parse_file(&file_path).await.unwrap();
        
        assert_eq!(result.language, "xml");
        assert!(!result.content_hash.is_empty());
        assert!(result.symbols.is_some());
        
        let symbols = result.symbols.unwrap();
        assert!(symbols.iter().any(|s| s.name == "project"));
        assert!(symbols.iter().any(|s| s.name == "dependencies"));
        
        assert!(result.chunks.is_some());
    }
    
    #[tokio::test]
    async fn test_sql_parsing_with_adapter() {
        let parser = TreeSitterParser::new().unwrap();
        let dir = TempDir::new().unwrap();
        
        let sql_code = r#"
        CREATE TABLE products (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            price DECIMAL(10, 2)
        );
        
        CREATE VIEW active_products AS
        SELECT * FROM products WHERE active = true;
        
        CREATE FUNCTION calculate_tax(price DECIMAL)
        RETURNS DECIMAL AS $$
        BEGIN
            RETURN price * 0.08;
        END;
        $$ LANGUAGE plpgsql;
        "#;
        
        let file_path = create_test_file(&dir, "test.sql", sql_code);
        let result = parser.parse_file(&file_path).await;
        assert!(result.is_ok());
        
        let analysis = result.unwrap();
        assert_eq!(analysis.language, "sql");
        
        let symbols = analysis.symbols.unwrap();
        println!("SQL symbols found: {:?}", symbols);
        assert!(symbols.len() >= 3); // Table, view, and function
        
        let symbol_names: Vec<&str> = symbols.iter().map(|s| s.name.as_str()).collect();
        assert!(symbol_names.contains(&"products"));
        assert!(symbol_names.contains(&"active_products"));
        assert!(symbol_names.contains(&"calculate_tax"));
    }
    
    #[tokio::test]
    async fn test_toml_parsing_with_adapter() {
        let parser = TreeSitterParser::new().unwrap();
        let dir = TempDir::new().unwrap();
        
        let toml_code = r#"
        [package]
        name = "my-awesome-app"
        version = "1.0.0"
        authors = ["John Doe <<EMAIL>>"]
        
        [dependencies]
        serde = { version = "1.0", features = ["derive"] }
        tokio = { version = "1.0", features = ["full"] }
        
        [dev-dependencies]
        criterion = "0.5"
        
        [profile.release]
        opt-level = 3
        lto = true
        "#;
        
        let file_path = create_test_file(&dir, "Cargo.toml", toml_code);
        let result = parser.parse_file(&file_path).await;
        assert!(result.is_ok());
        
        let analysis = result.unwrap();
        assert_eq!(analysis.language, "toml");
        
        let symbols = analysis.symbols.unwrap();
        println!("TOML symbols found: {:?}", symbols);
        assert!(symbols.len() > 5); // Multiple sections and keys
        
        let symbol_names: Vec<&str> = symbols.iter().map(|s| s.name.as_str()).collect();
        assert!(symbol_names.contains(&"package"));
        assert!(symbol_names.contains(&"package.name"));
        assert!(symbol_names.contains(&"dependencies"));
        assert!(symbol_names.contains(&"profile.release"));
    }
}
