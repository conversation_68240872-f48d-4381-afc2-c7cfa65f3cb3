// Enhancement for gemini-embedding-001 task type support
// This would improve embedding quality for code-specific use cases

#[derive(Debug, Serialize)]
struct EmbeddingRequest {
    instances: Vec<EmbeddingInstance>,
    parameters: Option<EmbeddingParameters>,
}

#[derive(Debug, Serialize)]
struct EmbeddingParameters {
    task_type: Option<String>,
    output_dimensionality: Option<i32>,
}

// Task types for gemini-embedding-001
pub enum TaskType {
    RetrievalQuery,        // For search queries
    RetrievalDocument,     // For documents to be searched
    SemanticSimilarity,    // For similarity comparisons
    Classification,        // For classification tasks
    Clustering,           // For clustering tasks
    CodeRetrievalQuery,   // For code search queries (NEW in 005!)
}

impl TaskType {
    fn as_str(&self) -> &'static str {
        match self {
            TaskType::RetrievalQuery => "RETRIEVAL_QUERY",
            TaskType::RetrievalDocument => "RETRIEVAL_DOCUMENT",
            TaskType::SemanticSimilarity => "SEMANTIC_SIMILARITY",
            TaskType::Classification => "CLASSIFICATION",
            TaskType::Clustering => "CLUSTERING",
            TaskType::CodeRetrievalQuery => "CODE_RETRIEVAL_QUERY",
        }
    }
}

// Example usage in call_vertex_ai:
async fn call_vertex_ai_enhanced(&self, instances: &[EmbeddingInstance], task_type: TaskType) -> Result<Vec<CodeEmbedding>> {
    let request = EmbeddingRequest {
        instances: instances.to_vec(),
        parameters: Some(EmbeddingParameters {
            task_type: Some(task_type.as_str().to_string()),
            output_dimensionality: None, // Use default 768
        }),
    };
    
    // ... rest of the implementation
}