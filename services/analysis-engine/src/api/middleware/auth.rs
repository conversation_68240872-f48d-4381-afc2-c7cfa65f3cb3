use axum::{
    body::Body,
    extract::State,
    http::Request,
    middleware::Next,
    response::{IntoResponse, Response},
};
use jsonwebtoken::{decode, decode_header, Algorithm, DecodingKey, Validation};
use serde::{Deserialize, Serialize};
use sha2::{Sha256, Digest};
use rand::{Rng, thread_rng};
use base64::{Engine as _, engine::general_purpose};
use crate::api::AppState;
use crate::api::errors::{ErrorResponse, ErrorType, ErrorContext};
use crate::storage::SpannerOperations;
use crate::audit::{AuditLogger, AuditEventBuilder, AuditAction, AuditOutcome, AuditSeverity};
use google_cloud_spanner::statement::Statement;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use dashmap::DashMap;
use std::collections::HashMap;
use uuid::Uuid;

#[derive(Debug, Serialize)]
pub struct AuthError {
    pub error: String,
    pub error_code: String,
}

// Helper function to create standardized authentication error responses
fn create_auth_error_response(
    error_code: &str,
    message: String,
    user_message: Option<String>,
    request: &Request<Body>,
) -> Response {
    let correlation_id = Uuid::new_v4().to_string();

    // Extract context information
    let context = ErrorContext {
        request_id: Some(correlation_id.clone()),
        user_id: None, // Not available during auth failure
        organization_id: None,
        resource_id: None,
        operation: Some(format!("{} {}", request.method(), request.uri().path())),
        parameters: None, // Don't include sensitive auth parameters
    };

    let mut error = ErrorResponse::new(ErrorType::Authentication, message);
    error.error_code = Some(error_code.to_string());
    error.user_message = user_message;
    error.correlation_id = Some(correlation_id);
    error.context = Some(context);
    error.retryable = false; // Auth errors are generally not retryable

    // Add security suggestions based on error type
    match error_code {
        "MISSING_AUTHENTICATION" => {
            error.suggestions.push(crate::api::errors::ErrorSuggestion {
                action: "provide_authentication".to_string(),
                description: "Include either an API key in the 'x-api-key' header or a JWT token in the 'Authorization: Bearer <token>' header".to_string(),
                url: Some("https://docs.ccl.dev/authentication".to_string()),
                priority: "high".to_string(),
            });
        }
        "INVALID_API_KEY" => {
            error.suggestions.push(crate::api::errors::ErrorSuggestion {
                action: "check_api_key".to_string(),
                description: "Verify your API key is correct and has not expired".to_string(),
                url: Some("https://docs.ccl.dev/api-keys".to_string()),
                priority: "high".to_string(),
            });
        }
        "INVALID_JWT_TOKEN" => {
            error.suggestions.push(crate::api::errors::ErrorSuggestion {
                action: "refresh_token".to_string(),
                description: "Your JWT token may be expired or invalid. Please obtain a new token".to_string(),
                url: Some("https://docs.ccl.dev/jwt-tokens".to_string()),
                priority: "high".to_string(),
            });
        }
        _ => {}
    }

    let mut response = error.into_response();

    // Add security headers
    let headers = response.headers_mut();
    if let Ok(value) = "nosniff".parse() {
        headers.insert("X-Content-Type-Options", value);
    }
    if let Ok(value) = "DENY".parse() {
        headers.insert("X-Frame-Options", value);
    }
    if let Ok(value) = "1; mode=block".parse() {
        headers.insert("X-XSS-Protection", value);
    }
    if let Ok(value) = "no-store, no-cache, must-revalidate".parse() {
        headers.insert("Cache-Control", value);
    }
    if let Ok(value) = "no-cache".parse() {
        headers.insert("Pragma", value);
    }

    response
}

// Helper function to create rate limit error response
fn create_rate_limit_error_response(
    remaining: i64,
    reset_time: u64,
    rate_limit: i64,
    request: &Request<Body>,
) -> Response {
    let correlation_id = Uuid::new_v4().to_string();

    let context = ErrorContext {
        request_id: Some(correlation_id.clone()),
        user_id: None,
        organization_id: None,
        resource_id: None,
        operation: Some(format!("{} {}", request.method(), request.uri().path())),
        parameters: None,
    };

    let mut error = ErrorResponse::new(
        ErrorType::RateLimit,
        "Rate limit exceeded".to_string(),
    );
    error.error_code = Some("RATE_LIMIT_EXCEEDED".to_string());
    error.user_message = Some("You have exceeded the rate limit. Please wait before making more requests.".to_string());
    error.correlation_id = Some(correlation_id);
    error.context = Some(context);
    error.retryable = true;

    // Calculate retry after seconds from timestamp
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or(Duration::from_secs(0))
        .as_secs();
    let retry_after = if reset_time > now {
        (reset_time - now) as u32
    } else {
        60 // Default to 60 seconds if calculation fails
    };
    error.retry_after_seconds = Some(retry_after);

    error.suggestions.push(crate::api::errors::ErrorSuggestion {
        action: "wait_and_retry".to_string(),
        description: format!("Wait {} seconds before making another request", retry_after),
        url: Some("https://docs.ccl.dev/rate-limits".to_string()),
        priority: "medium".to_string(),
    });

    let mut response = error.into_response();

    // Add rate limit headers
    let headers = response.headers_mut();
    if let Ok(limit_header) = rate_limit.to_string().parse() {
        headers.insert("X-RateLimit-Limit", limit_header);
    }
    if let Ok(remaining_header) = remaining.to_string().parse() {
        headers.insert("X-RateLimit-Remaining", remaining_header);
    }
    if let Ok(reset_header) = reset_time.to_string().parse() {
        headers.insert("X-RateLimit-Reset", reset_header);
    }

    // Add security headers
    if let Ok(value) = "nosniff".parse() {
        headers.insert("X-Content-Type-Options", value);
    }
    if let Ok(value) = "DENY".parse() {
        headers.insert("X-Frame-Options", value);
    }
    if let Ok(value) = "1; mode=block".parse() {
        headers.insert("X-XSS-Protection", value);
    }
    if let Ok(value) = "no-store, no-cache, must-revalidate".parse() {
        headers.insert("Cache-Control", value);
    }
    if let Ok(value) = "no-cache".parse() {
        headers.insert("Pragma", value);
    }

    response
}

#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    sub: String,  // Subject (user ID)
    exp: u64,     // Expiration time
    iat: u64,     // Issued at
    aud: String,  // Audience
    iss: String,  // Issuer
    nbf: Option<u64>, // Not before
    jti: Option<String>, // JWT ID for revocation tracking
    scope: Option<String>, // Token scope/permissions
    session_id: Option<String>, // Session identifier for revocation
    device_id: Option<String>, // Device fingerprint for binding
}

#[derive(Clone)]
struct JwtKey {
    kid: String,           // Key ID
    key: DecodingKey,      // The actual key for verification
    algorithm: Algorithm,  // Algorithm used with this key
    expires_at: Option<SystemTime>, // When this key expires
}

impl std::fmt::Debug for JwtKey {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("JwtKey")
            .field("kid", &self.kid)
            .field("algorithm", &self.algorithm)
            .field("expires_at", &self.expires_at)
            .field("key", &"[REDACTED]") // Don't expose the actual key in debug output
            .finish()
    }
}

#[derive(Debug)]
struct KeyManager {
    keys: HashMap<String, JwtKey>,
    default_key_id: Option<String>,
}

impl KeyManager {
    fn new() -> Self {
        Self {
            keys: HashMap::new(),
            default_key_id: None,
        }
    }

    fn add_key(&mut self, kid: String, key: DecodingKey, algorithm: Algorithm, expires_at: Option<SystemTime>) {
        let jwt_key = JwtKey {
            kid: kid.clone(),
            key,
            algorithm,
            expires_at,
        };
        self.keys.insert(kid.clone(), jwt_key);

        // Set as default if it's the first key
        if self.default_key_id.is_none() {
            self.default_key_id = Some(kid);
        }
    }

    fn get_key(&self, kid: &str) -> Option<&JwtKey> {
        self.keys.get(kid)
    }

    fn get_default_key(&self) -> Option<&JwtKey> {
        self.default_key_id.as_ref().and_then(|kid| self.keys.get(kid))
    }

    fn cleanup_expired_keys(&mut self) {
        let now = SystemTime::now();
        self.keys.retain(|_, key| {
            key.expires_at.map_or(true, |expires| expires > now)
        });
    }
}

#[derive(Debug)]
#[allow(dead_code)]
struct ApiKeyInfo {
    user_id: String,
    rate_limit: i64,
    expires_at: Option<SystemTime>,
}

#[derive(Debug)]
struct UserInfo {
    rate_limit: i64,
    #[allow(dead_code)]
    subscription_tier: String,
    #[allow(dead_code)]
    is_active: bool,
}

// Global key manager for JWT key rotation
lazy_static::lazy_static! {
    static ref JWT_KEY_MANAGER: Arc<std::sync::RwLock<KeyManager>> = {
        let mut manager = KeyManager::new();

        // Initialize with default key from environment
        if let Ok(jwt_secret) = std::env::var("JWT_SECRET") {
            let key = DecodingKey::from_secret(jwt_secret.as_bytes());
            manager.add_key("default".to_string(), key, Algorithm::HS256, None);
        }

        // Load additional keys from environment if available
        load_additional_keys(&mut manager);

        Arc::new(std::sync::RwLock::new(manager))
    };
}

fn load_additional_keys(manager: &mut KeyManager) {
    // Load keys from environment variables with pattern JWT_KEY_{KID}
    for (key, value) in std::env::vars() {
        if key.starts_with("JWT_KEY_") {
            if let Some(kid_suffix) = key.strip_prefix("JWT_KEY_") {
                let kid = kid_suffix.to_lowercase();
                let decoding_key = DecodingKey::from_secret(value.as_bytes());
                tracing::info!("Loaded JWT key with ID: {}", kid);
                manager.add_key(kid, decoding_key, Algorithm::HS256, None);
            }
        }
    }

    // TODO: In production, load keys from Google Secret Manager or similar KMS
    // This would involve fetching public keys from a JWKS endpoint
}

/// Refresh JWT keys from key management service
/// This should be called periodically to support key rotation
pub async fn refresh_jwt_keys() -> Result<(), String> {
    let mut manager = JWT_KEY_MANAGER.write()
        .map_err(|_| "Failed to acquire key manager write lock".to_string())?;

    // Clean up expired keys first
    manager.cleanup_expired_keys();

    // In production, this would fetch keys from Google Secret Manager or JWKS endpoint
    // For now, we'll just reload from environment variables
    load_additional_keys(&mut manager);

    tracing::info!("JWT keys refreshed successfully");
    Ok(())
}

/// Get current key information for debugging/monitoring
pub fn get_jwt_key_info() -> Result<Vec<String>, String> {
    let manager = JWT_KEY_MANAGER.read()
        .map_err(|_| "Failed to acquire key manager lock".to_string())?;

    let key_ids: Vec<String> = manager.keys.keys().cloned().collect();
    Ok(key_ids)
}

/// Revoke a specific JWT token by its JTI (JWT ID)
pub fn revoke_token(jti: &str) -> Result<(), String> {
    if jti.is_empty() {
        return Err("JWT ID cannot be empty".to_string());
    }

    REVOKED_TOKENS.insert(jti.to_string(), SystemTime::now());
    tracing::info!("Token revoked: {}", jti);
    Ok(())
}

/// Revoke all tokens for a specific session
pub fn revoke_session(session_id: &str) -> Result<(), String> {
    if session_id.is_empty() {
        return Err("Session ID cannot be empty".to_string());
    }

    REVOKED_SESSIONS.insert(session_id.to_string(), SystemTime::now());
    tracing::info!("Session revoked: {}", session_id);
    Ok(())
}

/// Check if a token is revoked by JTI
fn is_token_revoked(jti: &str) -> bool {
    REVOKED_TOKENS.contains_key(jti)
}

/// Check if a session is revoked
fn is_session_revoked(session_id: &str) -> bool {
    REVOKED_SESSIONS.contains_key(session_id)
}

/// Clean up expired revocation entries (should be called periodically)
pub fn cleanup_revoked_tokens() {
    let cutoff = SystemTime::now() - Duration::from_secs(30 * 24 * 60 * 60); // 30 days

    REVOKED_TOKENS.retain(|_, &mut revoked_at| revoked_at > cutoff);
    REVOKED_SESSIONS.retain(|_, &mut revoked_at| revoked_at > cutoff);

    tracing::debug!("Cleaned up expired revocation entries");
}

/// Get revocation statistics for monitoring
pub fn get_revocation_stats() -> (usize, usize) {
    (REVOKED_TOKENS.len(), REVOKED_SESSIONS.len())
}

/// Bulk revoke tokens by user ID (emergency function)
pub async fn revoke_user_tokens(user_id: &str, state: &AppState) -> Result<usize, String> {
    // In a production system, this would query the database for all active tokens
    // for the user and revoke them. For now, we'll just log the action.

    tracing::warn!("Emergency token revocation requested for user: {}", user_id);

    // In production, implement database query to find and revoke all user tokens
    // This is a placeholder for the actual implementation
    if let Some(_spanner) = &state.spanner {
        let _statement = Statement::new(
            "SELECT jti FROM user_tokens WHERE user_id = @user_id AND is_active = true"
        );
        // Implementation would go here
        tracing::info!("Would revoke all tokens for user: {}", user_id);
    }

    Ok(0) // Return count of revoked tokens
}

/// Generate a cryptographically secure salt for API key hashing
fn generate_salt() -> String {
    let mut rng = thread_rng();
    let salt_bytes: [u8; 32] = rng.gen();
    general_purpose::STANDARD.encode(salt_bytes)
}

/// Hash an API key with salt using PBKDF2-like approach
fn hash_api_key_with_salt(api_key: &str, salt: &str) -> Result<String, String> {
    // Decode the salt from base64
    let salt_bytes = general_purpose::STANDARD.decode(salt)
        .map_err(|e| format!("Failed to decode salt: {}", e))?;

    // Use PBKDF2-like approach with multiple iterations for security
    let iterations = 100_000;
    let mut hash = api_key.as_bytes().to_vec();

    for _ in 0..iterations {
        let mut hasher = Sha256::new();
        hasher.update(&hash);
        hasher.update(&salt_bytes);
        hash = hasher.finalize().to_vec();
    }

    Ok(general_purpose::STANDARD.encode(hash))
}

/// Verify an API key against a stored hash and salt
fn verify_api_key(api_key: &str, stored_hash: &str, salt: &str) -> Result<bool, String> {
    let computed_hash = hash_api_key_with_salt(api_key, salt)?;
    Ok(computed_hash == stored_hash)
}

/// Generate a new API key with secure hashing
pub fn generate_api_key() -> Result<(String, String, String), String> {
    // Generate a random API key
    let mut rng = thread_rng();
    let key_bytes: [u8; 32] = rng.gen();
    let api_key = format!("ak_{}", general_purpose::STANDARD.encode(key_bytes));

    // Generate salt and hash
    let salt = generate_salt();
    let hash = hash_api_key_with_salt(&api_key, &salt)?;

    Ok((api_key, hash, salt))
}

/// Extract client IP address from request headers
fn extract_client_ip(request: &Request<Body>) -> Option<String> {
    // Check X-Forwarded-For header first (for load balancers/proxies)
    if let Some(forwarded_for) = request.headers().get("x-forwarded-for") {
        if let Ok(forwarded_str) = forwarded_for.to_str() {
            // Take the first IP in the chain
            if let Some(first_ip) = forwarded_str.split(',').next() {
                return Some(first_ip.trim().to_string());
            }
        }
    }

    // Check X-Real-IP header
    if let Some(real_ip) = request.headers().get("x-real-ip") {
        if let Ok(ip_str) = real_ip.to_str() {
            return Some(ip_str.to_string());
        }
    }

    // Check CF-Connecting-IP header (Cloudflare)
    if let Some(cf_ip) = request.headers().get("cf-connecting-ip") {
        if let Ok(ip_str) = cf_ip.to_str() {
            return Some(ip_str.to_string());
        }
    }

    // Fallback to connection info if available
    // Note: This would require ConnectInfo extension to be available
    None
}

/// Extract user agent from request headers
fn extract_user_agent(request: &Request<Body>) -> Option<String> {
    request.headers()
        .get("user-agent")
        .and_then(|ua| ua.to_str().ok())
        .map(|s| s.to_string())
}

/// Generate device fingerprint from request headers for token binding
fn generate_device_fingerprint(request: &Request<Body>) -> String {
    let mut hasher = Sha256::new();

    // Include User-Agent
    if let Some(ua) = extract_user_agent(request) {
        hasher.update(ua.as_bytes());
    }

    // Include Accept-Language
    if let Some(lang) = request.headers().get("accept-language")
        .and_then(|h| h.to_str().ok()) {
        hasher.update(lang.as_bytes());
    }

    // Include Accept-Encoding
    if let Some(encoding) = request.headers().get("accept-encoding")
        .and_then(|h| h.to_str().ok()) {
        hasher.update(encoding.as_bytes());
    }

    // Include X-Forwarded-For (first IP only for consistency)
    if let Some(forwarded) = request.headers().get("x-forwarded-for")
        .and_then(|h| h.to_str().ok()) {
        if let Some(first_ip) = forwarded.split(',').next() {
            hasher.update(first_ip.trim().as_bytes());
        }
    }

    // Include custom device ID header if present
    if let Some(device_id) = request.headers().get("x-device-id")
        .and_then(|h| h.to_str().ok()) {
        hasher.update(device_id.as_bytes());
    }

    let result = hasher.finalize();
    format!("{:x}", result)
}

/// Validate device fingerprint against token claims
fn validate_device_binding(request: &Request<Body>, claims: &Claims) -> Result<(), String> {
    if let Some(token_device_id) = &claims.device_id {
        let request_fingerprint = generate_device_fingerprint(request);

        if token_device_id != &request_fingerprint {
            return Err("Token device binding validation failed".to_string());
        }
    }

    Ok(())
}

pub async fn auth_middleware(
    State(state): State<AppState>,
    mut request: Request<Body>,
    next: Next,
) -> Result<Response, impl IntoResponse> {
    // Allow health check endpoints without auth
    let path = request.uri().path();
    if path == "/health" || path == "/ready" || path == "/metrics" {
        return Ok(next.run(request).await);
    }

    // Extract headers from request
    let headers = request.headers();

    // Check for API key in headers
    let api_key = headers
        .get("x-api-key")
        .and_then(|v| v.to_str().ok());

    // Check for Bearer token
    let auth_header = headers
        .get("authorization")
        .and_then(|v| v.to_str().ok());

    let auth_result = match (api_key, auth_header) {
        (Some(key), _) => match &state.spanner {
            Some(spanner) => validate_api_key(key, spanner).await,
            None => {
                tracing::warn!("API key validation skipped - database unavailable");
                // In memory mode, use a default user with limited rate limit
                Ok(("memory-user".to_string(), 100))
            }
        },
        (_, Some(header)) if header.starts_with("Bearer ") => {
            let token = &header[7..];
            validate_jwt_token_with_request(token, &state, Some(&request)).await
        }
        _ => Err("No authentication provided".to_string()),
    };

    match auth_result {
        Ok((user_id, rate_limit)) => {
            // Check rate limiting
            let rate_limit_result = check_rate_limit(&state, &user_id, rate_limit).await;
            
            match rate_limit_result {
                Ok((allowed, remaining, reset_time)) => {
                    if !allowed {
                        return Err(create_rate_limit_error_response(
                            remaining,
                            reset_time,
                            rate_limit,
                            &request,
                        ));
                    }
                    
                    // Add user ID to request extensions for downstream handlers
                    request.extensions_mut().insert(user_id.clone());
                    
                    // Log successful authentication
                    tracing::debug!("Authentication successful for user: {}", user_id);

                    // Audit log successful authentication
                    let audit_logger = AuditLogger::new(state.spanner.clone());
                    let client_ip = extract_client_ip(&request);
                    let user_agent = extract_user_agent(&request);

                    let audit_event = AuditEventBuilder::new(AuditAction::LoginSuccess)
                        .user_id(user_id.clone())
                        .ip_address(client_ip.unwrap_or_else(|| "unknown".to_string()))
                        .user_agent(user_agent.unwrap_or_else(|| "unknown".to_string()))
                        .outcome(AuditOutcome::Success)
                        .severity(AuditSeverity::Info)
                        .metadata(serde_json::json!({
                            "auth_method": if request.headers().contains_key("authorization") { "jwt" } else { "api_key" },
                            "rate_limit": rate_limit
                        }))
                        .build();

                    if let Err(e) = audit_logger.log_event(audit_event).await {
                        tracing::error!("Failed to log audit event: {}", e);
                    }

                    // Add rate limit headers to successful response
                    let mut response = next.run(request).await;
                    let headers = response.headers_mut();
                    if let Ok(limit_header) = rate_limit.to_string().parse() {
                        headers.insert("X-RateLimit-Limit", limit_header);
                    }
                    if let Ok(remaining_header) = remaining.to_string().parse() {
                        headers.insert("X-RateLimit-Remaining", remaining_header);
                    }
                    if let Ok(reset_header) = reset_time.to_string().parse() {
                        headers.insert("X-RateLimit-Reset", reset_header);
                    }
                    
                    Ok(response)
                }
                Err(e) => {
                    tracing::error!("Rate limiting check failed: {}", e);
                    // If rate limiting fails, allow the request but log the error
                    request.extensions_mut().insert(user_id);
                    Ok(next.run(request).await)
                }
            }
        }
        Err(error) => {
            // Log authentication failure
            tracing::warn!("Authentication failed: {}", error);

            // Audit log authentication failure
            let audit_logger = AuditLogger::new(state.spanner.clone());
            let client_ip = extract_client_ip(&request);
            let user_agent = extract_user_agent(&request);

            let audit_event = AuditEventBuilder::new(AuditAction::LoginFailure)
                .ip_address(client_ip.unwrap_or_else(|| "unknown".to_string()))
                .user_agent(user_agent.unwrap_or_else(|| "unknown".to_string()))
                .outcome(AuditOutcome::Failure)
                .severity(AuditSeverity::Warning)
                .metadata(serde_json::json!({
                    "error": error,
                    "auth_method": if request.headers().contains_key("authorization") { "jwt" } else { "api_key" }
                }))
                .build();

            if let Err(e) = audit_logger.log_event(audit_event).await {
                tracing::error!("Failed to log audit event: {}", e);
            }

            // Determine specific error code and user message based on error type
            let (error_code, user_message) = match error.as_str() {
                "No authentication provided" => (
                    "MISSING_AUTHENTICATION",
                    Some("Authentication is required. Please provide an API key or JWT token.".to_string())
                ),
                e if e.contains("Invalid API key") => (
                    "INVALID_API_KEY",
                    Some("The provided API key is invalid or has expired.".to_string())
                ),
                e if e.contains("API key has expired") => (
                    "EXPIRED_API_KEY",
                    Some("Your API key has expired. Please obtain a new API key.".to_string())
                ),
                e if e.contains("Token") => (
                    "INVALID_JWT_TOKEN",
                    Some("The provided JWT token is invalid or has expired.".to_string())
                ),
                e if e.contains("User not found") => (
                    "USER_NOT_FOUND",
                    Some("The authenticated user account was not found.".to_string())
                ),
                e if e.contains("User is not active") => (
                    "USER_INACTIVE",
                    Some("Your user account is inactive. Please contact support.".to_string())
                ),
                _ => (
                    "AUTHENTICATION_FAILED",
                    Some("Authentication failed. Please check your credentials.".to_string())
                ),
            };

            Err(create_auth_error_response(
                error_code,
                error,
                user_message,
                &request,
            ))
        }
    }
}

async fn validate_api_key(key: &str, spanner: &Arc<SpannerOperations>) -> Result<(String, i64), String> {
    // First, we need to find potential matching API keys by prefix or other identifier
    // Since we can't reverse the hash, we'll need to query by a searchable field
    // For now, we'll query all active keys and verify each one (not ideal for production)
    // In production, consider using a key prefix or other searchable identifier

    let statement = Statement::new(
        "SELECT user_id, rate_limit, expires_at, is_active, key_hash, salt
         FROM api_keys
         WHERE is_active = true"
    );

    let mut tx = spanner.client.read_only_transaction().await
        .map_err(|e| format!("Failed to create read transaction: {}", e))?;
    let mut reader = tx.query(statement).await
        .map_err(|e| format!("Failed to query API keys: {}", e))?;

    // Iterate through all active API keys and verify each one
    while let Some(row) = reader.next().await.map_err(|e| format!("Failed to read row: {}", e))? {

        // Get stored hash and salt
        let stored_hash: String = row.column_by_name("key_hash")
            .map_err(|e| format!("Failed to read key_hash: {}", e))?;
        let salt: String = row.column_by_name("salt")
            .map_err(|e| format!("Failed to read salt: {}", e))?;

        // Verify the API key against stored hash
        match verify_api_key(key, &stored_hash, &salt) {
            Ok(true) => {
                // Key matches! Check expiration
                let expires_at: Result<String, _> = row.column_by_name("expires_at");

                if let Ok(expires_str) = expires_at {
                    let expires_time = chrono::DateTime::parse_from_rfc3339(&expires_str)
                        .map_err(|e| format!("Invalid expiration time format: {}", e))?;

                    if expires_time < chrono::Utc::now() {
                        return Err("API key has expired".to_string());
                    }
                }

                // Get user ID and rate limit
                let user_id: String = row.column_by_name("user_id")
                    .map_err(|e| format!("Failed to read user_id: {}", e))?;
                let rate_limit: i64 = row.column_by_name("rate_limit")
                    .map_err(|e| format!("Failed to read rate_limit: {}", e))?;

                // Log successful API key validation
                tracing::debug!("API key validated for user: {}, rate_limit: {}", user_id, rate_limit);

                return Ok((user_id, rate_limit));
            }
            Ok(false) => {
                // Key doesn't match, continue to next key
                continue;
            }
            Err(e) => {
                tracing::warn!("Error verifying API key: {}", e);
                continue;
            }
        }
    }

    // No matching key found
    Err("Invalid API key".to_string())
}

async fn validate_user_in_database(user_id: &str, state: &AppState) -> Result<UserInfo, String> {
    // Query Spanner for user information
    let mut statement = Statement::new(
        "SELECT subscription_tier, rate_limit, is_active, created_at 
         FROM users 
         WHERE user_id = @user_id"
    );
    statement.add_param("user_id", &user_id);

    let row = match &state.spanner {
        Some(spanner) => {
            let mut tx = spanner.client.read_only_transaction().await
                .map_err(|e| format!("Failed to create read transaction: {}", e))?;
            let mut reader = tx.query(statement).await
                .map_err(|e| format!("Failed to query user: {}", e))?;
            reader.next().await.map_err(|e| format!("Failed to read row: {}", e))?
        },
        None => {
            // In memory mode, return default user info
            return Ok(UserInfo {
                rate_limit: 100,
                subscription_tier: "memory".to_string(),
                is_active: true,
            });
        }
    };

    if let Some(row) = row {
        
        // Check if user is active
        let is_active: bool = row.column_by_name("is_active")
            .map_err(|e| format!("Failed to read is_active: {}", e))?;
        
        if !is_active {
            return Err("User account is inactive".to_string());
        }

        // Get subscription tier and rate limit
        let subscription_tier: String = row.column_by_name("subscription_tier")
            .unwrap_or_else(|_| "free".to_string());
        
        // Get rate limit based on subscription tier or from database
        let rate_limit: i64 = row.column_by_name("rate_limit")
            .ok()
            .unwrap_or_else(|| {
                // Default rate limits by tier
                match subscription_tier.as_str() {
                    "free" => 1_000,
                    "pro" => 10_000,
                    "team" => 100_000,
                    "enterprise" => i64::MAX,
                    _ => 1_000,
                }
            });

        Ok(UserInfo {
            rate_limit,
            subscription_tier,
            is_active,
        })
    } else {
        Err("User not found".to_string())
    }
}

async fn validate_jwt_token(token: &str, state: &AppState) -> Result<(String, i64), String> {
    validate_jwt_token_with_request(token, state, None).await
}

async fn validate_jwt_token_with_request(token: &str, state: &AppState, request: Option<&Request<Body>>) -> Result<(String, i64), String> {
    let config = JwtConfig::default();

    // Decode header to determine key ID if using key rotation
    let header = decode_header(token)
        .map_err(|e| format!("Invalid JWT header: {}", e))?;

    // Get the appropriate key for verification based on key ID
    let key_manager = JWT_KEY_MANAGER.read()
        .map_err(|_| "Failed to acquire key manager lock".to_string())?;

    let jwt_key = if let Some(kid) = header.kid {
        // Use specific key if key ID is provided
        key_manager.get_key(&kid)
            .ok_or_else(|| format!("Unknown key ID: {}", kid))?
    } else {
        // Fall back to default key if no key ID specified
        key_manager.get_default_key()
            .ok_or_else(|| "No default JWT key configured".to_string())?
    };

    // Check if key has expired
    if let Some(expires_at) = jwt_key.expires_at {
        if SystemTime::now() > expires_at {
            return Err("JWT key has expired".to_string());
        }
    }

    // Configure comprehensive validation
    let mut validation = Validation::new(jwt_key.algorithm);
    validation.set_audience(&["ccl-analysis-engine"]);
    validation.set_issuer(&[&config.expected_issuer]);
    validation.validate_exp = true;
    validation.validate_nbf = true;
    validation.validate_aud = true;
    validation.leeway = config.max_clock_skew_seconds;

    let token_data = decode::<Claims>(
        token,
        &jwt_key.key,
        &validation,
    )
    .map_err(|e| match e.kind() {
        jsonwebtoken::errors::ErrorKind::ExpiredSignature => "Token has expired".to_string(),
        jsonwebtoken::errors::ErrorKind::InvalidToken => "Invalid token format".to_string(),
        jsonwebtoken::errors::ErrorKind::InvalidAudience => "Invalid token audience".to_string(),
        jsonwebtoken::errors::ErrorKind::InvalidIssuer => "Invalid token issuer".to_string(),
        jsonwebtoken::errors::ErrorKind::InvalidSignature => "Invalid token signature".to_string(),
        jsonwebtoken::errors::ErrorKind::ImmatureSignature => "Token not yet valid".to_string(),
        _ => format!("Token validation failed: {}", e),
    })?;

    let claims = &token_data.claims;

    // Enhanced validation checks
    validate_token_claims(claims, &config)?;

    // Check token revocation
    if let Some(jti) = &claims.jti {
        if is_token_revoked(jti) {
            return Err("Token has been revoked".to_string());
        }
    } else if config.require_jti {
        return Err("Token missing required JWT ID".to_string());
    }

    // Check session revocation
    if let Some(session_id) = &claims.session_id {
        if is_session_revoked(session_id) {
            return Err("Session has been revoked".to_string());
        }
    }

    // Validate device binding if request is available
    if let Some(req) = request {
        validate_device_binding(req, claims)?;
    }

    // Check if user exists and is active in the database
    let user_id = claims.sub.clone();
    let user_info = validate_user_in_database(&user_id, state).await?;

    tracing::debug!("JWT validated for user: {} with rate limit: {}", user_id, user_info.rate_limit);

    Ok((user_id, user_info.rate_limit))
}

/// Validate token claims with enhanced security checks
fn validate_token_claims(claims: &Claims, config: &JwtConfig) -> Result<(), String> {
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .map_err(|e| format!("System time error: {}", e))?
        .as_secs();

    // Check token age
    if now - claims.iat > config.max_token_age_seconds {
        return Err("Token is too old".to_string());
    }

    // Validate issuer (additional check beyond jsonwebtoken validation)
    if claims.iss != config.expected_issuer {
        return Err("Invalid token issuer".to_string());
    }

    // Validate not-before claim with clock skew tolerance
    if let Some(nbf) = claims.nbf {
        if now + config.max_clock_skew_seconds < nbf {
            return Err("Token not yet valid".to_string());
        }
    }

    // Validate scope if present
    if let Some(scope) = &claims.scope {
        let token_scopes: Vec<&str> = scope.split(' ').collect();
        let has_valid_scope = token_scopes.iter()
            .any(|s| config.allowed_scopes.contains(&s.to_string()));

        if !has_valid_scope {
            return Err("Token has insufficient scope".to_string());
        }
    }

    // Validate device binding if required
    if config.require_device_binding && claims.device_id.is_none() {
        return Err("Token missing required device binding".to_string());
    }

    Ok(())
}

// In-memory rate limiting fallback
lazy_static::lazy_static! {
    static ref RATE_LIMIT_CACHE: DashMap<String, (i64, SystemTime)> = DashMap::new();
}

// Token revocation store - in production this should be Redis or database-backed
lazy_static::lazy_static! {
    static ref REVOKED_TOKENS: DashMap<String, SystemTime> = DashMap::new();
    static ref REVOKED_SESSIONS: DashMap<String, SystemTime> = DashMap::new();
}

// JWT validation configuration
#[derive(Debug, Clone)]
struct JwtConfig {
    expected_issuer: String,
    max_token_age_seconds: u64,
    max_clock_skew_seconds: u64,
    require_jti: bool,
    require_device_binding: bool,
    allowed_scopes: Vec<String>,
}

impl Default for JwtConfig {
    fn default() -> Self {
        Self {
            expected_issuer: std::env::var("JWT_ISSUER")
                .unwrap_or_else(|_| "ccl-analysis-engine".to_string()),
            max_token_age_seconds: std::env::var("JWT_MAX_AGE_SECONDS")
                .unwrap_or_else(|_| "604800".to_string()) // 7 days
                .parse()
                .unwrap_or(604800),
            max_clock_skew_seconds: 300, // 5 minutes
            require_jti: std::env::var("JWT_REQUIRE_JTI")
                .unwrap_or_else(|_| "true".to_string())
                .parse()
                .unwrap_or(true),
            require_device_binding: std::env::var("JWT_REQUIRE_DEVICE_BINDING")
                .unwrap_or_else(|_| "false".to_string())
                .parse()
                .unwrap_or(false),
            allowed_scopes: std::env::var("JWT_ALLOWED_SCOPES")
                .unwrap_or_else(|_| "analysis:read,analysis:write".to_string())
                .split(',')
                .map(|s| s.trim().to_string())
                .collect(),
        }
    }
}

async fn check_rate_limit(
    state: &AppState,
    user_id: &str,
    limit: i64,
) -> Result<(bool, i64, u64), anyhow::Error> {
    const WINDOW_SECONDS: u64 = 3600; // 1 hour window
    
    // Try Redis first
    if let Some(redis) = &state.redis {
        match redis.check_rate_limit(user_id, limit, WINDOW_SECONDS).await {
            Ok(result) => return Ok(result),
            Err(e) => {
                tracing::warn!("Redis rate limiting failed, falling back to in-memory: {}", e);
            }
        }
    }
    
    // Fallback to in-memory rate limiting
    let now = SystemTime::now();
    
    let mut entry = RATE_LIMIT_CACHE.entry(user_id.to_string()).or_insert((0, now));
    let (count, last_reset) = entry.value_mut();
    
    // Check if we need to reset the window
    let elapsed = now.duration_since(*last_reset)
        .map_err(|e| anyhow::anyhow!("Time calculation error: {}", e))?
        .as_secs();
    if elapsed >= WINDOW_SECONDS {
        *count = 0;
        *last_reset = now;
    }

    if *count < limit {
        *count += 1;
        let remaining = limit - *count;
        let reset_time = last_reset.duration_since(UNIX_EPOCH)
            .map_err(|e| anyhow::anyhow!("Time calculation error: {}", e))?
            .as_secs() + WINDOW_SECONDS;
        Ok((true, remaining, reset_time))
    } else {
        let reset_time = last_reset.duration_since(UNIX_EPOCH)
            .map_err(|e| anyhow::anyhow!("Time calculation error: {}", e))?
            .as_secs() + WINDOW_SECONDS;
        Ok((false, 0, reset_time))
    }
}

// Helper trait to add Duration::from_days
trait DurationExt {
    fn from_days(days: u64) -> Duration;
}

impl DurationExt for Duration {
    fn from_days(days: u64) -> Duration {
        Duration::from_secs(days * 24 * 60 * 60)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_api_key_hashing() {
        let key = "test-api-key-12345";
        let mut hasher = Sha256::new();
        hasher.update(key.as_bytes());
        let hash = format!("{:x}", hasher.finalize());
        
        // Verify hash is consistent
        let mut hasher2 = Sha256::new();
        hasher2.update(key.as_bytes());
        let hash2 = format!("{:x}", hasher2.finalize());
        
        assert_eq!(hash, hash2);
        assert_eq!(hash.len(), 64); // SHA256 produces 64 hex characters
    }

    #[tokio::test]
    async fn test_jwt_validation() {
        use jsonwebtoken::{encode, EncodingKey, Header};
        
        std::env::set_var("JWT_SECRET", "test-secret");
        
        let claims = Claims {
            sub: "user123".to_string(),
            exp: (SystemTime::now() + Duration::from_secs(3600))
                .duration_since(UNIX_EPOCH)
                .expect("System time should be valid in tests")
                .as_secs(),
            iat: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .expect("System time should be valid in tests")
                .as_secs(),
            aud: "ccl-analysis-engine".to_string(),
            iss: "test-issuer".to_string(),
            nbf: None,
            jti: Some("test-jwt-id".to_string()),
            scope: Some("read:analysis".to_string()),
            session_id: Some("test-session-id".to_string()),
            device_id: Some("test-device-id".to_string()),
        };

        let token = encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret("test-secret".as_bytes()),
        )
        .expect("JWT encoding should work in tests");
        
        // Mock state would be needed for full test
        // This just verifies the JWT encoding/decoding logic
        assert!(!token.is_empty());
    }
}
