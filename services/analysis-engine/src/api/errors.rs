use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use uuid::Uuid;
use axum::response::{IntoResponse, Response};
use axum::http::StatusCode;
use axum::Json;

#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorResponse {
    pub error_id: String,
    pub service: String,
    pub error_type: ErrorType,
    pub error_code: Option<String>,
    pub message: String,
    pub user_message: Option<String>,
    pub retryable: bool,
    pub retry_after_seconds: Option<u32>,
    pub correlation_id: Option<String>,
    pub timestamp: DateTime<Utc>,
    pub context: Option<ErrorContext>,
    pub details: Option<ErrorDetails>,
    pub suggestions: Vec<ErrorSuggestion>,
    pub support: ErrorSupport,
    pub metadata: ErrorMetadata,
}

#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "snake_case")]
#[allow(dead_code)]
pub enum ErrorType {
    Validation,
    Authentication,
    Authorization,
    NotFound,
    Conflict,
    RateLimit,
    Timeout,
    Internal,
    ExternalDependency,
    QuotaExceeded,
    Maintenance,
}

#[derive(Debug, Serialize)]
pub struct ErrorContext {
    pub request_id: Option<String>,
    pub user_id: Option<String>,
    pub organization_id: Option<String>,
    pub resource_id: Option<String>,
    pub operation: Option<String>,
    pub parameters: Option<serde_json::Value>,
}

#[derive(Debug, Serialize)]
pub struct ErrorDetails {
    pub field_errors: Vec<FieldError>,
    pub constraint_violations: Vec<ConstraintViolation>,
    pub dependency_errors: Vec<DependencyError>,
}

#[derive(Debug, Serialize)]
pub struct FieldError {
    pub field: String,
    pub message: String,
    pub code: Option<String>,
    pub value: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ConstraintViolation {
    pub constraint: String,
    pub message: String,
    pub current_value: Option<String>,
    pub expected_value: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct DependencyError {
    pub service: String,
    pub error: String,
    pub status_code: Option<u16>,
    pub response_time_ms: Option<u64>,
}

#[derive(Debug, Serialize)]
pub struct ErrorSuggestion {
    pub action: String,
    pub description: String,
    pub url: Option<String>,
    pub priority: String,
}

#[derive(Debug, Serialize)]
pub struct ErrorSupport {
    pub contact_support: bool,
    pub support_url: Option<String>,
    pub documentation_url: Option<String>,
    pub status_page_url: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ErrorMetadata {
    pub service_version: String,
    pub api_version: String,
    pub environment: String,
    pub region: String,
    pub trace_id: Option<String>,
    pub span_id: Option<String>,
}

impl ErrorResponse {
    pub fn new(error_type: ErrorType, message: String) -> Self {
        Self {
            error_id: format!("error_{}", Uuid::new_v4().to_string().replace("-", "")[..16].to_string()),
            service: "repository-analysis".to_string(),
            error_type,
            error_code: None,
            message,
            user_message: None,
            retryable: false,
            retry_after_seconds: None,
            correlation_id: None,
            timestamp: Utc::now(),
            context: None,
            details: None,
            suggestions: vec![],
            support: ErrorSupport {
                contact_support: false,
                support_url: Some("https://ccl.dev/support".to_string()),
                documentation_url: Some("https://docs.ccl.dev".to_string()),
                status_page_url: Some("https://status.ccl.dev".to_string()),
            },
            metadata: ErrorMetadata {
                service_version: env!("CARGO_PKG_VERSION").to_string(),
                api_version: "v1".to_string(),
                environment: std::env::var("ENVIRONMENT").unwrap_or_else(|_| "development".to_string()),
                region: std::env::var("GCP_REGION").unwrap_or_else(|_| "us-central1".to_string()),
                trace_id: None,
                span_id: None,
            },
        }
    }

    pub fn validation_error(message: String) -> Self {
        let mut error = Self::new(ErrorType::Validation, message.clone());
        error.error_code = Some("VALIDATION_ERROR".to_string());
        error.user_message = Some("The request contains invalid data. Please check and try again.".to_string());
        error.suggestions.push(ErrorSuggestion {
            action: "review_request".to_string(),
            description: "Review the request parameters and ensure they meet the API requirements".to_string(),
            url: Some("https://docs.ccl.dev/api/validation".to_string()),
            priority: "high".to_string(),
        });
        error
    }

    pub fn not_found_error(resource: String) -> Self {
        let mut error = Self::new(ErrorType::NotFound, format!("{} not found", resource));
        error.error_code = Some("RESOURCE_NOT_FOUND".to_string());
        error.user_message = Some(format!("The requested {} could not be found.", resource));
        error
    }

    pub fn rate_limit_error(message: String, retry_after: Option<u32>) -> Self {
        let mut error = Self::new(ErrorType::RateLimit, message);
        error.error_code = Some("RATE_LIMIT_EXCEEDED".to_string());
        error.user_message = Some("Too many requests. Please wait before trying again.".to_string());
        error.retryable = true;
        error.retry_after_seconds = retry_after;
        error
    }

    pub fn internal_error(message: String) -> Self {
        let mut error = Self::new(ErrorType::Internal, message);
        error.error_code = Some("INTERNAL_ERROR".to_string());
        error.user_message = Some("An internal error occurred. Please try again later.".to_string());
        error.support.contact_support = true;
        error
    }
}

impl IntoResponse for ErrorResponse {
    fn into_response(self) -> Response {
        let status = match self.error_type {
            ErrorType::Validation => StatusCode::BAD_REQUEST,
            ErrorType::Authentication => StatusCode::UNAUTHORIZED,
            ErrorType::Authorization => StatusCode::FORBIDDEN,
            ErrorType::NotFound => StatusCode::NOT_FOUND,
            ErrorType::Conflict => StatusCode::CONFLICT,
            ErrorType::RateLimit => StatusCode::TOO_MANY_REQUESTS,
            ErrorType::Timeout => StatusCode::REQUEST_TIMEOUT,
            ErrorType::Internal => StatusCode::INTERNAL_SERVER_ERROR,
            ErrorType::ExternalDependency => StatusCode::BAD_GATEWAY,
            ErrorType::QuotaExceeded => StatusCode::PAYMENT_REQUIRED,
            ErrorType::Maintenance => StatusCode::SERVICE_UNAVAILABLE,
        };

        (status, Json(self)).into_response()
    }
}

// Legacy ApiError type for backward compatibility
#[derive(Debug)]
#[allow(dead_code)]
pub enum ApiError {
    BadRequest(String),
    Unauthorized(String),
    Forbidden(String),
    NotFound(String),
    Conflict(String),
    TooManyRequests(String),
    InternalError(String),
    ServiceUnavailable(String),
}

impl From<ApiError> for ErrorResponse {
    fn from(err: ApiError) -> Self {
        match err {
            ApiError::BadRequest(msg) => ErrorResponse::validation_error(msg),
            ApiError::Unauthorized(msg) => {
                let mut error = ErrorResponse::new(ErrorType::Authentication, msg);
                error.error_code = Some("UNAUTHORIZED".to_string());
                error
            }
            ApiError::Forbidden(msg) => {
                let mut error = ErrorResponse::new(ErrorType::Authorization, msg);
                error.error_code = Some("FORBIDDEN".to_string());
                error
            }
            ApiError::NotFound(msg) => ErrorResponse::not_found_error(msg),
            ApiError::Conflict(msg) => {
                let mut error = ErrorResponse::new(ErrorType::Conflict, msg);
                error.error_code = Some("CONFLICT".to_string());
                error
            }
            ApiError::TooManyRequests(msg) => ErrorResponse::rate_limit_error(msg, Some(60)),
            ApiError::InternalError(msg) => ErrorResponse::internal_error(msg),
            ApiError::ServiceUnavailable(msg) => {
                let mut error = ErrorResponse::new(ErrorType::Maintenance, msg);
                error.error_code = Some("SERVICE_UNAVAILABLE".to_string());
                error
            }
        }
    }
}

impl IntoResponse for ApiError {
    fn into_response(self) -> Response {
        let error_response: ErrorResponse = self.into();
        error_response.into_response()
    }
}

// Convert common errors to ApiError
impl From<anyhow::Error> for ApiError {
    fn from(err: anyhow::Error) -> Self {
        ApiError::InternalError(err.to_string())
    }
}