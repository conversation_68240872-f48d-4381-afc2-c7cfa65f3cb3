use std::sync::{Arc, atomic::{AtomicU64, AtomicUsize, Ordering}};
use std::time::{Duration, Instant};
use tokio::sync::{Semaphore, RwLock};
use tracing::{debug, warn};
use anyhow::Result;
use serde::{Deserialize, Serialize};

/// Backpressure management system for the analysis engine
#[derive(Debug)]
pub struct BackpressureManager {
    /// Semaphore for controlling concurrent analyses
    analysis_semaphore: Arc<Semaphore>,
    /// Semaphore for controlling concurrent file parsing
    parsing_semaphore: Arc<Semaphore>,
    /// Semaphore for controlling database operations
    database_semaphore: Arc<Semaphore>,
    /// Semaphore for controlling storage operations
    storage_semaphore: Arc<Semaphore>,
    /// Current system metrics
    metrics: Arc<RwLock<BackpressureMetrics>>,
    /// Configuration
    config: BackpressureConfig,
    /// Circuit breaker states
    circuit_breakers: Arc<RwLock<CircuitBreakerStates>>,
}

/// Configuration for backpressure management
#[derive(Debug, <PERSON><PERSON>)]
pub struct BackpressureConfig {
    /// Maximum concurrent analyses
    pub max_concurrent_analyses: usize,
    /// Maximum concurrent file parsing operations
    pub max_concurrent_parsing: usize,
    /// Maximum concurrent database operations
    pub max_concurrent_database: usize,
    /// Maximum concurrent storage operations
    pub max_concurrent_storage: usize,
    /// Memory threshold for triggering backpressure (in MB)
    pub memory_threshold_mb: u64,
    /// CPU threshold for triggering backpressure (percentage)
    pub cpu_threshold_percent: f32,
    /// Queue size threshold for triggering backpressure
    pub queue_size_threshold: usize,
    /// Circuit breaker failure threshold
    pub circuit_breaker_failure_threshold: u32,
    /// Circuit breaker timeout duration
    pub circuit_breaker_timeout: Duration,
}

impl Default for BackpressureConfig {
    fn default() -> Self {
        Self {
            max_concurrent_analyses: 50,
            max_concurrent_parsing: 200,
            max_concurrent_database: 100,
            max_concurrent_storage: 150,
            memory_threshold_mb: 3000, // 3GB
            cpu_threshold_percent: 80.0,
            queue_size_threshold: 1000,
            circuit_breaker_failure_threshold: 5,
            circuit_breaker_timeout: Duration::from_secs(30),
        }
    }
}

/// Current backpressure metrics
#[derive(Debug, Default, Clone, Serialize, Deserialize)]
pub struct BackpressureMetrics {
    /// Current memory usage in MB
    pub memory_usage_mb: u64,
    /// Current CPU usage percentage
    pub cpu_usage_percent: f32,
    /// Number of active analyses
    pub active_analyses: usize,
    /// Number of queued requests
    pub queued_requests: usize,
    /// Number of rejected requests due to backpressure
    pub rejected_requests: u64,
    /// Average response time in milliseconds
    pub avg_response_time_ms: u64,
    /// Last updated timestamp
    pub last_updated: u64,
}

/// Circuit breaker states for different services
#[derive(Debug, Default)]
struct CircuitBreakerStates {
    database: CircuitBreakerState,
    storage: CircuitBreakerState,
    pubsub: CircuitBreakerState,
    embeddings: CircuitBreakerState,
    redis: CircuitBreakerState,
}

/// Individual circuit breaker state
#[derive(Debug)]
struct CircuitBreakerState {
    failure_count: AtomicU64,
    last_failure_time: AtomicU64,
    state: AtomicUsize, // 0 = Closed, 1 = Open, 2 = HalfOpen
}

impl Default for CircuitBreakerState {
    fn default() -> Self {
        Self {
            failure_count: AtomicU64::new(0),
            last_failure_time: AtomicU64::new(0),
            state: AtomicUsize::new(0), // Closed
        }
    }
}

/// Backpressure decision result
#[derive(Debug, Clone)]
pub enum BackpressureDecision {
    /// Allow the request to proceed
    Allow,
    /// Reject the request with a reason
    Reject(BackpressureReason),
    /// Throttle the request (delay before processing)
    Throttle(Duration),
}

/// Reasons for applying backpressure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BackpressureReason {
    /// Too many concurrent analyses
    ConcurrencyLimit,
    /// High memory usage
    MemoryPressure,
    /// High CPU usage
    CpuPressure,
    /// Queue size exceeded
    QueueOverflow,
    /// Circuit breaker is open
    CircuitBreakerOpen(String),
    /// System overload
    SystemOverload,
}

impl BackpressureManager {
    /// Create a new backpressure manager
    pub fn new(config: BackpressureConfig) -> Self {
        Self {
            analysis_semaphore: Arc::new(Semaphore::new(config.max_concurrent_analyses)),
            parsing_semaphore: Arc::new(Semaphore::new(config.max_concurrent_parsing)),
            database_semaphore: Arc::new(Semaphore::new(config.max_concurrent_database)),
            storage_semaphore: Arc::new(Semaphore::new(config.max_concurrent_storage)),
            metrics: Arc::new(RwLock::new(BackpressureMetrics::default())),
            circuit_breakers: Arc::new(RwLock::new(CircuitBreakerStates::default())),
            config,
        }
    }

    /// Check if a new analysis request should be allowed
    pub async fn check_analysis_request(&self) -> BackpressureDecision {
        // Check circuit breakers first
        if let Some(reason) = self.check_circuit_breakers().await {
            return BackpressureDecision::Reject(reason);
        }

        // Check system resources
        let metrics = self.metrics.read().await;
        
        // Memory pressure check
        if metrics.memory_usage_mb > self.config.memory_threshold_mb {
            warn!("Rejecting request due to memory pressure: {}MB > {}MB", 
                  metrics.memory_usage_mb, self.config.memory_threshold_mb);
            return BackpressureDecision::Reject(BackpressureReason::MemoryPressure);
        }

        // CPU pressure check
        if metrics.cpu_usage_percent > self.config.cpu_threshold_percent {
            warn!("Rejecting request due to CPU pressure: {}% > {}%", 
                  metrics.cpu_usage_percent, self.config.cpu_threshold_percent);
            return BackpressureDecision::Reject(BackpressureReason::CpuPressure);
        }

        // Queue size check
        if metrics.queued_requests > self.config.queue_size_threshold {
            warn!("Rejecting request due to queue overflow: {} > {}", 
                  metrics.queued_requests, self.config.queue_size_threshold);
            return BackpressureDecision::Reject(BackpressureReason::QueueOverflow);
        }

        // Concurrency check
        if self.analysis_semaphore.available_permits() == 0 {
            debug!("Rejecting request due to concurrency limit");
            return BackpressureDecision::Reject(BackpressureReason::ConcurrencyLimit);
        }

        // Apply throttling based on system load
        let load_factor = self.calculate_load_factor(&metrics).await;
        if load_factor > 0.8 {
            let throttle_duration = Duration::from_millis((load_factor * 1000.0) as u64);
            debug!("Throttling request for {:?} due to high load factor: {}", 
                   throttle_duration, load_factor);
            return BackpressureDecision::Throttle(throttle_duration);
        }

        BackpressureDecision::Allow
    }

    /// Acquire a permit for analysis processing
    pub async fn acquire_analysis_permit(&self) -> Result<AnalysisPermit> {
        let permit = self.analysis_semaphore.clone().acquire_owned().await
            .map_err(|_| anyhow::anyhow!("Failed to acquire analysis permit"))?;

        Ok(AnalysisPermit {
            _permit: permit,
            start_time: Instant::now(),
        })
    }

    /// Acquire a permit for file parsing
    pub async fn acquire_parsing_permit(&self) -> Result<ParsingPermit> {
        let permit = self.parsing_semaphore.clone().acquire_owned().await
            .map_err(|_| anyhow::anyhow!("Failed to acquire parsing permit"))?;

        Ok(ParsingPermit {
            _permit: permit,
        })
    }

    /// Acquire a permit for database operations
    pub async fn acquire_database_permit(&self) -> Result<DatabasePermit> {
        let permit = self.database_semaphore.clone().acquire_owned().await
            .map_err(|_| anyhow::anyhow!("Failed to acquire database permit"))?;

        Ok(DatabasePermit {
            _permit: permit,
        })
    }

    /// Acquire a permit for storage operations
    pub async fn acquire_storage_permit(&self) -> Result<StoragePermit> {
        let permit = self.storage_semaphore.clone().acquire_owned().await
            .map_err(|_| anyhow::anyhow!("Failed to acquire storage permit"))?;

        Ok(StoragePermit {
            _permit: permit,
        })
    }

    /// Update system metrics
    pub async fn update_metrics(&self, metrics: BackpressureMetrics) -> Result<()> {
        let mut current_metrics = self.metrics.write().await;
        *current_metrics = metrics;
        current_metrics.last_updated = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        Ok(())
    }

    /// Get current metrics
    pub async fn get_metrics(&self) -> BackpressureMetrics {
        self.metrics.read().await.clone()
    }

    /// Execute a function with circuit breaker protection
    pub async fn execute_with_circuit_breaker<T, F, Fut, E>(
        &self,
        service: &str,
        operation: F,
    ) -> Result<T>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = std::result::Result<T, E>>,
        E: std::fmt::Display + std::fmt::Debug,
    {
        // Check if circuit breaker is open
        if let Some(reason) = self.check_service_circuit_breaker(service).await {
            return Err(anyhow::anyhow!("Circuit breaker open for {}: {:?}", service, reason));
        }

        // Execute the operation
        match operation().await {
            Ok(result) => {
                self.record_success(service).await;
                Ok(result)
            }
            Err(e) => {
                self.record_failure(service).await;
                Err(anyhow::anyhow!("Operation failed for {}: {}", service, e))
            }
        }
    }

    /// Check if a specific service's circuit breaker is open
    async fn check_service_circuit_breaker(&self, service: &str) -> Option<BackpressureReason> {
        let circuit_breakers = self.circuit_breakers.read().await;
        let breaker = match service {
            "database" => &circuit_breakers.database,
            "storage" => &circuit_breakers.storage,
            "pubsub" => &circuit_breakers.pubsub,
            "embeddings" => &circuit_breakers.embeddings,
            "redis" => &circuit_breakers.redis,
            _ => return None,
        };

        let state = breaker.state.load(Ordering::Relaxed);
        if state == 1 { // Open
            let now = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs();
            let last_failure = breaker.last_failure_time.load(Ordering::Relaxed);

            if now - last_failure > self.config.circuit_breaker_timeout.as_secs() {
                breaker.state.store(2, Ordering::Relaxed); // HalfOpen
                debug!("Circuit breaker for {} moved to half-open state", service);
                None
            } else {
                Some(BackpressureReason::CircuitBreakerOpen(service.to_string()))
            }
        } else {
            None
        }
    }

    /// Record a failure for circuit breaker
    pub async fn record_failure(&self, service: &str) {
        let circuit_breakers = self.circuit_breakers.read().await;
        let breaker = match service {
            "database" => &circuit_breakers.database,
            "storage" => &circuit_breakers.storage,
            "pubsub" => &circuit_breakers.pubsub,
            "embeddings" => &circuit_breakers.embeddings,
            "redis" => &circuit_breakers.redis,
            _ => return,
        };

        let failure_count = breaker.failure_count.fetch_add(1, Ordering::Relaxed) + 1;
        breaker.last_failure_time.store(
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            Ordering::Relaxed,
        );

        if failure_count >= self.config.circuit_breaker_failure_threshold as u64 {
            breaker.state.store(1, Ordering::Relaxed); // Open
            warn!("Circuit breaker opened for service: {}", service);
        }
    }

    /// Record a success for circuit breaker
    pub async fn record_success(&self, service: &str) {
        let circuit_breakers = self.circuit_breakers.read().await;
        let breaker = match service {
            "database" => &circuit_breakers.database,
            "storage" => &circuit_breakers.storage,
            "pubsub" => &circuit_breakers.pubsub,
            "embeddings" => &circuit_breakers.embeddings,
            "redis" => &circuit_breakers.redis,
            _ => return,
        };

        breaker.failure_count.store(0, Ordering::Relaxed);
        breaker.state.store(0, Ordering::Relaxed); // Closed
        debug!("Circuit breaker for {} reset to closed state", service);
    }

    /// Check circuit breaker states
    async fn check_circuit_breakers(&self) -> Option<BackpressureReason> {
        let circuit_breakers = self.circuit_breakers.read().await;
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        for (service, breaker) in [
            ("database", &circuit_breakers.database),
            ("storage", &circuit_breakers.storage),
            ("pubsub", &circuit_breakers.pubsub),
            ("embeddings", &circuit_breakers.embeddings),
            ("redis", &circuit_breakers.redis),
        ] {
            let state = breaker.state.load(Ordering::Relaxed);
            if state == 1 { // Open
                let last_failure = breaker.last_failure_time.load(Ordering::Relaxed);
                if now - last_failure > self.config.circuit_breaker_timeout.as_secs() {
                    breaker.state.store(2, Ordering::Relaxed); // HalfOpen
                } else {
                    return Some(BackpressureReason::CircuitBreakerOpen(service.to_string()));
                }
            }
        }

        None
    }

    /// Get circuit breaker status for all services
    pub async fn get_circuit_breaker_status(&self) -> CircuitBreakerStatus {
        let circuit_breakers = self.circuit_breakers.read().await;

        CircuitBreakerStatus {
            database: self.get_breaker_info(&circuit_breakers.database),
            storage: self.get_breaker_info(&circuit_breakers.storage),
            pubsub: self.get_breaker_info(&circuit_breakers.pubsub),
            embeddings: self.get_breaker_info(&circuit_breakers.embeddings),
            redis: self.get_breaker_info(&circuit_breakers.redis),
        }
    }

    /// Get information about a specific circuit breaker
    fn get_breaker_info(&self, breaker: &CircuitBreakerState) -> CircuitBreakerInfo {
        let state = breaker.state.load(Ordering::Relaxed);
        let failure_count = breaker.failure_count.load(Ordering::Relaxed);
        let last_failure_time = breaker.last_failure_time.load(Ordering::Relaxed);

        CircuitBreakerInfo {
            state: match state {
                0 => "closed".to_string(),
                1 => "open".to_string(),
                2 => "half_open".to_string(),
                _ => "unknown".to_string(),
            },
            failure_count,
            last_failure_time,
            threshold: self.config.circuit_breaker_failure_threshold as u64,
        }
    }

    /// Calculate system load factor (0.0 to 1.0)
    async fn calculate_load_factor(&self, metrics: &BackpressureMetrics) -> f32 {
        let memory_factor = metrics.memory_usage_mb as f32 / self.config.memory_threshold_mb as f32;
        let cpu_factor = metrics.cpu_usage_percent / self.config.cpu_threshold_percent;
        let queue_factor = metrics.queued_requests as f32 / self.config.queue_size_threshold as f32;

        // Take the maximum factor as the overall load
        memory_factor.max(cpu_factor).max(queue_factor).min(1.0)
    }
}

/// Circuit breaker status for all services
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitBreakerStatus {
    pub database: CircuitBreakerInfo,
    pub storage: CircuitBreakerInfo,
    pub pubsub: CircuitBreakerInfo,
    pub embeddings: CircuitBreakerInfo,
    pub redis: CircuitBreakerInfo,
}

/// Information about a specific circuit breaker
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitBreakerInfo {
    pub state: String,
    pub failure_count: u64,
    pub last_failure_time: u64,
    pub threshold: u64,
}

/// RAII permit for analysis operations
pub struct AnalysisPermit {
    _permit: tokio::sync::OwnedSemaphorePermit,
    start_time: Instant,
}

impl Drop for AnalysisPermit {
    fn drop(&mut self) {
        let duration = self.start_time.elapsed();
        debug!("Analysis permit released after {:?}", duration);
    }
}

/// RAII permit for parsing operations
pub struct ParsingPermit {
    _permit: tokio::sync::OwnedSemaphorePermit,
}

/// RAII permit for database operations
pub struct DatabasePermit {
    _permit: tokio::sync::OwnedSemaphorePermit,
}

/// RAII permit for storage operations
pub struct StoragePermit {
    _permit: tokio::sync::OwnedSemaphorePermit,
}
