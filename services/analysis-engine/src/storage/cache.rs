use anyhow::{Context, Result};
use crate::storage::redis_client::RedisClient;
use crate::models::FileAnalysis;
use std::sync::Arc;
use serde::{Serialize, Deserialize};

#[derive(Serialize, Deserialize)]
pub struct CachedAnalysis {
    pub commit_hash: String,
    pub analyses: Vec<FileAnalysis>,
    pub cached_at: chrono::DateTime<chrono::Utc>,
}

/// Cache key prefixes for different types of data
const ANALYSIS_CACHE_PREFIX: &str = "analysis:";
const PATTERN_CACHE_PREFIX: &str = "patterns:";
const EMBEDDING_CACHE_PREFIX: &str = "embeddings:";

/// Default TTL values in seconds
const ANALYSIS_CACHE_TTL: u64 = 3600; // 1 hour
const PATTERN_CACHE_TTL: u64 = 7200; // 2 hours
const EMBEDDING_CACHE_TTL: u64 = 86400; // 24 hours

pub struct CacheManager {
    redis_client: Option<Arc<RedisClient>>,
}

impl CacheManager {
    pub fn new(redis_client: Option<Arc<RedisClient>>) -> Self {
        Self { redis_client }
    }

    pub fn new_without_redis() -> Self {
        Self { redis_client: None }
    }
    
    /// Get cached analysis results
    pub async fn get_analysis(&self, analysis_id: &str) -> Result<Option<Vec<FileAnalysis>>> {
        if let Some(redis) = &self.redis_client {
            let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
            match redis.get_cached_analysis(&key).await {
                Ok(Some(data)) => {
                    // Try to deserialize as new format first
                    if let Ok(cached_analysis) = serde_json::from_str::<CachedAnalysis>(&data) {
                        Ok(Some(cached_analysis.analyses))
                    } else {
                        // Fallback to old format for backward compatibility
                        let analyses: Vec<FileAnalysis> = serde_json::from_str(&data)
                            .context("Failed to deserialize cached analysis")?;
                        Ok(Some(analyses))
                    }
                }
                Ok(None) => Ok(None),
                Err(e) => {
                    tracing::warn!("Failed to get cached analysis: {}", e);
                    Ok(None) // Graceful fallback
                }
            }
        } else {
            Ok(None)
        }
    }

    /// Get cached analysis with commit hash validation
    pub async fn get_analysis_with_commit_check(&self, analysis_id: &str, current_commit_hash: &str) -> Result<Option<Vec<FileAnalysis>>> {
        if let Some(redis) = &self.redis_client {
            let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
            match redis.get_cached_analysis(&key).await {
                Ok(Some(data)) => {
                    if let Ok(cached_analysis) = serde_json::from_str::<CachedAnalysis>(&data) {
                        // Check if commit hash matches
                        if cached_analysis.commit_hash == current_commit_hash {
                            tracing::info!("Cache hit with matching commit hash for analysis: {}", analysis_id);
                            Ok(Some(cached_analysis.analyses))
                        } else {
                            tracing::info!("Cache hit but commit hash mismatch for analysis: {} (cached: {}, current: {})",
                                analysis_id, cached_analysis.commit_hash, current_commit_hash);
                            Ok(None) // Cache is stale
                        }
                    } else {
                        // Old format cache - consider it stale
                        tracing::info!("Cache hit but old format for analysis: {}, considering stale", analysis_id);
                        Ok(None)
                    }
                }
                Ok(None) => Ok(None),
                Err(e) => {
                    tracing::warn!("Failed to get cached analysis: {}", e);
                    Ok(None) // Graceful fallback
                }
            }
        } else {
            Ok(None)
        }
    }
    
    /// Set cached analysis results
    pub async fn set_analysis(&self, analysis_id: &str, analyses: &[FileAnalysis]) -> Result<()> {
        if let Some(redis) = &self.redis_client {
            let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
            let data = serde_json::to_string(analyses)
                .context("Failed to serialize analysis for caching")?;

            match redis.set_cached_analysis(&key, &data, ANALYSIS_CACHE_TTL).await {
                Ok(()) => Ok(()),
                Err(e) => {
                    tracing::warn!("Failed to cache analysis: {}", e);
                    Ok(()) // Graceful fallback - don't fail the operation
                }
            }
        } else {
            Ok(())
        }
    }

    /// Set cached analysis results with commit hash
    pub async fn set_analysis_with_commit(&self, analysis_id: &str, analyses: &[FileAnalysis], commit_hash: &str) -> Result<()> {
        if let Some(redis) = &self.redis_client {
            let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
            let cached_analysis = CachedAnalysis {
                commit_hash: commit_hash.to_string(),
                analyses: analyses.to_vec(),
                cached_at: chrono::Utc::now(),
            };
            let data = serde_json::to_string(&cached_analysis)
                .context("Failed to serialize analysis with commit hash for caching")?;

            match redis.set_cached_analysis(&key, &data, ANALYSIS_CACHE_TTL).await {
                Ok(()) => {
                    tracing::info!("Cached analysis {} with commit hash {}", analysis_id, commit_hash);
                    Ok(())
                },
                Err(e) => {
                    tracing::warn!("Failed to cache analysis: {}", e);
                    Ok(()) // Graceful fallback - don't fail the operation
                }
            }
        } else {
            Ok(())
        }
    }
    
    /// Get cached patterns for a file
    #[allow(dead_code)]
    pub async fn get_patterns(&self, file_hash: &str) -> Result<Option<Vec<String>>> {
        if let Some(redis) = &self.redis_client {
            let key = format!("{}{}", PATTERN_CACHE_PREFIX, file_hash);
            match redis.get_cached_analysis(&key).await {
                Ok(Some(data)) => {
                    let patterns: Vec<String> = serde_json::from_str(&data)
                        .context("Failed to deserialize cached patterns")?;
                    Ok(Some(patterns))
                }
                Ok(None) => Ok(None),
                Err(e) => {
                    tracing::warn!("Failed to get cached patterns: {}", e);
                    Ok(None)
                }
            }
        } else {
            Ok(None)
        }
    }
    
    /// Set cached patterns for a file
    pub async fn set_patterns(&self, file_hash: &str, patterns: &[String]) -> Result<()> {
        if let Some(redis) = &self.redis_client {
            let key = format!("{}{}", PATTERN_CACHE_PREFIX, file_hash);
            let data = serde_json::to_string(patterns)
                .context("Failed to serialize patterns for caching")?;
            
            match redis.set_cached_analysis(&key, &data, PATTERN_CACHE_TTL).await {
                Ok(()) => Ok(()),
                Err(e) => {
                    tracing::warn!("Failed to cache patterns: {}", e);
                    Ok(())
                }
            }
        } else {
            Ok(())
        }
    }
    
    /// Get cached embeddings
    #[allow(dead_code)]
    pub async fn get_embeddings(&self, chunk_id: &str) -> Result<Option<Vec<f32>>> {
        if let Some(redis) = &self.redis_client {
            let key = format!("{}{}", EMBEDDING_CACHE_PREFIX, chunk_id);
            match redis.get_cached_analysis(&key).await {
                Ok(Some(data)) => {
                    let embeddings: Vec<f32> = serde_json::from_str(&data)
                        .context("Failed to deserialize cached embeddings")?;
                    Ok(Some(embeddings))
                }
                Ok(None) => Ok(None),
                Err(e) => {
                    tracing::warn!("Failed to get cached embeddings: {}", e);
                    Ok(None)
                }
            }
        } else {
            Ok(None)
        }
    }
    
    /// Set cached embeddings
    pub async fn set_embeddings(&self, chunk_id: &str, embeddings: &[f32]) -> Result<()> {
        if let Some(redis) = &self.redis_client {
            let key = format!("{}{}", EMBEDDING_CACHE_PREFIX, chunk_id);
            let data = serde_json::to_string(embeddings)
                .context("Failed to serialize embeddings for caching")?;
            
            match redis.set_cached_analysis(&key, &data, EMBEDDING_CACHE_TTL).await {
                Ok(()) => Ok(()),
                Err(e) => {
                    tracing::warn!("Failed to cache embeddings: {}", e);
                    Ok(())
                }
            }
        } else {
            Ok(())
        }
    }
    
    /// Check if cache is available
    #[allow(dead_code)]
    pub fn is_available(&self) -> bool {
        self.redis_client.is_some()
    }
    
    /// Clear all cache for an analysis
    #[allow(dead_code)]
    pub async fn clear_analysis_cache(&self, analysis_id: &str) -> Result<()> {
        if let Some(redis) = &self.redis_client {
            let key = format!("{}{}", ANALYSIS_CACHE_PREFIX, analysis_id);
            // Redis doesn't have a direct delete method in our client, 
            // so we'll set with 1 second TTL to effectively delete
            match redis.set_cached_analysis(&key, "", 1).await {
                Ok(()) => Ok(()),
                Err(e) => {
                    tracing::warn!("Failed to clear analysis cache: {}", e);
                    Ok(())
                }
            }
        } else {
            Ok(())
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_cache_manager_without_redis() {
        let cache = CacheManager::new(None);
        
        // Should gracefully handle no Redis
        assert!(!cache.is_available());
        
        let result = cache.get_analysis("test-id").await.unwrap();
        assert!(result.is_none());
        
        // Set should not fail
        let analyses = vec![];
        cache.set_analysis("test-id", &analyses).await.unwrap();
    }
}