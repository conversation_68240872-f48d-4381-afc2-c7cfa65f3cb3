# Update Embedding Model to text-embedding-005

## Changes Required

### 1. Update Model Name and Endpoint
In `src/services/embeddings.rs`, line 223:

```rust
// Current:
"https://{}-aiplatform.googleapis.com/v1/projects/{}/locations/{}/publishers/google/models/text-embedding-004:predict"

// Update to:
"https://{}-aiplatform.googleapis.com/v1/projects/{}/locations/{}/publishers/google/models/text-embedding-005:predict"
```

### 2. Update Model Reference
Line 270 and 388:

```rust
// Current:
model: "text-embedding-004".to_string(),

// Update to:
model: "text-embedding-005".to_string(),
```

### 3. Add Task Type Support (Optional but Recommended)
text-embedding-005 supports task types which improve embedding quality:

```rust
#[derive(Debug, Serialize)]
struct EmbeddingRequest {
    instances: Vec<EmbeddingInstance>,
    parameters: Option<EmbeddingParameters>,
}

#[derive(Debug, Serialize)]
struct EmbeddingParameters {
    task_type: Option<String>,
}

// When creating embeddings for code:
let request = EmbeddingRequest {
    instances: instances.to_vec(),
    parameters: Some(EmbeddingParameters {
        task_type: Some("RETRIEVAL_DOCUMENT".to_string()),
    }),
};
```

### 4. For Code Search Queries (Future Enhancement)
When implementing code search, use:
```rust
task_type: Some("CODE_RETRIEVAL_QUERY".to_string()),
```

## Benefits of Upgrading

1. **Better Performance**: text-embedding-005 outperforms 004 on all benchmarks
2. **Code-Specific Support**: CODE_RETRIEVAL_QUERY task type optimized for code search
3. **Longer Context**: Supports longer input sequences
4. **Future-Proof**: Latest stable model from Google

## Testing After Update

1. Verify embeddings still generate correctly
2. Check dimension is still 768
3. Test with various code snippets
4. Monitor performance improvements