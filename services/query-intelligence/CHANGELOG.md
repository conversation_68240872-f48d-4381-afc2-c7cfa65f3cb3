# Changelog - Query Intelligence Service

All notable changes to the Query Intelligence service are documented here.

## [Unreleased] - July 2025

### 🚀 Major Updates

#### Google GenAI SDK Migration (Critical)
- ✅ Migrated from deprecated Vertex AI SDK to unified Google GenAI SDK
- ✅ Added support for Gemini 2.5 models (Flash, Flash-Lite, Pro)
- ✅ Implemented model routing for optimal performance/cost balance
- ✅ Added streaming API support with async generators

#### Security Enhancements
- ✅ Implemented comprehensive security middleware:
  - Prompt injection detection and prevention
  - PII (Personally Identifiable Information) detection
  - SQL/Code injection blocking
  - Input sanitization and validation
- ✅ Integrated GCP Secret Manager for all credentials
- ✅ Added JWT authentication with secure token management
- ✅ Implemented rate limiting per user with Redis

#### Reliability & Fault Tolerance
- ✅ Added circuit breakers for all external services:
  - Analysis Engine (3 failures → 30s recovery)
  - Pattern Mining (3 failures → 60s recovery)
  - Redis Cache (5 failures → 30s recovery)
  - LLM Service (3 failures → 60s recovery)
  - Pinecone (3 failures → 30s recovery)
- ✅ Implemented fallback handlers for graceful degradation
- ✅ Added comprehensive health checks with dependency status

#### Pattern Mining Integration
- ✅ Integrated pattern-mining service for code quality insights
- ✅ Added pattern detection for relevant query intents
- ✅ Included pattern recommendations in LLM responses
- ✅ Added code quality scoring and anti-pattern detection

#### Monitoring & Observability
- ✅ Implemented Prometheus metrics:
  - Query processing metrics by intent
  - LLM token usage tracking
  - Embedding generation timing
  - Circuit breaker status
- ✅ Added structured logging with correlation IDs
- ✅ Created `/circuit-breakers` status endpoint

### 🔧 Technical Improvements

#### Performance Optimizations
- Improved integration test coverage from 30% to 87.5%
- Added async streaming for real-time responses
- Optimized embedding caching strategy
- Implemented connection pooling for all clients

#### Code Quality
- Added comprehensive unit tests for new features
- Improved error handling and recovery
- Enhanced type hints and documentation
- Standardized logging across all modules

### 📝 Configuration Updates

#### New Environment Variables
```bash
# Pattern Mining Integration
PATTERN_MINING_URL=http://pattern-mining:8003

# Security Features
USE_SECRET_MANAGER=true
SECRET_PROJECT_ID=your-project-id
ENABLE_INPUT_VALIDATION=true
ENABLE_PROMPT_INJECTION_DETECTION=true
ENABLE_PII_DETECTION=true

# Circuit Breaker Settings (defaults)
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60
```

### 🐛 Bug Fixes
- Fixed JWT token validation in auth middleware
- Resolved Redis connection issues in tests
- Fixed streaming API implementation for new SDK
- Corrected metrics collection for Prometheus

### 📚 Documentation
- Updated README with new features and configuration
- Added security best practices guide
- Created troubleshooting section for common issues
- Added migration guide from Vertex AI to GenAI SDK

### ⚠️ Breaking Changes
- Vertex AI SDK is no longer supported (use Google GenAI SDK)
- JWT_SECRET_KEY must be in Secret Manager for production
- Changed LLM service from `llm_service.py` to `llm_service_v2.py`

### 🔄 Migration Notes

To migrate to this version:

1. **Update dependencies**:
   ```bash
   poetry remove google-cloud-aiplatform
   poetry add google-genai
   ```

2. **Update environment variables**:
   - Add `USE_VERTEX_AI=true` for production
   - Add `USE_SECRET_MANAGER=true` for production
   - Configure pattern mining URL if using the feature

3. **Update imports**:
   - Change `from .llm_service import` to `from .llm_service_v2 import`

4. **Test circuit breakers**:
   - Monitor `/circuit-breakers` endpoint
   - Adjust thresholds if needed

## [1.0.0] - June 2025

### Initial Release
- Natural language query processing
- Semantic code search with Pinecone
- LLM-powered response generation
- WebSocket streaming support
- Basic caching with Redis
- JWT authentication
- Prometheus metrics
- Health check endpoints

---

## Version Summary

| Version | Date | Status | Key Features |
|---------|------|--------|--------------|
| Unreleased | July 2025 | In Progress | GenAI SDK, Security, Circuit Breakers, Patterns |
| 1.0.0 | June 2025 | Released | Initial MVP |