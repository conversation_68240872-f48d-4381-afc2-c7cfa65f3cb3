"""
Security middleware for input validation and threat detection
"""

import re
import json
from typing import Dict, List, Any, Optional
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
import structlog

from ..config.settings import get_settings

logger = structlog.get_logger()
settings = get_settings()


class SecurityMiddleware:
    """Comprehensive security middleware for the query intelligence service"""
    
    def __init__(self):
        self.max_query_length = settings.MAX_QUERY_LENGTH
        self.enable_input_validation = settings.ENABLE_INPUT_VALIDATION
        self.enable_prompt_injection_detection = settings.ENABLE_PROMPT_INJECTION_DETECTION
        self.enable_pii_detection = settings.ENABLE_PII_DETECTION
        
        # Compile regex patterns for performance
        self._compile_patterns()
    
    def _compile_patterns(self):
        """Compile regex patterns for security checks"""
        # Prompt injection patterns
        self.prompt_injection_patterns = [
            re.compile(r"ignore\s+(?:all\s+)?(?:previous|above|all)\s+(?:instructions|prompts)", re.IGNORECASE),
            re.compile(r"you\s+are\s+(?:now|actually)\s+(?:a|an)\s+", re.IGNORECASE),
            re.compile(r"(?:system|assistant|user)\s*:\s*", re.IGNORECASE),
            re.compile(r"(?:execute|run|eval|import|exec)\s*\(", re.IGNORECASE),
            re.compile(r"<script|javascript:|data:text/html", re.IGNORECASE),
            re.compile(r"(?:tell|show|give)\s+me\s+(?:the|your)\s+(?:prompt|instructions)", re.IGNORECASE),
            re.compile(r"(?:bypass|override|disable)\s+(?:safety|security|filter)", re.IGNORECASE),
            re.compile(r"role\s*:\s*(?:admin|system|assistant|user)", re.IGNORECASE),
        ]
        
        # PII patterns
        self.pii_patterns = [
            re.compile(r"\b\d{3}-\d{2}-\d{4}\b"),  # SSN
            re.compile(r"\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b"),  # Credit card
            re.compile(r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"),  # Email
            re.compile(r"\b\d{3}-\d{3}-\d{4}\b"),  # Phone number
            re.compile(r"(?:password|pwd|api[_\s-]?key|secret)\s*(?:is|:)\s*[:=]?\s*\S+", re.IGNORECASE),
        ]
        
        # SQL injection patterns
        self.sql_injection_patterns = [
            re.compile(r"(?:union|select|insert|update|delete|drop|create|alter)\s+", re.IGNORECASE),
            re.compile(r"(?:or|and)\s+(?:1=1|true|false)", re.IGNORECASE),
            re.compile(r"['\"];?\s*(?:union|select|insert|update|delete)", re.IGNORECASE),
        ]
        
        # Code injection patterns
        self.code_injection_patterns = [
            re.compile(r"(?:import|require|include)\s*\(?\s*[\"']", re.IGNORECASE),
            re.compile(r"(?:eval|exec|system|shell_exec|passthru)\s*\(", re.IGNORECASE),
            re.compile(r"(?:__import__|getattr|setattr|hasattr)\s*\(", re.IGNORECASE),
            re.compile(r"(?:subprocess|os\.system|os\.popen)", re.IGNORECASE),
        ]
    
    async def __call__(self, request: Request, call_next):
        """Main security middleware function"""
        try:
            # Skip security checks for health/metrics endpoints
            if request.url.path in ["/health", "/ready", "/metrics"]:
                return await call_next(request)
            
            # Validate request
            validation_result = await self._validate_request(request)
            if validation_result:
                return validation_result
            
            # Process request
            response = await call_next(request)
            
            # Add security headers
            response = self._add_security_headers(response)
            
            return response
            
        except Exception as e:
            logger.error("security_middleware_error", error=str(e), exc_info=True)
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"error": "Internal security error"}
            )
    
    async def _validate_request(self, request: Request) -> Optional[Response]:
        """Validate incoming request for security threats"""
        try:
            # Get request body if it exists
            body = None
            if request.method in ["POST", "PUT", "PATCH"]:
                body = await request.body()
                
                # Parse JSON body
                if body:
                    try:
                        request_data = json.loads(body.decode())
                    except json.JSONDecodeError:
                        if self.enable_input_validation:
                            return JSONResponse(
                                status_code=status.HTTP_400_BAD_REQUEST,
                                content={"error": "Invalid JSON format"}
                            )
                        request_data = {}
                else:
                    request_data = {}
            else:
                request_data = {}
            
            # Check query parameters
            query_params = dict(request.query_params)
            
            # Validate query length
            if "query" in request_data:
                query_length = len(request_data["query"])
                if query_length > self.max_query_length:
                    return JSONResponse(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        content={
                            "error": f"Query too long. Maximum length is {self.max_query_length} characters"
                        }
                    )
            
            # Check for prompt injection
            if self.enable_prompt_injection_detection:
                threat_result = self._check_prompt_injection(request_data, query_params)
                if threat_result:
                    client_ip = self._get_client_ip(request)
                    logger.warning(
                        "prompt_injection_detected",
                        client_ip=client_ip,
                        path=request.url.path,
                        method=request.method
                    )
                    return threat_result
            
            # Check for PII
            if self.enable_pii_detection:
                pii_result = self._check_pii(request_data, query_params)
                if pii_result:
                    client_ip = self._get_client_ip(request)
                    logger.warning(
                        "pii_detected",
                        client_ip=client_ip,
                        path=request.url.path,
                        method=request.method
                    )
                    return pii_result
            
            # Check for code injection
            if self.enable_input_validation:
                injection_result = self._check_code_injection(request_data, query_params)
                if injection_result:
                    client_ip = self._get_client_ip(request)
                    logger.warning(
                        "code_injection_detected",
                        client_ip=client_ip,
                        path=request.url.path,
                        method=request.method
                    )
                    return injection_result
            
            return None
            
        except Exception as e:
            logger.error("request_validation_error", error=str(e), exc_info=True)
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"error": "Validation error"}
            )
    
    def _check_prompt_injection(self, request_data: Dict, query_params: Dict) -> Optional[Response]:
        """Check for prompt injection attacks"""
        # Check all text fields
        text_fields = []
        
        # Extract text from request
        if "query" in request_data:
            text_fields.append(request_data["query"])
        
        # Add query parameters
        text_fields.extend(query_params.values())
        
        # Check each text field
        for text in text_fields:
            if isinstance(text, str):
                for pattern in self.prompt_injection_patterns:
                    if pattern.search(text):
                        logger.warning(
                            "prompt_injection_detected",
                            text=text[:100],
                            pattern=pattern.pattern,
                            client_ip=self._get_client_ip()
                        )
                        return JSONResponse(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            content={
                                "error": "Potential prompt injection detected",
                                "type": "security_violation"
                            }
                        )
        
        return None
    
    def _check_pii(self, request_data: Dict, query_params: Dict) -> Optional[Response]:
        """Check for Personally Identifiable Information"""
        # Check all text fields
        text_fields = []
        
        # Extract text from request
        if "query" in request_data:
            text_fields.append(request_data["query"])
        
        # Add query parameters
        text_fields.extend(query_params.values())
        
        # Check each text field
        for text in text_fields:
            if isinstance(text, str):
                for pattern in self.pii_patterns:
                    if pattern.search(text):
                        logger.warning(
                            "pii_detected",
                            text="[REDACTED]",
                            pattern_type="pii",
                            client_ip=self._get_client_ip()
                        )
                        return JSONResponse(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            content={
                                "error": "Personally identifiable information detected",
                                "type": "pii_violation"
                            }
                        )
        
        return None
    
    def _check_code_injection(self, request_data: Dict, query_params: Dict) -> Optional[Response]:
        """Check for code injection attempts"""
        # Check all text fields
        text_fields = []
        
        # Extract text from request
        if "query" in request_data:
            text_fields.append(request_data["query"])
        
        # Add query parameters
        text_fields.extend(query_params.values())
        
        # Check each text field
        for text in text_fields:
            if isinstance(text, str):
                # Check SQL injection
                for pattern in self.sql_injection_patterns:
                    if pattern.search(text):
                        logger.warning(
                            "sql_injection_detected",
                            text=text[:100],
                            pattern=pattern.pattern,
                            client_ip=self._get_client_ip()
                        )
                        return JSONResponse(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            content={
                                "error": "Potential SQL injection detected",
                                "type": "security_violation"
                            }
                        )
                
                # Check code injection
                for pattern in self.code_injection_patterns:
                    if pattern.search(text):
                        logger.warning(
                            "code_injection_detected",
                            text=text[:100],
                            pattern=pattern.pattern,
                            client_ip=self._get_client_ip()
                        )
                        return JSONResponse(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            content={
                                "error": "Potential code injection detected",
                                "type": "security_violation"
                            }
                        )
        
        return None
    
    def _add_security_headers(self, response: Response) -> Response:
        """Add security headers to response"""
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Content-Security-Policy"] = "default-src 'self'"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address from request headers
        
        Checks in order:
        1. X-Forwarded-For header (for proxies/load balancers)
        2. X-Real-IP header (alternative proxy header)
        3. X-Client-IP header (another alternative)
        4. Direct client connection IP
        """
        # Check forwarded headers (common in production with load balancers)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # X-Forwarded-For can contain multiple IPs, take the first one
            ips = [ip.strip() for ip in forwarded_for.split(",")]
            if ips and ips[0]:
                return ips[0]
        
        # Check alternative headers
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
            
        client_ip = request.headers.get("X-Client-IP")
        if client_ip:
            return client_ip
        
        # Fallback to direct connection
        if request.client and request.client.host:
            return request.client.host
            
        return "unknown"


# Middleware instance
security_middleware = SecurityMiddleware()


async def security_middleware_func(request: Request, call_next):
    """Security middleware function for FastAPI"""
    return await security_middleware(request, call_next)


def validate_query_input(query: str) -> Dict[str, Any]:
    """Standalone function to validate query input"""
    security = SecurityMiddleware()
    
    result = {
        "valid": True,
        "errors": [],
        "warnings": []
    }
    
    # Allow empty queries
    if not query:
        return result
    
    # Check length
    if len(query) > security.max_query_length:
        result["valid"] = False
        result["errors"].append(f"Query too long (max {security.max_query_length} characters)")
    
    # Check for prompt injection
    for pattern in security.prompt_injection_patterns:
        if pattern.search(query):
            result["valid"] = False
            result["errors"].append("Potential prompt injection detected")
            break
    
    # Check for PII
    for pattern in security.pii_patterns:
        if pattern.search(query):
            result["valid"] = False
            result["errors"].append("Personally identifiable information detected")
            break
    
    # Check for SQL injection
    for pattern in security.sql_injection_patterns:
        if pattern.search(query):
            result["valid"] = False
            result["errors"].append("Potential SQL injection detected")
            break
    
    # Check for code injection
    for pattern in security.code_injection_patterns:
        if pattern.search(query):
            result["valid"] = False
            result["errors"].append("Potential code injection detected")
            break
    
    return result