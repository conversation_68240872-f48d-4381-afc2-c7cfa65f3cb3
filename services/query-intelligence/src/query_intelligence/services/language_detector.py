"""
Multi-language query support with automatic language detection
and translation capabilities
"""

import re
from typing import Di<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, List
from dataclasses import dataclass
from enum import Enum
import structlog
from langdetect import detect_langs, LangDetectException
from googletrans import Translator

logger = structlog.get_logger()


class SupportedLanguage(Enum):
    """Languages supported for queries"""
    ENGLISH = "en"
    SPANISH = "es"
    FRENCH = "fr"
    GERMAN = "de"
    ITALIAN = "it"
    PORTUGUESE = "pt"
    DUTCH = "nl"
    POLISH = "pl"
    RUSSIAN = "ru"
    JAPANESE = "ja"
    KOREAN = "ko"
    CHINESE_SIMPLIFIED = "zh-cn"
    CHINESE_TRADITIONAL = "zh-tw"
    ARABIC = "ar"
    HINDI = "hi"
    

@dataclass
class LanguageDetectionResult:
    """Result of language detection"""
    detected_language: str
    confidence: float
    is_supported: bool
    needs_translation: bool
    

@dataclass 
class TranslationResult:
    """Result of query translation"""
    original_query: str
    translated_query: str
    source_language: str
    target_language: str = "en"
    confidence: float = 1.0


class MultiLanguageSupport:
    """Service for handling multi-language queries"""
    
    def __init__(self):
        self.translator = Translator()
        self.supported_languages = {lang.value for lang in SupportedLanguage}
        self.programming_keywords = self._load_programming_keywords()
        
    def _load_programming_keywords(self) -> Dict[str, List[str]]:
        """Load programming keywords for different languages"""
        return {
            "function": [
                "function", "función", "fonction", "funktion", "funzione",
                "função", "functie", "funkcja", "функция", "関数", "함수", "函数"
            ],
            "class": [
                "class", "clase", "classe", "klasse", "classe",
                "classe", "klasse", "klasa", "класс", "クラス", "클래스", "类"
            ],
            "method": [
                "method", "método", "méthode", "methode", "metodo",
                "método", "methode", "metoda", "метод", "メソッド", "메서드", "方法"
            ],
            "variable": [
                "variable", "variable", "variable", "variable", "variabile",
                "variável", "variabele", "zmienna", "переменная", "変数", "변수", "变量"
            ],
            "error": [
                "error", "error", "erreur", "fehler", "errore",
                "erro", "fout", "błąd", "ошибка", "エラー", "오류", "错误"
            ],
            "debug": [
                "debug", "depurar", "déboguer", "debuggen", "debug",
                "depurar", "debuggen", "debugować", "отладка", "デバッグ", "디버그", "调试"
            ]
        }
        
    async def detect_language(self, query: str) -> LanguageDetectionResult:
        """Detect the language of a query"""
        try:
            # Use langdetect for detection
            detected = detect_langs(query)
            
            if not detected:
                return LanguageDetectionResult(
                    detected_language="en",
                    confidence=0.5,
                    is_supported=True,
                    needs_translation=False
                )
            
            # Get the most likely language
            top_lang = detected[0]
            lang_code = top_lang.lang
            confidence = top_lang.prob
            
            # Check if it's a supported language
            is_supported = lang_code in self.supported_languages
            needs_translation = lang_code != "en" and is_supported
            
            logger.info(
                "language_detected",
                language=lang_code,
                confidence=confidence,
                is_supported=is_supported
            )
            
            return LanguageDetectionResult(
                detected_language=lang_code,
                confidence=confidence,
                is_supported=is_supported,
                needs_translation=needs_translation
            )
            
        except LangDetectException as e:
            logger.warning("language_detection_failed", error=str(e))
            # Default to English
            return LanguageDetectionResult(
                detected_language="en",
                confidence=0.5,
                is_supported=True,
                needs_translation=False
            )
            
    async def translate_query(
        self, 
        query: str, 
        source_language: str,
        preserve_code_terms: bool = True
    ) -> TranslationResult:
        """Translate query to English"""
        
        try:
            # Preserve programming terms if requested
            preserved_terms = {}
            if preserve_code_terms:
                query, preserved_terms = self._preserve_code_terms(query)
            
            # Translate using Google Translate
            translation = self.translator.translate(
                query,
                src=source_language,
                dest='en'
            )
            
            translated_text = translation.text
            
            # Restore preserved terms
            if preserved_terms:
                translated_text = self._restore_code_terms(
                    translated_text, 
                    preserved_terms
                )
            
            logger.info(
                "query_translated",
                source_lang=source_language,
                original_length=len(query),
                translated_length=len(translated_text)
            )
            
            return TranslationResult(
                original_query=query,
                translated_query=translated_text,
                source_language=source_language,
                confidence=translation.extra_data.get('confidence', 0.9)
            )
            
        except Exception as e:
            logger.error("translation_failed", error=str(e))
            # Return original query if translation fails
            return TranslationResult(
                original_query=query,
                translated_query=query,
                source_language=source_language,
                confidence=0.0
            )
            
    def _preserve_code_terms(self, query: str) -> Tuple[str, Dict[str, str]]:
        """Extract and preserve code terms during translation"""
        preserved = {}
        modified_query = query
        
        # Preserve function/method names (camelCase, snake_case)
        code_pattern = r'\b([a-zA-Z_][a-zA-Z0-9_]*(?:[A-Z][a-z]*)*)\b'
        matches = re.finditer(code_pattern, query)
        
        for i, match in enumerate(matches):
            term = match.group(1)
            # Check if it looks like code (has underscore or mixed case)
            if '_' in term or (term.lower() != term and term.upper() != term):
                placeholder = f"__CODE{i}__"
                preserved[placeholder] = term
                modified_query = modified_query.replace(term, placeholder)
        
        # Preserve quoted strings
        string_pattern = r'["\']([^"\']+)["\']'
        matches = re.finditer(string_pattern, modified_query)
        
        for i, match in enumerate(matches):
            term = match.group(0)
            placeholder = f"__STRING{i}__"
            preserved[placeholder] = term
            modified_query = modified_query.replace(term, placeholder)
            
        return modified_query, preserved
        
    def _restore_code_terms(self, text: str, preserved_terms: Dict[str, str]) -> str:
        """Restore preserved code terms after translation"""
        restored_text = text
        
        for placeholder, original in preserved_terms.items():
            restored_text = restored_text.replace(placeholder, original)
            
        return restored_text
        
    def enhance_query_with_translations(
        self, 
        query: str,
        detected_language: str
    ) -> str:
        """Enhance query by adding translated programming terms"""
        
        if detected_language == "en":
            return query
            
        enhanced_query = query
        
        # Add English equivalents of programming terms
        for eng_term, translations in self.programming_keywords.items():
            for translation in translations:
                if translation in query.lower() and translation != eng_term:
                    # Add English term as a synonym
                    enhanced_query += f" OR {eng_term}"
                    break
                    
        return enhanced_query
        
    def get_language_specific_tips(self, language: str) -> List[str]:
        """Get query tips for specific language"""
        
        tips_map = {
            "es": [
                "Usa 'función' para buscar funciones",
                "Usa 'clase' para buscar clases",
                "Puedes escribir consultas en español"
            ],
            "fr": [
                "Utilisez 'fonction' pour rechercher des fonctions",
                "Utilisez 'classe' pour rechercher des classes",
                "Vous pouvez écrire des requêtes en français"
            ],
            "de": [
                "Verwenden Sie 'Funktion' für Funktionen",
                "Verwenden Sie 'Klasse' für Klassen",
                "Sie können Anfragen auf Deutsch schreiben"
            ],
            "ja": [
                "関数を検索するには '関数' を使用してください",
                "クラスを検索するには 'クラス' を使用してください",
                "日本語でクエリを書くことができます"
            ],
            "ko": [
                "함수를 검색하려면 '함수'를 사용하세요",
                "클래스를 검색하려면 '클래스'를 사용하세요",
                "한국어로 쿼리를 작성할 수 있습니다"
            ],
            "zh-cn": [
                "使用 '函数' 搜索函数",
                "使用 '类' 搜索类",
                "您可以用中文编写查询"
            ]
        }
        
        return tips_map.get(language, [])
        

# Singleton instance
_language_support: Optional[MultiLanguageSupport] = None


def get_language_support() -> MultiLanguageSupport:
    """Get singleton language support instance"""
    global _language_support
    if _language_support is None:
        _language_support = MultiLanguageSupport()
    return _language_support