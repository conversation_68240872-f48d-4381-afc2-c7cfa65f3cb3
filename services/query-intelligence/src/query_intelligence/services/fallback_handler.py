"""
Fallback handlers for when services are unavailable
Provides graceful degradation of functionality
"""

import structlog
from typing import List, Dict, Any, Optional
import hashlib
import json

from ..models import (
    CodeChunk,
    SearchResult,
    GeneratedResponse,
    IntentAnalysis,
    QueryContext,
    EmbeddingVector
)
from ..utils.circuit_breaker import CircuitBreakerError

logger = structlog.get_logger()


class FallbackHandler:
    """Handles fallback logic when services are unavailable"""
    
    @staticmethod
    async def handle_llm_fallback(
        query: str,
        intent: IntentAnalysis,
        code_chunks: List[CodeChunk],
        context: QueryContext,
        error: Exception
    ) -> GeneratedResponse:
        """Fallback when LLM service is unavailable"""
        logger.warning(
            "llm_fallback_triggered",
            query=query,
            error=str(error)
        )
        
        # Generate a simple response based on code chunks
        if code_chunks:
            response_text = f"Based on the code analysis, here are relevant code sections for your query about {intent.primary_intent.value}:\n\n"
            
            for i, chunk in enumerate(code_chunks[:3], 1):
                response_text += f"{i}. {chunk.file_path} (lines {chunk.start_line}-{chunk.end_line}):\n"
                response_text += f"   {chunk.content[:200]}...\n\n"
            
            response_text += "\nNote: The AI response service is temporarily unavailable. The above code sections were found to be relevant to your query."
        else:
            response_text = "I found no relevant code sections for your query. The AI service is temporarily unavailable. Please try again later."
        
        return GeneratedResponse(
            text=response_text,
            confidence=0.3,  # Low confidence for fallback
            model_used="fallback",
            prompt_tokens=0,
            completion_tokens=0,
            generation_time_ms=0
        )
    
    @staticmethod
    async def handle_search_fallback(
        embedding: Any,
        repository_id: str,
        filters: Optional[Dict[str, Any]],
        limit: int,
        error: Exception
    ) -> SearchResult:
        """Fallback when search service is unavailable"""
        logger.warning(
            "search_fallback_triggered",
            repository_id=repository_id,
            error=str(error)
        )
        
        # Return empty result with explanation
        return SearchResult(
            chunks=[],
            total_results=0,
            search_time_ms=0,
            metadata={
                "fallback": True,
                "reason": "Search service temporarily unavailable"
            }
        )
    
    @staticmethod
    async def handle_cache_fallback(
        key: str,
        error: Exception
    ) -> Optional[str]:
        """Fallback when cache service is unavailable"""
        logger.warning(
            "cache_fallback_triggered",
            key=key,
            error=str(error)
        )
        
        # Return None to indicate cache miss
        return None
    
    @staticmethod
    def generate_cache_key(query: str, repository_id: str) -> str:
        """Generate a cache key for fallback scenarios"""
        key_data = f"{query}:{repository_id}"
        return f"query_cache:{hashlib.md5(key_data.encode()).hexdigest()}"
    
    @staticmethod
    async def handle_analysis_engine_fallback(
        repository_id: str,
        error: Exception
    ) -> Dict[str, Any]:
        """Fallback when analysis engine is unavailable"""
        logger.warning(
            "analysis_engine_fallback_triggered",
            repository_id=repository_id,
            error=str(error)
        )
        
        return {
            "status": "unavailable",
            "message": "Analysis engine temporarily unavailable",
            "fallback": True
        }


def with_fallback(fallback_handler_name: str):
    """
    Decorator to add fallback handling to async functions
    
    Args:
        fallback_handler_name: Name of the FallbackHandler method to use
        
    Example:
        @with_fallback("handle_llm_fallback")
        async def generate_response(...):
            ...
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except CircuitBreakerError as e:
                # Circuit breaker is open, use fallback
                handler = getattr(FallbackHandler, fallback_handler_name)
                return await handler(*args, error=e, **kwargs)
            except Exception as e:
                # Other errors might also trigger fallback
                if "timeout" in str(e).lower() or "connection" in str(e).lower():
                    handler = getattr(FallbackHandler, fallback_handler_name)
                    return await handler(*args, error=e, **kwargs)
                raise
        
        return wrapper
    return decorator