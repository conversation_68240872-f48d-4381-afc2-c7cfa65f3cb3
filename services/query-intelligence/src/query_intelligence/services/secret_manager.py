"""
Secret Manager Service for securely managing secrets
Integrates with GCP Secret Manager for production deployments
"""

import os
from typing import Optional, Dict, Any
from functools import lru_cache
import structlog

from ..config.settings import get_settings

logger = structlog.get_logger()
settings = get_settings()

class SecretManagerService:
    """Service for managing secrets from GCP Secret Manager"""
    
    def __init__(self):
        self.use_secret_manager = settings.USE_SECRET_MANAGER
        self.project_id = settings.SECRET_PROJECT_ID or settings.GCP_PROJECT_ID
        self._client = None
        
        if self.use_secret_manager:
            try:
                from google.cloud import secretmanager
                self._client = secretmanager.SecretManagerServiceClient()
                logger.info("secret_manager_initialized", project=self.project_id)
            except ImportError:
                logger.error("google_cloud_secretmanager_not_installed")
                raise ImportError("google-cloud-secret-manager package is required for Secret Manager integration")
            except Exception as e:
                logger.error("secret_manager_init_failed", error=str(e))
                raise
    
    @lru_cache(maxsize=32)
    def get_secret(self, secret_name: str, version: str = "latest") -> Optional[str]:
        """
        Retrieve a secret from GCP Secret Manager
        
        Args:
            secret_name: Name of the secret
            version: Version of the secret (default: latest)
            
        Returns:
            Secret value as string, or None if not found
        """
        if not self.use_secret_manager:
            logger.warning("secret_manager_disabled", secret_name=secret_name)
            return None
            
        if not self._client:
            logger.error("secret_manager_not_initialized")
            return None
            
        try:
            # Build the resource name
            name = f"projects/{self.project_id}/secrets/{secret_name}/versions/{version}"
            
            # Access the secret version
            response = self._client.access_secret_version(request={"name": name})
            secret_value = response.payload.data.decode("UTF-8")
            
            logger.info("secret_retrieved", secret_name=secret_name, version=version)
            return secret_value
            
        except Exception as e:
            logger.error("secret_retrieval_failed", 
                        secret_name=secret_name, 
                        version=version,
                        error=str(e))
            return None
    
    def get_secret_or_env(self, secret_name: str, env_var_name: str, default: Optional[str] = None) -> Optional[str]:
        """
        Get secret from Secret Manager, fallback to environment variable
        
        Args:
            secret_name: Name of the secret in Secret Manager
            env_var_name: Name of the environment variable as fallback
            default: Default value if neither secret nor env var is found
            
        Returns:
            Secret value, environment variable value, or default
        """
        # Try Secret Manager first
        if self.use_secret_manager:
            secret_value = self.get_secret(secret_name)
            if secret_value:
                return secret_value
                
        # Fallback to environment variable
        env_value = os.getenv(env_var_name)
        if env_value:
            return env_value
            
        # Return default
        return default
    
    def validate_production_secrets(self) -> Dict[str, Any]:
        """
        Validate that all required production secrets are available
        
        Returns:
            Dict with validation results
        """
        results = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        if not settings.is_production():
            results["warnings"].append("Not in production environment")
            return results
            
        # Check if Secret Manager is enabled in production
        if not self.use_secret_manager:
            results["valid"] = False
            results["errors"].append("Secret Manager must be enabled in production")
            
        # Check required secrets
        required_secrets = {
            "jwt-secret-key": "JWT_SECRET_KEY",
            "pinecone-api-key": "PINECONE_API_KEY",
        }
        
        for secret_name, env_var in required_secrets.items():
            value = self.get_secret_or_env(secret_name, env_var)
            if not value:
                results["valid"] = False
                results["errors"].append(f"Required secret '{secret_name}' not found")
            elif value == "CHANGE-THIS-USE-SECRET-MANAGER":
                results["valid"] = False
                results["errors"].append(f"Secret '{secret_name}' still has default value")
                
        return results
    
    def refresh_cache(self):
        """Clear the secret cache to force refresh"""
        self.get_secret.cache_clear()
        logger.info("secret_cache_cleared")


# Global instance
_secret_manager_service = None

def get_secret_manager_service() -> SecretManagerService:
    """Get the global secret manager service instance"""
    global _secret_manager_service
    if _secret_manager_service is None:
        _secret_manager_service = SecretManagerService()
    return _secret_manager_service


def get_jwt_secret() -> str:
    """Get JWT secret from Secret Manager or environment"""
    secret_service = get_secret_manager_service()
    secret = secret_service.get_secret_or_env(
        secret_name="jwt-secret-key",
        env_var_name="JWT_SECRET_KEY",
        default="CHANGE-THIS-USE-SECRET-MANAGER"
    )
    
    if secret == "CHANGE-THIS-USE-SECRET-MANAGER" and settings.is_production():
        raise ValueError("JWT secret key must be set in production")
        
    return secret


def get_pinecone_api_key() -> Optional[str]:
    """Get Pinecone API key from Secret Manager or environment"""
    secret_service = get_secret_manager_service()
    return secret_service.get_secret_or_env(
        secret_name="pinecone-api-key",
        env_var_name="PINECONE_API_KEY"
    )


def get_google_api_key() -> Optional[str]:
    """Get Google API key from Secret Manager or environment"""
    secret_service = get_secret_manager_service()
    return secret_service.get_secret_or_env(
        secret_name="google-api-key",
        env_var_name="GOOGLE_API_KEY"
    )