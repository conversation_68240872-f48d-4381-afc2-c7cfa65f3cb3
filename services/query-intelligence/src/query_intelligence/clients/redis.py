import redis.asyncio as redis
from functools import lru_cache
import structlog
from ..config.settings import get_settings
from ..utils.circuit_breaker import circuit_breaker, register_circuit_breaker

settings = get_settings()
logger = structlog.get_logger()


class RedisClient:
    def __init__(self):
        self.pool = redis.ConnectionPool.from_url(
            settings.REDIS_URL, max_connections=10
        )
        self._client = None

    async def _get_client(self):
        """Get or create Redis client"""
        if not self._client:
            self._client = redis.Redis(connection_pool=self.pool, decode_responses=True)
        return self._client

    @circuit_breaker(
        name="redis_get",
        failure_threshold=5,
        recovery_timeout=30,
        expected_exception=redis.RedisError
    )
    async def get(self, key: str) -> str:
        client = await self._get_client()
        return await client.get(key)

    @circuit_breaker(
        name="redis_set",
        failure_threshold=5,
        recovery_timeout=30,
        expected_exception=redis.RedisError
    )
    async def set(self, key: str, value: str, ex: int = 3600) -> bool:
        client = await self._get_client()
        return await client.set(key, value, ex=ex)

    @circuit_breaker(
        name="redis_delete",
        failure_threshold=5,
        recovery_timeout=30,
        expected_exception=redis.RedisError
    )
    async def delete(self, key: str) -> int:
        client = await self._get_client()
        return await client.delete(key)

    async def exists(self, key: str) -> bool:
        client = await self._get_client()
        return await client.exists(key)

    async def ttl(self, key: str) -> int:
        client = await self._get_client()
        return await client.ttl(key)

    async def keys(self, pattern: str) -> list:
        client = await self._get_client()
        return await client.keys(pattern)

    async def ping(self) -> bool:
        """Test Redis connection"""
        client = await self._get_client()
        return await client.ping()

    async def aclose(self):
        """Async close for compatibility"""
        await self.close()

    def pipeline(self):
        """Get Redis pipeline for atomic operations"""
        return redis.Redis(connection_pool=self.pool, decode_responses=True).pipeline()

    async def close(self):
        if self._client:
            await self._client.close()
            self._client = None


# Create singleton instance
_redis_client_instance = None


@lru_cache()
def get_redis_client() -> RedisClient:
    """Get singleton Redis client instance"""
    global _redis_client_instance
    if _redis_client_instance is None:
        _redis_client_instance = RedisClient()
        
        # Register circuit breakers
        if hasattr(_redis_client_instance.get, 'circuit_breaker'):
            register_circuit_breaker(_redis_client_instance.get.circuit_breaker)
        if hasattr(_redis_client_instance.set, 'circuit_breaker'):
            register_circuit_breaker(_redis_client_instance.set.circuit_breaker)
        if hasattr(_redis_client_instance.delete, 'circuit_breaker'):
            register_circuit_breaker(_redis_client_instance.delete.circuit_breaker)
            
    return _redis_client_instance


# For backward compatibility
redis_client = get_redis_client()
