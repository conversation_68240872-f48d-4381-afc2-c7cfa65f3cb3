"""
Configuration settings for Query Intelligence Service
Updated July 2025 for Google GenAI SDK migration
"""
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field
from functools import lru_cache
from typing import Optional


class Settings(BaseSettings):
    # Service Configuration
    PROJECT_NAME: str = "Query Intelligence Service"
    SERVICE_NAME: str = "query-intelligence"
    PORT: int = 8002
    ENVIRONMENT: str = Field(default="development", description="Environment (development/staging/production)")
    LOG_LEVEL: str = "INFO"

    # External Service URLs
    REDIS_URL: str = Field(default="redis://localhost:6379", description="Redis connection URL")
    ANALYSIS_ENGINE_URL: str = Field(default="http://localhost:8001", description="Analysis Engine service URL")
    PATTERN_MINING_URL: str = Field(default="http://localhost:8003", description="Pattern Mining service URL")
    COLLABORATION_URL: str = Field(default="http://localhost:8004", description="Collaboration service URL")

    # ===== CRITICAL: Google GenAI SDK Configuration (July 2025) =====
    # The Vertex AI SDK was deprecated on June 24, 2025
    USE_VERTEX_AI: bool = Field(
        default=True, 
        description="Use Vertex AI backend (True) or Gemini Developer API (False)"
    )
    
    # Model Configuration - Updated for Gemini 2.5 models
    GEMINI_MODEL_NAME: str = Field(
        default="gemini-2.5-flash",
        description="Primary model - use gemini-2.5-flash, gemini-2.5-flash-lite, or gemini-2.5-pro"
    )
    EMBEDDING_MODEL_NAME: str = Field(
        default="sentence-transformers/all-mpnet-base-v2",
        description="Model for generating embeddings"
    )
    
    # Model Routing Strategy
    USE_MODEL_ROUTING: bool = Field(
        default=True,
        description="Enable intelligent routing between model tiers"
    )
    SIMPLE_QUERY_MODEL: str = Field(
        default="gemini-2.5-flash-lite",
        description="Model for simple/fast queries"
    )
    COMPLEX_QUERY_MODEL: str = Field(
        default="gemini-2.5-pro",
        description="Model for complex analysis"
    )

    # GCP Configuration (for Vertex AI backend)
    GCP_PROJECT_ID: Optional[str] = Field(default=None, description="GCP project ID")
    GCP_REGION: str = Field(default="us-central1", description="GCP region")
    SERVICE_ACCOUNT_PATH: Optional[str] = Field(
        default=None,
        description="Path to service account JSON (for production)"
    )
    
    # Gemini Developer API Configuration (for development)
    GOOGLE_API_KEY: Optional[str] = Field(
        default=None,
        description="Gemini API key (for development only)"
    )

    # Pinecone Configuration
    PINECONE_API_KEY: Optional[str] = Field(default=None, description="Pinecone API key")
    PINECONE_INDEX_NAME: str = Field(default="ccl-code-embeddings", description="Pinecone index name")
    PINECONE_CLOUD: str = Field(default="aws", description="Pinecone cloud provider")
    PINECONE_REGION: str = Field(default="us-west-2", description="Pinecone region")

    # Cache Configuration
    CACHE_TTL_HOURS: int = Field(default=24, description="Cache TTL in hours")
    CACHE_MAX_SIZE: int = Field(default=10000, description="Maximum cache size")
    SEMANTIC_CACHE_ENABLED: bool = Field(
        default=True,
        description="Enable semantic caching for LLM responses"
    )

    # Performance Configuration
    MAX_QUERY_LENGTH: int = Field(default=10000, description="Maximum query length")
    MAX_CODE_CHUNKS: int = Field(default=20, description="Maximum code chunks to process")
    MAX_RESPONSE_TOKENS: int = Field(default=2048, description="Maximum response tokens")
    QUERY_TIMEOUT_SECONDS: int = Field(default=30, description="Query timeout in seconds")
    
    # Cloud Run Optimization
    MIN_INSTANCES: int = Field(default=5, description="Minimum Cloud Run instances")
    MAX_INSTANCES: int = Field(default=200, description="Maximum Cloud Run instances")
    CONCURRENCY: int = Field(default=20, description="Max concurrent requests per instance")
    CPU_BOOST_ENABLED: bool = Field(default=True, description="Enable startup CPU boost")

    # Security Configuration - CRITICAL: Use Secret Manager in production!
    JWT_SECRET_KEY: str = Field(
        default="CHANGE-THIS-USE-SECRET-MANAGER",
        description="JWT secret key - MUST use Secret Manager in production"
    )
    JWT_ALGORITHM: str = Field(default="HS256", description="JWT algorithm")
    JWT_EXPIRATION_MINUTES: int = Field(default=60, description="JWT expiration in minutes")
    API_KEY_HEADER: str = Field(default="X-API-Key", description="API key header name")
    
    # Secret Manager Configuration
    USE_SECRET_MANAGER: bool = Field(
        default=False,
        description="Use GCP Secret Manager for secrets"
    )
    SECRET_PROJECT_ID: Optional[str] = Field(
        default=None,
        description="Project ID for Secret Manager"
    )

    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = Field(default=100, description="Max requests per window")
    RATE_LIMIT_WINDOW_SECONDS: int = Field(default=60, description="Rate limit window")
    RATE_LIMIT_PER_USER: bool = Field(
        default=True,
        description="Apply rate limiting per user instead of per IP"
    )

    # Monitoring
    ENABLE_METRICS: bool = Field(default=True, description="Enable Prometheus metrics")
    METRICS_PORT: int = Field(default=9090, description="Metrics port")
    ENABLE_TRACING: bool = Field(default=True, description="Enable distributed tracing")
    
    # Security Features
    ENABLE_INPUT_VALIDATION: bool = Field(
        default=True,
        description="Enable input validation and sanitization"
    )
    ENABLE_PROMPT_INJECTION_DETECTION: bool = Field(
        default=True,
        description="Detect and prevent prompt injection attacks"
    )
    ENABLE_PII_DETECTION: bool = Field(
        default=True,
        description="Detect and redact PII in queries"
    )

    model_config = SettingsConfigDict(
        extra="ignore",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )

    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.ENVIRONMENT.lower() == "production"

    def validate_production_settings(self):
        """Validate critical settings for production"""
        if self.is_production():
            errors = []
            
            if self.JWT_SECRET_KEY == "CHANGE-THIS-USE-SECRET-MANAGER":
                errors.append("JWT_SECRET_KEY must be changed from default")
            
            if not self.USE_SECRET_MANAGER:
                errors.append("USE_SECRET_MANAGER must be enabled in production")
            
            if self.GOOGLE_API_KEY:
                errors.append("GOOGLE_API_KEY should not be used in production, use service accounts")
            
            if not self.SERVICE_ACCOUNT_PATH and self.USE_VERTEX_AI:
                errors.append("SERVICE_ACCOUNT_PATH required for Vertex AI in production")
            
            if errors:
                raise ValueError(f"Production configuration errors: {'; '.join(errors)}")
    
    def get_jwt_secret(self) -> str:
        """Get JWT secret from Secret Manager or environment"""
        if self.USE_SECRET_MANAGER:
            from ..services.secret_manager import get_jwt_secret
            return get_jwt_secret()
        return self.JWT_SECRET_KEY
    
    def get_pinecone_api_key(self) -> str:
        """Get Pinecone API key from Secret Manager or environment"""
        if self.USE_SECRET_MANAGER:
            from ..services.secret_manager import get_pinecone_api_key
            return get_pinecone_api_key()
        return self.PINECONE_API_KEY
    
    def get_google_api_key(self) -> str:
        """Get Google API key from Secret Manager or environment"""
        if self.USE_SECRET_MANAGER:
            from ..services.secret_manager import get_google_api_key
            return get_google_api_key()
        return self.GOOGLE_API_KEY


@lru_cache()
def get_settings():
    """Get cached settings instance"""
    settings = Settings()
    settings.validate_production_settings()
    return settings


# Keep backward compatibility
settings = get_settings()