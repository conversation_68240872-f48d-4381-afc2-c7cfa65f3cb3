# Query Intelligence Service - Production Readiness Plan

## Executive Summary
This plan outlines the steps needed to bring the Query Intelligence Service to production status in July 2025, including migration to the new Google GenAI SDK and addressing all production requirements.

## 🚨 Critical: SDK Migration (Priority 1)

### Current State
- Using deprecated Vertex AI SDK (deprecated June 24, 2025)
- Receiving deprecation warnings on all API calls
- SDK will stop working after June 24, 2026

### Migration to Google GenAI SDK

#### 1. Install New SDK
```bash
# Remove old SDK
poetry remove google-cloud-aiplatform

# Install new unified SDK
poetry add google-genai
```

#### 2. Update Service Implementation
```python
# Old (deprecated)
import vertexai
from vertexai.generative_models import GenerativeModel

# New (unified SDK)
import google.generativeai as genai
```

#### 3. Environment Configuration
```bash
# For Vertex AI (production)
export GOOGLE_CLOUD_PROJECT=vibe-match-463114
export GOOGLE_CLOUD_LOCATION=us-central1
export GOOGLE_GENAI_USE_VERTEXAI=true

# For development (Gemini API)
export GOOGLE_API_KEY=your-api-key
```

## 📋 Production Readiness Checklist

### Phase 1: Critical SDK & Model Updates (Week 1)

- [ ] **Migrate to Google GenAI SDK**
  - [ ] Update dependencies in pyproject.toml
  - [ ] Refactor LLMService to use new SDK
  - [ ] Update model initialization
  - [ ] Test with both Vertex AI and Gemini API endpoints

- [ ] **Model Selection & Testing**
  - [ ] Verify available models in July 2025
  - [ ] Test gemini-2.0-flash-exp availability
  - [ ] Implement fallback to gemini-1.5-pro if needed
  - [ ] Benchmark response times with new SDK

- [ ] **Authentication Updates**
  - [ ] Implement service account authentication
  - [ ] Support both ADC and service account JSON
  - [ ] Add authentication method selection via environment

### Phase 2: Code Quality & Testing (Week 1-2)

- [ ] **Achieve >90% Test Coverage**
  - [ ] Add tests for API endpoints (currently 0%)
  - [ ] Add tests for WebSocket handlers (currently 0%)
  - [ ] Add tests for metrics utilities (currently 0%)
  - [ ] Complete integration test fixes
  - [ ] Add end-to-end tests with new SDK

- [ ] **Error Handling & Resilience**
  - [ ] Implement retry logic for transient failures
  - [ ] Add circuit breakers for external services
  - [ ] Implement graceful degradation
  - [ ] Add comprehensive error logging

### Phase 3: Performance Optimization (Week 2)

- [ ] **Achieve <100ms Response Time**
  - [ ] Implement response streaming with new SDK
  - [ ] Optimize embedding generation
  - [ ] Tune Redis caching strategy
  - [ ] Implement connection pooling
  - [ ] Add request batching where applicable

- [ ] **Load Testing**
  - [ ] Create load test scenarios
  - [ ] Test with 100, 1000, 10000 concurrent users
  - [ ] Identify and fix bottlenecks
  - [ ] Document performance characteristics

### Phase 4: Security & Compliance (Week 2-3)

- [ ] **Security Hardening**
  - [ ] Implement request validation
  - [ ] Add input sanitization
  - [ ] Configure CORS properly
  - [ ] Add security headers
  - [ ] Implement rate limiting per user/IP

- [ ] **Secrets Management**
  - [ ] Move all secrets to GCP Secret Manager
  - [ ] Implement secret rotation
  - [ ] Remove hardcoded values
  - [ ] Add secret scanning to CI/CD

### Phase 5: Infrastructure & Deployment (Week 3)

- [ ] **Cloud Run Configuration**
  - [ ] Set appropriate resource limits
  - [ ] Configure autoscaling (min: 1, max: 100)
  - [ ] Set up health checks
  - [ ] Configure startup/liveness probes
  - [ ] Optimize cold start times

- [ ] **CI/CD Pipeline**
  - [ ] Complete Cloud Build configuration
  - [ ] Add automated testing
  - [ ] Implement staged rollouts
  - [ ] Add rollback mechanisms
  - [ ] Configure deployment notifications

- [ ] **Monitoring & Observability**
  - [ ] Set up Prometheus metrics
  - [ ] Create Grafana dashboards
  - [ ] Configure alerting rules
  - [ ] Set up structured logging
  - [ ] Implement distributed tracing

### Phase 6: Documentation & Operations (Week 3-4)

- [ ] **API Documentation**
  - [ ] Generate OpenAPI/Swagger docs
  - [ ] Create API usage examples
  - [ ] Document rate limits
  - [ ] Add authentication guide

- [ ] **Operational Runbooks**
  - [ ] Create incident response procedures
  - [ ] Document rollback procedures
  - [ ] Add troubleshooting guides
  - [ ] Create performance tuning guide

- [ ] **Production Checklist**
  - [ ] Database migration strategy
  - [ ] API versioning implementation
  - [ ] Feature flags setup
  - [ ] A/B testing framework

## 🎯 Success Criteria

1. **Performance**
   - P95 latency < 100ms for cached queries
   - P95 latency < 2s for uncached queries
   - Support 1000+ concurrent connections
   - 99.9% uptime SLA

2. **Quality**
   - >90% test coverage
   - 0 critical security vulnerabilities
   - All linting checks pass
   - Type checking enabled

3. **Operations**
   - Automated deployment pipeline
   - Real-time monitoring and alerting
   - Comprehensive logging
   - Easy rollback capability

## 📅 Timeline

- **Week 1**: SDK Migration & Critical Updates
- **Week 2**: Testing & Performance
- **Week 3**: Security & Infrastructure
- **Week 4**: Documentation & Go-Live

## 🚀 Go-Live Checklist

- [ ] All tests passing with >90% coverage
- [ ] Load testing completed successfully
- [ ] Security scan shows no critical issues
- [ ] Monitoring and alerting configured
- [ ] Documentation complete
- [ ] Runbooks created and tested
- [ ] Team trained on operations
- [ ] Rollback plan tested
- [ ] Performance targets met
- [ ] Stakeholder sign-off obtained

## 📊 Risk Mitigation

1. **SDK Deprecation Risk**: Migrate immediately to google-genai SDK
2. **Model Availability**: Implement multi-model fallback strategy
3. **Performance Risk**: Extensive caching and optimization
4. **Security Risk**: Regular scanning and penetration testing
5. **Operational Risk**: Comprehensive monitoring and runbooks

## Next Steps

1. **Immediate Action**: Start SDK migration today
2. **Set up development environment with new SDK**
3. **Create feature branch for migration**
4. **Begin systematic testing of all components**

---

**Note**: This plan assumes we're in July 2025, post-deprecation of the old Vertex AI SDK. The migration to google-genai SDK is critical and should be completed immediately.