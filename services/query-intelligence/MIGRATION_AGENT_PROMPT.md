# Google GenAI SDK Migration Agent Prompt

## Critical Context
The Vertex AI SDK generative AI module was **deprecated on June 24, 2025** (last month). The Query Intelligence service is currently using the deprecated SDK and requires immediate migration to the new unified Google GenAI SDK to prevent service failures.

## Your Mission
Implement the complete migration from the deprecated Vertex AI SDK to the new Google GenAI SDK following the comprehensive migration guide already prepared at `/services/query-intelligence/SDK_MIGRATION_GUIDE.md`.

## Current State Analysis
1. **Deprecated SDK in Use**: `google-cloud-aiplatform==1.99.0`
2. **Service Affected**: Query Intelligence (Python FastAPI service)
3. **Migration Guide**: Complete guide exists with all implementation details
4. **Code Status**: Still using deprecated imports and API calls

## Implementation Requirements

### Phase 1: Dependencies and Setup (30 minutes)
1. **Update Dependencies**:
   ```bash
   cd services/query-intelligence
   poetry remove google-cloud-aiplatform
   poetry add google-genai
   poetry update
   ```

2. **Verify New SDK Installation**:
   - Test import: `import google.generativeai as genai`
   - Check version compatibility
   - Ensure all dependencies resolve

### Phase 2: Core Service Migration (2 hours)
1. **Implement New LLMServiceV2**:
   - Create `src/query_intelligence/services/llm_service_v2.py`
   - Follow the complete implementation in the migration guide
   - Handle both Vertex AI and Developer API modes
   - Implement fallback logic for model selection

2. **Update Key Methods**:
   - `generate_response()` - Core response generation
   - `generate_json_response()` - JSON-formatted responses
   - `stream_response()` - Streaming responses
   - `analyze_intent()` - Intent analysis
   - All error handling and logging

3. **Configuration Updates**:
   - Update `src/query_intelligence/config/settings.py`
   - Add new environment variables:
     - `USE_VERTEX_AI`
     - `GOOGLE_API_KEY`
     - `GOOGLE_GENAI_USE_VERTEXAI`
     - `GEMINI_MODEL_NAME`

### Phase 3: Model Selection Strategy
**CRITICAL**: Address model selection concerns:

1. **Primary Model**: `gemini-2.0-flash-exp` (latest available)
2. **Fallback Model**: `gemini-1.5-pro` (stable)
3. **Model Logic**:
   ```python
   try:
       self.model = genai.GenerativeModel("gemini-2.0-flash-exp")
   except Exception:
       self.model = genai.GenerativeModel("gemini-1.5-pro")
   ```

4. **Environment Configuration**:
   - Production: Use Vertex AI backend with project authentication
   - Development: Use Developer API with API key
   - Staging: Configurable via environment variables

### Phase 4: Testing and Validation (1 hour)
1. **Create Test Script**:
   - Use the test script from migration guide
   - Test both JSON and text generation
   - Verify authentication works
   - Test streaming functionality

2. **Unit Test Updates**:
   - Update all test files to mock new SDK
   - Change import statements in tests
   - Update mock objects to match new API

3. **Integration Testing**:
   - Test with actual API calls
   - Verify performance is comparable
   - Test error handling scenarios

### Phase 5: Gradual Deployment (1-2 days)
1. **Feature Flag Implementation**:
   ```python
   USE_NEW_SDK = os.getenv("USE_NEW_SDK", "false").lower() == "true"
   
   if USE_NEW_SDK:
       from .llm_service_v2 import LLMServiceV2 as LLMService
   else:
       from .llm_service import LLMService
   ```

2. **Rollout Strategy**:
   - Start with `USE_NEW_SDK=false` (safety)
   - Test in development environment
   - Enable for 10% of traffic
   - Monitor metrics and errors
   - Gradually increase to 100%

## Critical Implementation Notes

### Authentication Handling
- **Vertex AI Mode**: Use project-based authentication
- **Developer API Mode**: Use API key authentication
- **Auto-detection**: Based on environment variables

### Error Handling
- Implement comprehensive error handling
- Add retry logic for transient failures
- Log all errors with context
- Graceful degradation when possible

### Performance Considerations
- Monitor token usage and costs
- Implement request timeout handling
- Add response caching where appropriate
- Track generation times and success rates

## Model Concerns Resolution
Based on the migration guide, the recommended model strategy is:

1. **Primary**: `gemini-2.0-flash-exp` (latest, fastest)
2. **Fallback**: `gemini-1.5-pro` (stable, reliable)
3. **Cost-effective**: `gemini-1.5-flash-8b` (for simple queries)

The new SDK allows dynamic model switching based on query complexity.

## Success Criteria
- [ ] All services start without errors
- [ ] All API endpoints respond correctly
- [ ] No performance degradation
- [ ] All tests pass
- [ ] Error rates remain stable
- [ ] Cost impact is neutral or positive

## Emergency Rollback Plan
If critical issues arise:
1. Set `USE_NEW_SDK=false`
2. Restart services immediately
3. Monitor for stability
4. Fix issues in new implementation
5. Re-attempt migration

## Files to Modify
1. `pyproject.toml` - Dependencies
2. `src/query_intelligence/services/llm_service_v2.py` - New service
3. `src/query_intelligence/config/settings.py` - Configuration
4. `tests/unit/test_llm_service.py` - Unit tests
5. `docker-compose.yml` - Environment variables
6. All files importing the old SDK

## Implementation Priority
**This is a P0 critical issue** - the deprecated SDK could stop working at any time. Prioritize this above all other work until completed.

Remember: The migration guide at `/services/query-intelligence/SDK_MIGRATION_GUIDE.md` contains all the detailed implementation code. Follow it precisely, test thoroughly, and deploy carefully with the feature flag approach.