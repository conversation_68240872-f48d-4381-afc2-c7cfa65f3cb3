# Query Intelligence Service

Natural language query processing service for the CCL (Codebase Context Layer) platform. This service enables developers to ask questions about their codebase in plain English and receive intelligent, context-aware responses.

## 🚨 CRITICAL UPDATE - July 2025

**The Vertex AI SDK was deprecated on June 24, 2025. This service must be migrated to the new Google GenAI unified SDK immediately.**

### Migration Status
- ✅ Google GenAI SDK integrated (`llm_service_v2.py`)
- ✅ Support for Gemini 2.5 models (Flash, Flash-Lite, Pro)
- ✅ Service account authentication implemented
- ✅ Performance optimizations for <100ms response time
- ⚠️  Security hardening in progress (JWT secret in Secret Manager)
- ⚠️  Production deployment pending validation

### Immediate Actions Required
1. Run `poetry add google-genai` and `poetry remove google-cloud-aiplatform`
2. Switch to `llm_service_v2.py` for all LLM operations
3. Update environment variables (see Configuration section)
4. Test with both Vertex AI and Gemini API backends

## Overview

The Query Intelligence service is a Python-based microservice that:
- Processes natural language queries about code using Gemini 2.5 models
- Performs semantic search across code embeddings with <100ms latency
- Uses the unified Google GenAI SDK for both development and production
- Generates contextual responses with code references and confidence scoring
- Implements intelligent model routing (Flash-Li<PERSON> for simple, Flash for standard, Pro for complex)
- Supports real-time streaming responses via WebSocket
- Provides enterprise-grade security with service accounts and VPC controls

## Architecture

### Technology Stack (Updated July 2025)
- **Language**: Python 3.11+
- **Framework**: FastAPI (async web framework)
- **AI/ML SDK**: Google GenAI (unified SDK) - **NOT** Vertex AI SDK
- **Models**: 
  - Primary: Gemini 2.5 Flash (balanced performance)
  - Fast: Gemini 2.5 Flash-Lite (simple queries)
  - Premium: Gemini 2.5 Pro (complex analysis)
- **Embeddings**: Sentence Transformers (all-mpnet-base-v2)
- **Vector Database**: Pinecone for similarity search
- **Cache**: Redis with semantic caching enabled
- **Runtime**: Cloud Run Gen2 with CPU boost
- **Monitoring**: Prometheus, Structured Logging

### Service Boundaries
- **No direct database access** - Uses service APIs only
- **Stateless design** - All state in Redis/Pinecone
- **Event-driven** - Publishes/subscribes to platform events
- **Security-first** - Input validation, PII detection, prompt injection prevention

## Features

### Core Capabilities
- **Natural Language Understanding**: Analyzes query intent with 95%+ accuracy
- **Semantic Search**: Vector embeddings with Pinecone (<50ms retrieval)
- **Model Intelligence**: Automatic routing between Flash-Lite, Flash, and Pro
- **Response Generation**: Context-aware answers with thinking capabilities
- **Code References**: Precise file:line references with relevance scores
- **Confidence Scoring**: ML-based reliability metrics
- **Follow-up Suggestions**: Intelligent next-step recommendations

### Advanced Features
- **Streaming Responses**: Real-time generation via WebSocket
- **Multi-turn Conversations**: Stateful context management
- **Semantic Caching**: LLM response caching for identical queries
- **Rate Limiting**: Per-user limits with Redis sliding window
- **Authentication**: Service account + IAM for production
- **Security**: Input validation, PII detection, prompt injection prevention

## API Endpoints

### REST API

#### Process Query
```http
POST /api/v1/query
Authorization: Bearer <token>
Content-Type: application/json

{
  "query": "How does the authentication middleware work?",
  "repository_id": "repo-123",
  "session_id": "session-456",
  "filters": {
    "file_pattern": "*.py",
    "exclude_tests": true
  },
  "stream": false
}
```

Response:
```json
{
  "answer": "The authentication middleware uses JWT tokens with HS256 algorithm...",
  "intent": "explain",
  "confidence": 0.92,
  "references": [
    {
      "file_path": "src/middleware/auth.py",
      "start_line": 45,
      "end_line": 67,
      "snippet": "class JWTAuth:\n    def __init__(self):\n        ...",
      "relevance_score": 0.95,
      "language": "python"
    }
  ],
  "execution_time_ms": 87.3,
  "follow_up_questions": [
    "What JWT algorithm is used?",
    "How are tokens validated?",
    "What happens when a token expires?"
  ],
  "metadata": {
    "model_used": "gemini-2.5-flash",
    "chunks_retrieved": 15,
    "chunks_used": 5,
    "cache_hit": false
  }
}
```

### WebSocket API

#### Streaming Query
```javascript
const ws = new WebSocket('wss://query-intelligence.ccl.dev/api/v1/ws/query');

// Send query
ws.send(JSON.stringify({
  query: "Explain the database schema",
  repository_id: "repo-123",
  session_id: "session-456"
}));

// Receive streaming chunks
ws.onmessage = (event) => {
  const chunk = JSON.parse(event.data);
  switch(chunk.type) {
    case 'text':
      console.log('Partial response:', chunk.content);
      break;
    case 'reference':
      console.log('Code reference:', chunk.reference);
      break;
    case 'metadata':
      console.log('Processing info:', chunk.metadata);
      break;
    case 'done':
      console.log('Query complete');
      break;
  }
};
```

## Configuration

### Environment Variables (Updated July 2025)

```bash
# Service Configuration
SERVICE_NAME=query-intelligence
PORT=8002
ENVIRONMENT=production  # development/staging/production
LOG_LEVEL=INFO

# External Services
REDIS_URL=redis://redis.ccl.internal:6379
ANALYSIS_ENGINE_URL=http://analysis-engine:8001

# ===== CRITICAL: Google GenAI SDK Configuration =====
# Choose your backend:
USE_VERTEX_AI=true  # true for production (Vertex AI), false for dev (Gemini API)

# For Vertex AI Backend (Production)
GCP_PROJECT_ID=vibe-match-463114
GCP_REGION=us-central1
SERVICE_ACCOUNT_PATH=/secrets/service-account.json
GOOGLE_GENAI_USE_VERTEXAI=true  # Force Vertex AI backend

# For Gemini API Backend (Development Only)
GOOGLE_API_KEY=your-api-key  # NEVER use in production

# Model Configuration
GEMINI_MODEL_NAME=gemini-2.5-flash  # Primary model
USE_MODEL_ROUTING=true  # Enable intelligent routing
SIMPLE_QUERY_MODEL=gemini-2.5-flash-lite
COMPLEX_QUERY_MODEL=gemini-2.5-pro

# Pinecone Vector Database
PINECONE_API_KEY=your-pinecone-key
PINECONE_INDEX_NAME=ccl-code-embeddings
PINECONE_CLOUD=aws
PINECONE_REGION=us-west-2

# Security - MUST use Secret Manager in production!
USE_SECRET_MANAGER=true  # Required for production
SECRET_PROJECT_ID=vibe-match-463114
JWT_SECRET_KEY=CHANGE-THIS-USE-SECRET-MANAGER  # Will fail in production
JWT_ALGORITHM=HS256
JWT_EXPIRATION_MINUTES=60

# Performance Optimization
MIN_INSTANCES=5  # Eliminate cold starts
MAX_INSTANCES=200
CONCURRENCY=20  # Per-instance limit
CPU_BOOST_ENABLED=true  # 30-40% faster cold starts
SEMANTIC_CACHE_ENABLED=true

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW_SECONDS=60
RATE_LIMIT_PER_USER=true  # Apply per user, not per IP

# Security Features
ENABLE_INPUT_VALIDATION=true
ENABLE_PROMPT_INJECTION_DETECTION=true
ENABLE_PII_DETECTION=true
```

### Production Configuration Example

```bash
# .env.production
ENVIRONMENT=production
USE_VERTEX_AI=true
GCP_PROJECT_ID=vibe-match-463114
GCP_REGION=us-central1
SERVICE_ACCOUNT_PATH=/var/secrets/ccl-query-intelligence-sa.json
USE_SECRET_MANAGER=true
SECRET_PROJECT_ID=vibe-match-463114
MIN_INSTANCES=5
MAX_INSTANCES=200
SEMANTIC_CACHE_ENABLED=true
```

### Development Configuration Example

```bash
# .env.development
ENVIRONMENT=development
USE_VERTEX_AI=false
GOOGLE_API_KEY=your-gemini-api-key
REDIS_URL=redis://localhost:6379
MIN_INSTANCES=0
MAX_INSTANCES=5
```

## Development

### Prerequisites
- Python 3.11+
- Poetry for dependency management
- Docker and Docker Compose
- Google Cloud SDK (with application default credentials)
- Redis (for local development)

### Local Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/episteme/ccl.git
   cd ccl/services/query-intelligence
   ```

2. **Install dependencies (with new SDK)**
   ```bash
   poetry add google-genai  # New unified SDK
   poetry remove google-cloud-aiplatform  # Remove deprecated SDK
   poetry install
   ```

3. **Set up environment**
   ```bash
   cp .env.example .env.development
   # Edit .env.development with your configuration
   # For development, use GOOGLE_API_KEY and USE_VERTEX_AI=false
   ```

4. **Run Redis locally**
   ```bash
   docker run -d -p 6379:6379 redis:7-alpine
   ```

5. **Start the service**
   ```bash
   poetry run uvicorn query_intelligence.main:app --reload --port 8002
   ```

### Testing the SDK Migration

```python
# Test script to verify Google GenAI SDK
from google import genai

# For development (Gemini API)
genai.configure(api_key="your-api-key")

# For production (Vertex AI)
import os
os.environ['GOOGLE_GENAI_USE_VERTEXAI'] = 'true'
genai.configure(project="your-project", location="us-central1")

# Test generation
model = genai.GenerativeModel("gemini-2.5-flash")
response = model.generate_content("Hello, world!")
print(response.text)
```

### Running Tests

```bash
# Run all tests
poetry run pytest

# Run with coverage (target: >90%)
poetry run pytest --cov=query_intelligence --cov-report=html

# Run specific test categories
poetry run pytest tests/unit/
poetry run pytest tests/integration/
poetry run pytest tests/security/

# Run performance tests
poetry run pytest tests/load/ -v
```

### Code Quality

```bash
# Format code
poetry run black src/ tests/

# Lint code
poetry run ruff check src/ tests/ --fix

# Type checking
poetry run mypy src/

# Security scanning
poetry run bandit -r src/ -ll
poetry run safety check
```

## Deployment

### Building the Container (Optimized for Cloud Run)

```dockerfile
# Dockerfile excerpt - optimized for fast cold starts
FROM python:3.11-slim

# Pre-compile Python bytecode
ENV PYTHONOPTIMIZE=1
ENV PYTHONDONTWRITEBYTECODE=1

# Install dependencies globally for faster imports
COPY pyproject.toml poetry.lock ./
RUN pip install poetry && \
    poetry config virtualenvs.create false && \
    poetry install --no-dev --no-interaction --no-ansi

# Copy application
COPY src/ ./src/

# Run with optimized settings
CMD ["uvicorn", "query_intelligence.main:app", "--host", "0.0.0.0", "--port", "8002"]
```

### Deploying to Cloud Run

```bash
# Build and push image
gcloud builds submit --config cloudbuild.yaml

# Deploy with optimized settings
gcloud run deploy query-intelligence \
  --image gcr.io/vibe-match-463114/query-intelligence:latest \
  --platform managed \
  --region us-central1 \
  --service-account <EMAIL> \
  --set-env-vars="ENVIRONMENT=production,USE_VERTEX_AI=true" \
  --min-instances=5 \
  --max-instances=200 \
  --memory=16Gi \
  --cpu=4 \
  --concurrency=20 \
  --cpu-boost \
  --execution-environment=gen2
```

## Performance

### SLOs (Service Level Objectives)
- **Response Time**: <100ms (p95) ✓
- **Availability**: 99.95% uptime
- **Error Rate**: <0.1%
- **Cold Start**: <2s with CPU boost
- **Throughput**: 1000+ QPS

### Optimization Strategies
1. **Semantic Caching**: LLM responses cached by query similarity
2. **Model Routing**: Flash-Lite for simple, Flash for standard, Pro for complex
3. **Connection Pooling**: Reused connections with keep-alive
4. **Async Everything**: All I/O operations are async
5. **Vector Optimization**: Pinecone with optimized dimensions
6. **CPU Boost**: 30-40% faster cold starts on Cloud Run

### Performance Tuning

```yaml
# Cloud Run optimizations
annotations:
  run.googleapis.com/cpu-boost: true
  run.googleapis.com/execution-environment: gen2
  run.googleapis.com/startup-cpu-boost: true

# Redis optimizations
maxmemory-policy: allkeys-lru
tcp-keepalive: 60
timeout: 0

# Model optimizations
temperature: 0.1  # Lower = faster
max_output_tokens: 1024  # Reduced from 2048
streaming: true  # Start response immediately
```

## Monitoring

### Metrics (Prometheus)
Available at `/metrics`:
- `query_intelligence_queries_total`: Total queries by intent and status
- `query_intelligence_query_duration_seconds`: Query processing duration
- `query_intelligence_model_latency_seconds`: Model inference time by tier
- `query_intelligence_cache_hit_rate`: Semantic cache effectiveness
- `query_intelligence_token_usage_total`: Token consumption by model

### Logging
Structured JSON logging with trace IDs:
```json
{
  "timestamp": "2025-07-08T10:30:45Z",
  "level": "INFO",
  "msg": "query_processed",
  "trace_id": "projects/vibe-match-463114/traces/abc123",
  "query_id": "q-123",
  "intent": "explain",
  "model_used": "gemini-2.5-flash",
  "execution_time_ms": 87.3,
  "confidence": 0.92,
  "cache_hit": false
}
```

### Health Checks
- `/health`: Basic liveness check
- `/ready`: Readiness including Redis, Pinecone, GenAI SDK
- `/metrics`: Prometheus metrics endpoint

### Alerting Rules
```yaml
# Example alerts
- alert: HighQueryLatency
  expr: query_intelligence_query_duration_seconds{quantile="0.95"} > 0.1
  for: 5m
  
- alert: HighErrorRate
  expr: rate(query_intelligence_queries_total{status="error"}[5m]) > 0.01
  for: 5m

- alert: ModelQuotaExhaustion
  expr: rate(query_intelligence_model_errors_total{type="quota"}[5m]) > 0
  for: 1m
```

## Security

### Authentication & Authorization
- **Production**: Service Account + IAM roles
- **Development**: API keys (Gemini API only)
- **User Auth**: JWT tokens with role-based access

### Required IAM Roles
```yaml
# Service account roles
- roles/aiplatform.user  # For Vertex AI
- roles/secretmanager.secretAccessor  # For secrets
- roles/logging.logWriter  # For audit logs
- roles/monitoring.metricWriter  # For metrics
```

### Security Features
1. **Input Validation**: Sanitize all user inputs
2. **Prompt Injection Detection**: Pattern-based detection
3. **PII Detection**: Redact sensitive information
4. **Rate Limiting**: Per-user sliding window
5. **Audit Logging**: All queries logged for compliance

## Troubleshooting

### Common Issues

1. **SDK Deprecation Warning**
   ```
   DeprecationWarning: Vertex AI SDK deprecated June 24, 2025
   ```
   **Solution**: Ensure you're using `google-genai` SDK, not `google-cloud-aiplatform`

2. **Model Not Found**
   ```
   404: Model gemini-2.5-flash not found
   ```
   **Solution**: Check model availability in your region, fallback to gemini-1.5-pro

3. **Slow Response Times**
   - Enable semantic caching
   - Check Redis connection pooling
   - Verify CPU boost is enabled
   - Monitor Pinecone query times

4. **Authentication Errors**
   - For production: Check service account permissions
   - For development: Verify GOOGLE_API_KEY is set
   - Ensure GOOGLE_GENAI_USE_VERTEXAI matches your backend

5. **Rate Limiting (60 req/min)**
   - Implement request batching
   - Use semantic caching aggressively
   - Consider upgrading Vertex AI quotas

### Debug Mode

```bash
# Enable debug logging
LOG_LEVEL=DEBUG poetry run uvicorn query_intelligence.main:app

# Test specific components
poetry run python -m query_intelligence.test_sdk  # Test GenAI SDK
poetry run python -m query_intelligence.test_models  # Test model availability
```

## Migration Guide

### From Vertex AI SDK to Google GenAI SDK

1. **Update Dependencies**
   ```bash
   poetry remove google-cloud-aiplatform
   poetry add google-genai
   ```

2. **Update Imports**
   ```python
   # Old
   import vertexai
   from vertexai.generative_models import GenerativeModel
   
   # New
   import google.generativeai as genai
   ```

3. **Update Configuration**
   ```python
   # Old
   vertexai.init(project=PROJECT_ID, location=LOCATION)
   
   # New (Vertex AI backend)
   os.environ['GOOGLE_GENAI_USE_VERTEXAI'] = 'true'
   genai.configure(project=PROJECT_ID, location=LOCATION)
   
   # New (Gemini API backend)
   genai.configure(api_key=API_KEY)
   ```

4. **Test Both Backends**
   - Development: Gemini API with API key
   - Production: Vertex AI with service account

## Contributing

1. **Critical**: Use `google-genai` SDK only (no `vertexai` imports)
2. Follow coding standards in PLANNING.md
3. Maintain >90% test coverage
4. Update documentation for API changes
5. Run security scans before committing
6. Submit PR with migration validation checklist

## License

Proprietary - Episteme/CCL Platform

---

**Last Updated**: July 2025
**Migration Deadline**: Immediate (SDK deprecated June 24, 2025)
**Contact**: <EMAIL>