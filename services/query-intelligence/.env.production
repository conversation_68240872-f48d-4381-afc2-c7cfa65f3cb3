# Production Environment Configuration
# Query Intelligence Service - July 2025

# Service Configuration
SERVICE_NAME=query-intelligence
PORT=8002
ENVIRONMENT=production
LOG_LEVEL=INFO

# External Services (Production URLs)
REDIS_URL=redis://redis.ccl.internal:6379
ANALYSIS_ENGINE_URL=http://analysis-engine:8001
PATTERN_MINING_URL=http://pattern-mining:8003
COLLABORATION_URL=http://collaboration:8004

# Google GenAI SDK Configuration (Vertex AI Backend)
USE_VERTEX_AI=true
GCP_PROJECT_ID=vibe-match-463114
GCP_REGION=us-central1
SERVICE_ACCOUNT_PATH=/var/secrets/ccl-query-intelligence-sa.json

# Model Configuration
GEMINI_MODEL_NAME=gemini-2.5-flash
USE_MODEL_ROUTING=true
SIMPLE_QUERY_MODEL=gemini-2.5-flash  # Flash-lite not available
COMPLEX_QUERY_MODEL=gemini-2.5-pro

# Pinecone Vector Database (Production)
# These should be loaded from Secret Manager
PINECONE_API_KEY=SECRET_MANAGER:pinecone-api-key
PINECONE_INDEX_NAME=ccl-code-embeddings-prod
PINECONE_CLOUD=aws
PINECONE_REGION=us-west-2

# Security - Using Secret Manager
USE_SECRET_MANAGER=true
SECRET_PROJECT_ID=vibe-match-463114
JWT_SECRET_KEY=SECRET_MANAGER:jwt-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRATION_MINUTES=60

# Performance Optimization (Production Settings)
MIN_INSTANCES=5  # Eliminate cold starts
MAX_INSTANCES=200
CONCURRENCY=20
CPU_BOOST_ENABLED=true
SEMANTIC_CACHE_ENABLED=true

# Cache Configuration
CACHE_TTL_HOURS=24
CACHE_MAX_SIZE=10000

# Rate Limiting (Production)
RATE_LIMIT_REQUESTS=1000  # Higher for production
RATE_LIMIT_WINDOW_SECONDS=60
RATE_LIMIT_PER_USER=true

# Security Features (All enabled for production)
ENABLE_INPUT_VALIDATION=true
ENABLE_PROMPT_INJECTION_DETECTION=true
ENABLE_PII_DETECTION=true

# Monitoring (Production)
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_TRACING=true

# Performance Settings
MAX_QUERY_LENGTH=10000
MAX_CODE_CHUNKS=20
MAX_RESPONSE_TOKENS=2048
QUERY_TIMEOUT_SECONDS=30