import pytest
import json
from unittest.mock import patch, As<PERSON><PERSON><PERSON>, <PERSON><PERSON>
from fastapi.testclient import <PERSON><PERSON><PERSON>

from query_intelligence.main import app
from query_intelligence.models import (
    QueryIntent,
    IntentAnalysis,
    CodeChunk,
    SearchResult,
    CodeReference,
)


@pytest.fixture
def test_client():
    return TestClient(app)


@pytest.fixture
def mock_query_processor():
    mock = Mock()
    mock._create_context = Mock()
    mock._analyze_intent = AsyncMock()
    mock.semantic_search = Mock()
    mock.semantic_search.generate_embedding = AsyncMock()
    mock.semantic_search.search = AsyncMock()
    mock._build_search_filters = Mock()
    mock._rerank_chunks = AsyncMock()
    mock._extract_references = Mock()
    mock.llm_service = Mock()
    mock.llm_service.stream_response = AsyncMock()
    mock._generate_follow_ups = AsyncMock()
    return mock


class TestWebSocketAPI:

    def test_websocket_connection(self, test_client):
        with test_client.websocket_connect("/api/v1/ws/query") as websocket:
            # Connection should be established
            assert websocket is not None

    def test_websocket_query_success(self, test_client, mock_query_processor):
        # Setup mocks
        mock_context = Mock()
        mock_query_processor._create_context.return_value = mock_context

        mock_intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            code_elements=["auth"],
            scope="repository",
            context_depth="normal",
            confidence=0.9,
        )
        mock_query_processor._analyze_intent.return_value = mock_intent

        mock_query_processor.semantic_search.generate_embedding.return_value = [
            0.1,
            0.2,
            0.3,
        ]

        mock_chunks = [
            CodeChunk(
                file_path="src/auth.py",
                start_line=10,
                end_line=20,
                content="auth code",
                language="python",
                similarity_score=0.9,
                recency_score=0.8,
                combined_score=0.85,
            )
        ]
        mock_query_processor.semantic_search.search.return_value = SearchResult(
            chunks=mock_chunks, total_results=1, search_time_ms=50.0
        )

        mock_query_processor._build_search_filters.return_value = {}
        mock_query_processor._rerank_chunks.return_value = mock_chunks

        mock_references = [
            CodeReference(
                file_path="src/auth.py",
                start_line=10,
                end_line=20,
                snippet="auth code",
                relevance_score=0.85,
            )
        ]
        mock_query_processor._extract_references.return_value = mock_references

        # Setup streaming response
        async def mock_stream():
            yield "Authentication works by "
            yield "validating JWT tokens..."

        mock_query_processor.llm_service.stream_response.return_value = mock_stream()
        mock_query_processor._generate_follow_ups.return_value = [
            "How are tokens validated?"
        ]

        with patch(
            "query_intelligence.api.websocket.get_query_processor",
            return_value=mock_query_processor,
        ):
            with test_client.websocket_connect("/api/v1/ws/query") as websocket:
                # Send query
                websocket.send_text(
                    json.dumps(
                        {
                            "query": "How does authentication work?",
                            "repository_id": "test-repo",
                        }
                    )
                )

                # Receive acknowledgment
                data = websocket.receive_json()
                assert data["type"] == "acknowledged"
                assert data["query"] == "How does authentication work?"

                # Receive processing started
                data = websocket.receive_json()
                assert data["type"] == "processing_started"

                # Receive intent analysis
                data = websocket.receive_json()
                assert data["type"] == "intent_analyzed"
                assert data["intent"] == "explain"
                assert data["confidence"] == 0.9

                # Receive search status
                data = websocket.receive_json()
                assert data["type"] == "status"
                assert "Searching" in data["message"]

                # Receive search complete
                data = websocket.receive_json()
                assert data["type"] == "search_complete"
                assert data["results_found"] == 1

                # Receive reference
                data = websocket.receive_json()
                assert data["type"] == "reference"
                assert data["reference"]["file_path"] == "src/auth.py"

                # Receive generation status
                data = websocket.receive_json()
                assert data["type"] == "status"
                assert "Generating" in data["message"]

                # Receive text chunks
                data = websocket.receive_json()
                assert data["type"] == "text"
                assert data["content"] == "Authentication works by "

                data = websocket.receive_json()
                assert data["type"] == "text"
                assert data["content"] == "validating JWT tokens..."

                # Receive completion
                data = websocket.receive_json()
                assert data["type"] == "done"
                assert data["done"] is True
                assert "follow_up_questions" in data["metadata"]

    def test_websocket_invalid_json(self, test_client):
        with test_client.websocket_connect("/api/v1/ws/query") as websocket:
            # Send invalid JSON
            websocket.send_text("invalid json")

            # Should receive error
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert "Invalid JSON" in data["message"]

    def test_websocket_missing_fields(self, test_client):
        with test_client.websocket_connect("/api/v1/ws/query") as websocket:
            # Send incomplete request
            websocket.send_text(
                json.dumps(
                    {
                        "query": "Test query"
                        # Missing repository_id
                    }
                )
            )

            # Should receive error
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert "Query processing error" in data["message"]

    def test_websocket_processing_error(self, test_client, mock_query_processor):
        # Setup error
        mock_query_processor._analyze_intent.side_effect = Exception(
            "Processing failed"
        )

        with patch(
            "query_intelligence.api.websocket.get_query_processor",
            return_value=mock_query_processor,
        ):
            with test_client.websocket_connect("/api/v1/ws/query") as websocket:
                # Send query
                websocket.send_text(
                    json.dumps({"query": "Test query", "repository_id": "test-repo"})
                )

                # Skip acknowledgment
                websocket.receive_json()

                # Should receive error
                data = websocket.receive_json()
                assert data["type"] == "error"
                assert "Streaming error" in data["message"]
