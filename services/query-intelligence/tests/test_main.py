from fastapi.testclient import TestClient
from query_intelligence.main import app
from unittest.mock import patch

client = TestClient(app)


def test_health_check():
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}


@patch("query_intelligence.services.query_processor.QueryProcessor.process_query")
def test_query_endpoint(mock_process_query):
    mock_process_query.return_value = {
        "answer": "mocked response",
        "intent": "EXPLAIN",
    }

    response = client.post(
        "/query",
        json={"query": "test query", "repository_id": "test_repo"},
        headers={"Authorization": "Bearer some-token"},
    )

    assert response.status_code == 200
    data = response.json()
    assert data["answer"] == "mocked response"
    assert data["intent"] == "EXPLAIN"
