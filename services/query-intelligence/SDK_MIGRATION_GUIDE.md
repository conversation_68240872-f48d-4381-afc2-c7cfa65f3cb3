# Google GenAI SDK Migration Guide

## Overview
This guide provides step-by-step instructions for migrating from the deprecated Vertex AI SDK to the new unified Google GenAI SDK (July 2025).

## Why Migrate?

1. **Deprecation**: The Vertex AI SDK generative AI module was deprecated on June 24, 2025
2. **Unified Interface**: Single SDK works with both Gemini Developer API and Vertex AI
3. **Better Features**: Enhanced streaming, function calling, and model management
4. **Future Proof**: Continued support and updates

## Migration Steps

### Step 1: Update Dependencies

```bash
# Remove old dependencies
poetry remove google-cloud-aiplatform

# Add new unified SDK
poetry add google-genai

# Update other dependencies
poetry update
```

### Step 2: Update Imports

#### Old Code (Vertex AI SDK)
```python
import vertexai
from vertexai.generative_models import GenerativeModel, GenerationConfig
from google.auth import default
```

#### New Code (Google GenAI SDK)
```python
import google.generativeai as genai
from google.generativeai.types import GenerationConfig
import google.auth
```

### Step 3: Update LLMService Implementation

Create a new file: `src/query_intelligence/services/llm_service_v2.py`

```python
import asyncio
import json
import time
from typing import List, Dict, Any

import structlog
import google.generativeai as genai
from google.generativeai.types import GenerationConfig
import google.auth

from ..models import (
    QueryContext,
    IntentAnalysis,
    CodeChunk,
    GeneratedResponse,
)
from ..config.settings import get_settings

logger = structlog.get_logger()
settings = get_settings()


class LLMServiceV2:
    """Service for LLM interactions using Google GenAI SDK"""

    def __init__(self):
        # Configure based on environment
        if settings.USE_VERTEX_AI:
            # Use Vertex AI backend
            import os
            os.environ['GOOGLE_GENAI_USE_VERTEXAI'] = 'true'
            
            # Get project info
            credentials, project = google.auth.default()
            
            # Configure with project
            genai.configure(
                project=settings.GCP_PROJECT_ID or project,
                location=settings.GCP_REGION or "us-central1",
                credentials=credentials
            )
        else:
            # Use Gemini Developer API (for development)
            genai.configure(api_key=settings.GOOGLE_API_KEY)

        # Initialize model
        model_name = settings.GEMINI_MODEL_NAME or "gemini-2.0-flash-exp"
        
        # Check if model exists, fallback if needed
        try:
            self.model = genai.GenerativeModel(model_name)
            logger.info("model_initialized", model=model_name)
        except Exception as e:
            logger.warning("model_init_failed", model=model_name, error=str(e))
            # Fallback to stable model
            self.model = genai.GenerativeModel("gemini-1.5-pro")
            logger.info("model_fallback", model="gemini-1.5-pro")

        # Generation configs
        self.default_config = GenerationConfig(
            temperature=0.3,
            max_output_tokens=2048,
            top_p=0.95,
            top_k=40,
        )

        self.json_config = GenerationConfig(
            temperature=0.1,
            max_output_tokens=1024,
            response_mime_type="application/json",
        )

    async def generate_response(
        self,
        query: str,
        intent: IntentAnalysis,
        code_chunks: List[CodeChunk],
        context: QueryContext,
    ) -> GeneratedResponse:
        """Generate response based on query and code context"""
        start_time = time.time()

        try:
            # Build prompt
            prompt = self._build_response_prompt(query, intent, code_chunks, context)

            # Generate response
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt,
                generation_config=self.default_config
            )

            generation_time_ms = (time.time() - start_time) * 1000

            # Calculate confidence
            confidence = self._calculate_confidence(response, code_chunks, intent)

            # Extract token counts if available
            usage = response.usage_metadata if hasattr(response, 'usage_metadata') else None
            
            return GeneratedResponse(
                text=response.text,
                confidence=confidence,
                model_used=self.model.model_name,
                prompt_tokens=usage.prompt_token_count if usage else None,
                completion_tokens=usage.candidates_token_count if usage else None,
                generation_time_ms=generation_time_ms,
            )

        except Exception as e:
            logger.error("response_generation_failed", error=str(e), exc_info=True)
            raise

    async def generate_json_response(self, prompt: str) -> Dict[str, Any]:
        """Generate a JSON response from the LLM"""
        try:
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt,
                generation_config=self.json_config
            )

            # Parse JSON response
            try:
                return json.loads(response.text)
            except json.JSONDecodeError:
                # Try to extract JSON from the response
                text = response.text.strip()
                if text.startswith("```json"):
                    text = text[7:]
                if text.endswith("```"):
                    text = text[:-3]
                return json.loads(text.strip())

        except Exception as e:
            logger.error("json_generation_failed", error=str(e), exc_info=True)
            return {}

    async def stream_response(
        self,
        query: str,
        intent: IntentAnalysis,
        code_chunks: List[CodeChunk],
        context: QueryContext,
    ):
        """Stream response generation for real-time output"""
        prompt = self._build_response_prompt(query, intent, code_chunks, context)

        # Use streaming generation
        response = await asyncio.to_thread(
            self.model.generate_content,
            prompt,
            generation_config=self.default_config,
            stream=True,
        )

        # Yield chunks as they come
        async for chunk in response:
            if chunk.text:
                yield chunk.text

    # ... (rest of the methods remain the same)
```

### Step 4: Update Environment Configuration

#### For Production (Vertex AI)
```bash
# .env.production
GOOGLE_CLOUD_PROJECT=vibe-match-463114
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_GENAI_USE_VERTEXAI=true
USE_VERTEX_AI=true
GEMINI_MODEL_NAME=gemini-2.0-flash-exp
```

#### For Development (Gemini API)
```bash
# .env.development
GOOGLE_API_KEY=your-api-key-here
USE_VERTEX_AI=false
GEMINI_MODEL_NAME=gemini-2.0-flash-exp
```

### Step 5: Update Settings

```python
# src/query_intelligence/config/settings.py
class Settings(BaseSettings):
    # ... existing settings ...
    
    # New settings for GenAI SDK
    USE_VERTEX_AI: bool = Field(default=True, description="Use Vertex AI backend")
    GOOGLE_API_KEY: Optional[str] = Field(default=None, description="Gemini API key for development")
    GOOGLE_GENAI_USE_VERTEXAI: Optional[str] = Field(default=None, description="Force Vertex AI backend")
```

### Step 6: Test Migration

```python
# test_migration.py
import asyncio
from query_intelligence.services.llm_service_v2 import LLMServiceV2

async def test_new_sdk():
    service = LLMServiceV2()
    
    # Test JSON generation
    prompt = "Return a JSON object with a 'status' field set to 'ok'"
    result = await service.generate_json_response(prompt)
    print(f"JSON Response: {result}")
    
    # Test text generation
    from query_intelligence.models import IntentAnalysis, QueryIntent, QueryContext
    
    intent = IntentAnalysis(
        primary_intent=QueryIntent.EXPLAIN,
        confidence=0.9
    )
    
    context = QueryContext(
        repository_id="test",
        user_id="test"
    )
    
    response = await service.generate_response(
        "What is a REST API?",
        intent,
        [],
        context
    )
    print(f"Text Response: {response.text[:200]}...")
    print(f"Model Used: {response.model_used}")

if __name__ == "__main__":
    asyncio.run(test_new_sdk())
```

### Step 7: Gradual Rollout

1. **Create feature flag**:
```python
USE_NEW_SDK = os.getenv("USE_NEW_SDK", "false").lower() == "true"

if USE_NEW_SDK:
    from .llm_service_v2 import LLMServiceV2 as LLMService
else:
    from .llm_service import LLMService
```

2. **Test in development first**
3. **Enable for 10% of production traffic**
4. **Monitor metrics and errors**
5. **Gradually increase to 100%**

### Step 8: Update Tests

Update all test files to mock the new SDK:

```python
@patch("google.generativeai.GenerativeModel")
async def test_llm_service(mock_model):
    mock_instance = Mock()
    mock_model.return_value = mock_instance
    
    # Mock response
    mock_response = Mock()
    mock_response.text = "Test response"
    mock_response.usage_metadata = Mock(
        prompt_token_count=10,
        candidates_token_count=20
    )
    
    mock_instance.generate_content.return_value = mock_response
    
    # Test service
    service = LLMServiceV2()
    # ... rest of test
```

## Common Issues and Solutions

### Issue 1: Authentication Errors
**Solution**: Ensure GOOGLE_GENAI_USE_VERTEXAI is set correctly

### Issue 2: Model Not Found
**Solution**: Use fallback model logic as shown above

### Issue 3: Different Response Format
**Solution**: Add compatibility layer to handle both old and new formats

### Issue 4: Streaming Changes
**Solution**: Update async iteration for new streaming API

## Rollback Plan

If issues arise:

1. Set `USE_NEW_SDK=false` environment variable
2. Restart services
3. Monitor for stability
4. Fix issues in new SDK implementation
5. Re-attempt migration

## Verification Checklist

- [ ] All unit tests pass with new SDK
- [ ] Integration tests pass
- [ ] Performance metrics are comparable or better
- [ ] No increase in error rates
- [ ] Cost is similar or lower
- [ ] All features work correctly

## References

- [Google GenAI SDK Documentation](https://cloud.google.com/vertex-ai/generative-ai/docs/sdks/overview)
- [Migration Guide](https://cloud.google.com/vertex-ai/generative-ai/docs/deprecations/genai-vertexai-sdk)
- [Unified SDK Announcement](https://cloud.google.com/blog/products/ai-machine-learning/google-genai-unified-sdk)