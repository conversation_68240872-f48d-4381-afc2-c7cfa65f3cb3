{"data_mtime": 1752049734, "dep_lines": [22, 23, 25, 26, 27, 29, 30, 39, 21, 28, 29, 40, 41, 42, 43, 44, 45, 46, 47, 15, 16, 17, 18, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 34], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 25, 10, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 10, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["torch.distributed.fsdp", "torch.utils.data", "transformers.generation.configuration_utils", "transformers.integrations.deepspeed", "transformers.integrations.fsdp", "transformers.utils.logging", "transformers.utils.deprecation", "transformers.data.data_collator", "torch.nn", "transformers.trainer", "transformers.utils", "transformers.feature_extraction_utils", "transformers.image_processing_utils", "transformers.modeling_utils", "transformers.processing_utils", "transformers.tokenization_utils_base", "transformers.trainer_callback", "transformers.trainer_utils", "transformers.training_args", "contextlib", "copy", "pathlib", "typing", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "logging", "numpy", "torch._C", "torch._C._distributed_c10d", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.distributed", "torch.distributed._composable_state", "torch.distributed.device_mesh", "torch.distributed.fsdp._common_utils", "torch.distributed.fsdp.api", "torch.distributed.fsdp.fully_sharded_data_parallel", "torch.distributed.fsdp.wrap", "torch.nn.modules", "torch.nn.modules.module", "torch.nn.parameter", "torch.optim", "torch.optim.lr_scheduler", "torch.optim.optimizer", "torch.utils", "torch.utils._contextlib", "torch.utils.data.dataset", "transformers.data", "transformers.generation", "transformers.image_processing_base", "transformers.integrations", "transformers.integrations.peft", "transformers.trainer_pt_utils", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "072609df5fce029445df84a60b09ddf87354bece", "id": "transformers.trainer_seq2seq", "ignore_all": true, "interface_hash": "8e45f3677d0877559a949f8571123257ced90ba1", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/trainer_seq2seq.py", "plugin_data": null, "size": 17961, "suppressed": ["datasets"], "version_id": "1.16.1"}