{"data_mtime": 1752049733, "dep_lines": [2114, 2133, 38, 68, 69, 3977, 27, 34, 37, 38, 3977, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 33, 34, 36, 74, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 78, 742, 76, 78, 91, 742], "dep_prios": [20, 20, 10, 5, 5, 20, 5, 10, 5, 5, 20, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 10, 20, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 20, 20, 5, 20], "dependencies": ["transformers.models.auto.configuration_auto", "transformers.models.auto.tokenization_auto", "transformers.utils.logging", "transformers.utils.chat_template_utils", "transformers.utils.import_utils", "transformers.models.auto", "collections.abc", "packaging.version", "transformers.dynamic_module_utils", "transformers.utils", "transformers.models", "copy", "json", "os", "re", "warnings", "collections", "contextlib", "dataclasses", "pathlib", "typing", "numpy", "packaging", "transformers", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "_warnings", "abc", "enum", "logging", "torch._C", "torch._tensor", "torch.types", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "7dbaa9370cf18a80e79434f6dfe0638793888afd", "id": "transformers.tokenization_utils_base", "ignore_all": true, "interface_hash": "75aedcc8648c9c4b59a4de58463ccff6ece1fde7", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/tokenization_utils_base.py", "plugin_data": null, "size": 207164, "suppressed": ["jax.numpy", "mlx.core", "tensorflow", "jax", "tokenizers", "mlx"], "version_id": "1.16.1"}