{"data_mtime": 1752049733, "dep_lines": [33, 48, 23, 32, 34, 35, 36, 48, 19, 20, 21, 22, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 26, 29, 30, 26], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 5, 5, 5], "dependencies": ["transformers.integrations.ggml", "transformers.utils.logging", "collections.abc", "transformers.convert_slow_tokenizer", "transformers.modeling_gguf_pytorch_utils", "transformers.tokenization_utils", "transformers.tokenization_utils_base", "transformers.utils", "copy", "json", "os", "collections", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "json.decoder", "logging", "transformers.integrations", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "f47b8dcb507018c48f2b4f20c135d1d1513375e7", "id": "transformers.tokenization_utils_fast", "ignore_all": true, "interface_hash": "eaa253f5bfd4690066937f21e3381719b42d8d51", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/tokenization_utils_fast.py", "plugin_data": null, "size": 41370, "suppressed": ["tokenizers.pre_tokenizers", "tokenizers.decoders", "tokenizers.trainers", "tokenizers"], "version_id": "1.16.1"}