{"data_mtime": 1752049734, "dep_lines": [28, 35, 36, 37, 27, 34, 35, 17, 18, 19, 20, 21, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 25, 24], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10], "dependencies": ["transformers.models.auto", "transformers.utils.logging", "transformers.generation.configuration_utils", "transformers.generation.tf_logits_process", "transformers.modeling_tf_outputs", "transformers.tf_utils", "transformers.utils", "copy", "inspect", "warnings", "dataclasses", "typing", "numpy", "builtins", "_collections_abc", "_frozen_importlib", "_warnings", "abc", "collections", "logging", "numpy._core", "numpy._core.numerictypes", "numpy._typing", "numpy._typing._dtype_like", "transformers.configuration_utils", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "08e33e549cc2b0d26b5aa0a12c2c532458072b7f", "id": "transformers.generation.tf_utils", "ignore_all": true, "interface_hash": "7f41ea4f00554ec839f48dda8d840a8254687919", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/generation/tf_utils.py", "plugin_data": null, "size": 175687, "suppressed": ["tensorflow.compiler.tf2xla.python.xla", "tensorflow"], "version_id": "1.16.1"}