{"data_mtime": 1752049734, "dep_lines": [27, 28, 34, 23, 26, 27, 16, 17, 18, 19, 21, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 10, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.generation.configuration_utils", "transformers.generation.logits_process", "torch.nn", "transformers.modeling_utils", "transformers.utils", "collections", "dataclasses", "functools", "typing", "numpy", "torch", "builtins", "_frozen_importlib", "abc", "logging", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.modules", "torch.nn.modules.module", "torch.nn.parameter", "transformers.configuration_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "6de47fd3faa814e132d73ce2118ef35129b1f6c1", "id": "transformers.generation.watermarking", "ignore_all": true, "interface_hash": "529e3c23150dc05039260c9d52c52d2377fe113d", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/generation/watermarking.py", "plugin_data": null, "size": 24540, "suppressed": [], "version_id": "1.16.1"}