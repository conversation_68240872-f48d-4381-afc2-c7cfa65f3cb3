{".class": "MypyFile", "_fullname": "transformers.optimization", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Adafactor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.optim.optimizer.Optimizer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.optimization.Adafactor", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.optimization.Adafactor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.optimization", "mro": ["transformers.optimization.Adafactor", "torch.optim.optimizer.Optimizer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "params", "lr", "eps", "clip_threshold", "decay_rate", "beta1", "weight_decay", "scale_parameter", "relative_step", "warmup_init"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization.Adafactor.__init__", "name": "__init__", "type": null}}, "_approx_sq_grad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["exp_avg_sq_row", "exp_avg_sq_col"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.optimization.Adafactor._approx_sq_grad", "name": "_approx_sq_grad", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.optimization.Adafactor._approx_sq_grad", "name": "_approx_sq_grad", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["exp_avg_sq_row", "exp_avg_sq_col"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_approx_sq_grad of Adafactor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_lr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["param_group", "param_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.optimization.Adafactor._get_lr", "name": "_get_lr", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.optimization.Adafactor._get_lr", "name": "_get_lr", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["param_group", "param_state"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_lr of Adafactor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["param_group", "param_shape"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.optimization.Adafactor._get_options", "name": "_get_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.optimization.Adafactor._get_options", "name": "_get_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["param_group", "param_shape"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_options of Adafactor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_rms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.optimization.Adafactor._rms", "name": "_rms", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.optimization.Adafactor._rms", "name": "_rms", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tensor"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_rms of Adafactor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "closure"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.optimization.Adafactor.step", "name": "step", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.optimization.Adafactor.step", "name": "step", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "closure"], "arg_types": ["transformers.optimization.Adafactor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "step of <PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.optimization.Adafactor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.optimization.Adafactor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AdafactorSchedule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.optim.lr_scheduler.LambdaLR"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.optimization.AdafactorSchedule", "name": "AdafactorSchedule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.optimization.AdafactorSchedule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.optimization", "mro": ["transformers.optimization.AdafactorSchedule", "torch.optim.lr_scheduler.LambdaLR", "torch.optim.lr_scheduler.LRScheduler", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "optimizer", "initial_lr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization.AdafactorSchedule.__init__", "name": "__init__", "type": null}}, "get_lr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization.AdafactorSchedule.get_lr", "name": "get_lr", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.optimization.AdafactorSchedule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.optimization.AdafactorSchedule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LambdaLR": {".class": "SymbolTableNode", "cross_ref": "torch.optim.lr_scheduler.LambdaLR", "kind": "Gdef"}, "LayerWiseDummyOptimizer": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.LayerWiseDummyOptimizer", "kind": "Gdef"}, "LayerWiseDummyScheduler": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.LayerWiseDummyScheduler", "kind": "Gdef"}, "Optimizer": {".class": "SymbolTableNode", "cross_ref": "torch.optim.optimizer.Optimizer", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ReduceLROnPlateau": {".class": "SymbolTableNode", "cross_ref": "torch.optim.lr_scheduler.ReduceLROnPlateau", "kind": "Gdef"}, "SchedulerType": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.SchedulerType", "kind": "Gdef"}, "TYPE_TO_SCHEDULER_FUNCTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.optimization.TYPE_TO_SCHEDULER_FUNCTION", "name": "TYPE_TO_SCHEDULER_FUNCTION", "setter_type": null, "type": {".class": "Instance", "args": ["transformers.trainer_utils.SchedulerType", "builtins.function"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.optimization.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.optimization.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.optimization.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.optimization.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.optimization.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.optimization.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_get_constant_lambda": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization._get_constant_lambda", "name": "_get_constant_lambda", "type": null}}, "_get_constant_schedule_with_warmup_lr_lambda": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["current_step", "num_warmup_steps"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization._get_constant_schedule_with_warmup_lr_lambda", "name": "_get_constant_schedule_with_warmup_lr_lambda", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["current_step", "num_warmup_steps"], "arg_types": ["builtins.int", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_constant_schedule_with_warmup_lr_lambda", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_cosine_schedule_with_warmup_lr_lambda": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3], "arg_names": ["current_step", "num_warmup_steps", "num_training_steps", "num_cycles"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization._get_cosine_schedule_with_warmup_lr_lambda", "name": "_get_cosine_schedule_with_warmup_lr_lambda", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3], "arg_names": ["current_step", "num_warmup_steps", "num_training_steps", "num_cycles"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.float"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_cosine_schedule_with_warmup_lr_lambda", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_cosine_with_hard_restarts_schedule_with_warmup_lr_lambda": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3], "arg_names": ["current_step", "num_warmup_steps", "num_training_steps", "num_cycles"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization._get_cosine_with_hard_restarts_schedule_with_warmup_lr_lambda", "name": "_get_cosine_with_hard_restarts_schedule_with_warmup_lr_lambda", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3], "arg_names": ["current_step", "num_warmup_steps", "num_training_steps", "num_cycles"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_cosine_with_hard_restarts_schedule_with_warmup_lr_lambda", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_inverse_sqrt_schedule_lr_lambda": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": ["current_step", "num_warmup_steps", "timescale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization._get_inverse_sqrt_schedule_lr_lambda", "name": "_get_inverse_sqrt_schedule_lr_lambda", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": ["current_step", "num_warmup_steps", "timescale"], "arg_types": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_inverse_sqrt_schedule_lr_lambda", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_linear_schedule_with_warmup_lr_lambda": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": ["current_step", "num_warmup_steps", "num_training_steps"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization._get_linear_schedule_with_warmup_lr_lambda", "name": "_get_linear_schedule_with_warmup_lr_lambda", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["current_step", "num_warmup_steps", "num_training_steps"], "arg_types": ["builtins.int", "builtins.int", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_linear_schedule_with_warmup_lr_lambda", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_polynomial_decay_schedule_with_warmup_lr_lambda": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 3], "arg_names": ["current_step", "num_warmup_steps", "num_training_steps", "lr_end", "power", "lr_init"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization._get_polynomial_decay_schedule_with_warmup_lr_lambda", "name": "_get_polynomial_decay_schedule_with_warmup_lr_lambda", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3], "arg_names": ["current_step", "num_warmup_steps", "num_training_steps", "lr_end", "power", "lr_init"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_polynomial_decay_schedule_with_warmup_lr_lambda", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_wsd_scheduler_lambda": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["current_step", "num_warmup_steps", "num_stable_steps", "num_decay_steps", "warmup_type", "decay_type", "min_lr_ratio", "num_cycles"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization._get_wsd_scheduler_lambda", "name": "_get_wsd_scheduler_lambda", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["current_step", "num_warmup_steps", "num_stable_steps", "num_decay_steps", "warmup_type", "decay_type", "min_lr_ratio", "num_cycles"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.str", "builtins.float", "builtins.float"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_wsd_scheduler_lambda", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_adafactor_schedule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["optimizer", "initial_lr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization.get_adafactor_schedule", "name": "get_adafactor_schedule", "type": null}}, "get_constant_schedule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["optimizer", "last_epoch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization.get_constant_schedule", "name": "get_constant_schedule", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["optimizer", "last_epoch"], "arg_types": ["torch.optim.optimizer.Optimizer", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_constant_schedule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_constant_schedule_with_warmup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["optimizer", "num_warmup_steps", "last_epoch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization.get_constant_schedule_with_warmup", "name": "get_constant_schedule_with_warmup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["optimizer", "num_warmup_steps", "last_epoch"], "arg_types": ["torch.optim.optimizer.Optimizer", "builtins.int", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_constant_schedule_with_warmup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cosine_schedule_with_warmup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["optimizer", "num_warmup_steps", "num_training_steps", "num_cycles", "last_epoch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization.get_cosine_schedule_with_warmup", "name": "get_cosine_schedule_with_warmup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["optimizer", "num_warmup_steps", "num_training_steps", "num_cycles", "last_epoch"], "arg_types": ["torch.optim.optimizer.Optimizer", "builtins.int", "builtins.int", "builtins.float", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_cosine_schedule_with_warmup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cosine_with_hard_restarts_schedule_with_warmup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["optimizer", "num_warmup_steps", "num_training_steps", "num_cycles", "last_epoch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization.get_cosine_with_hard_restarts_schedule_with_warmup", "name": "get_cosine_with_hard_restarts_schedule_with_warmup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["optimizer", "num_warmup_steps", "num_training_steps", "num_cycles", "last_epoch"], "arg_types": ["torch.optim.optimizer.Optimizer", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_cosine_with_hard_restarts_schedule_with_warmup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cosine_with_min_lr_schedule_with_warmup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["optimizer", "num_warmup_steps", "num_training_steps", "num_cycles", "last_epoch", "min_lr", "min_lr_rate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization.get_cosine_with_min_lr_schedule_with_warmup", "name": "get_cosine_with_min_lr_schedule_with_warmup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["optimizer", "num_warmup_steps", "num_training_steps", "num_cycles", "last_epoch", "min_lr", "min_lr_rate"], "arg_types": ["torch.optim.optimizer.Optimizer", "builtins.int", "builtins.int", "builtins.float", "builtins.int", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_cosine_with_min_lr_schedule_with_warmup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_inverse_sqrt_schedule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["optimizer", "num_warmup_steps", "timescale", "last_epoch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization.get_inverse_sqrt_schedule", "name": "get_inverse_sqrt_schedule", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["optimizer", "num_warmup_steps", "timescale", "last_epoch"], "arg_types": ["torch.optim.optimizer.Optimizer", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_inverse_sqrt_schedule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_linear_schedule_with_warmup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["optimizer", "num_warmup_steps", "num_training_steps", "last_epoch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization.get_linear_schedule_with_warmup", "name": "get_linear_schedule_with_warmup", "type": null}}, "get_polynomial_decay_schedule_with_warmup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["optimizer", "num_warmup_steps", "num_training_steps", "lr_end", "power", "last_epoch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization.get_polynomial_decay_schedule_with_warmup", "name": "get_polynomial_decay_schedule_with_warmup", "type": null}}, "get_reduce_on_plateau_schedule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["optimizer", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization.get_reduce_on_plateau_schedule", "name": "get_reduce_on_plateau_schedule", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["optimizer", "kwargs"], "arg_types": ["torch.optim.optimizer.Optimizer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_reduce_on_plateau_schedule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_scheduler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["name", "optimizer", "num_warmup_steps", "num_training_steps", "scheduler_specific_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization.get_scheduler", "name": "get_scheduler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["name", "optimizer", "num_warmup_steps", "num_training_steps", "scheduler_specific_kwargs"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "transformers.trainer_utils.SchedulerType"], "uses_pep604_syntax": false}, "torch.optim.optimizer.Optimizer", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_scheduler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_wsd_schedule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["optimizer", "num_warmup_steps", "num_decay_steps", "num_training_steps", "num_stable_steps", "warmup_type", "decay_type", "min_lr_ratio", "num_cycles", "last_epoch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.optimization.get_wsd_schedule", "name": "get_wsd_schedule", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["optimizer", "num_warmup_steps", "num_decay_steps", "num_training_steps", "num_stable_steps", "warmup_type", "decay_type", "min_lr_ratio", "num_cycles", "last_epoch"], "arg_types": ["torch.optim.optimizer.Optimizer", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.float", "builtins.float", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_wsd_schedule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.optimization.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/optimization.py"}