{"data_mtime": 1752049733, "dep_lines": [39, 38, 41, 43, 59, 27, 36, 37, 42, 43, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30, 31, 32, 34, 35, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1179, 56, 1179, 56, 1179], "dep_prios": [5, 5, 5, 10, 5, 5, 10, 10, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 20, 20, 20], "dependencies": ["torch.utils.data.distributed", "torch.utils.data", "transformers.integrations.deepspeed", "transformers.utils.logging", "torch.optim.lr_scheduler", "collections.abc", "torch.distributed", "torch.nn", "transformers.tokenization_utils_base", "transformers.utils", "copy", "datetime", "io", "json", "math", "os", "re", "sys", "warnings", "contextlib", "dataclasses", "itertools", "logging", "typing", "numpy", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "_warnings", "abc", "collections", "enum", "functools", "numpy._typing", "numpy._typing._ufunc", "torch._C", "torch._C._VariableFunctions", "torch._C._distributed_c10d", "torch._tensor", "torch.distributed.distributed_c10d", "torch.optim", "torch.optim.optimizer", "torch.types", "torch.utils", "torch.utils.data.dataset", "torch.utils.data.sampler", "transformers.utils.generic", "transformers.utils.import_utils", "types"], "hash": "417e580eeb7f9ed8008e147a0f999c8539984760", "id": "transformers.trainer_pt_utils", "ignore_all": true, "interface_hash": "00eeebaf1d002682aabd2572fd5dfca9bd16aad0", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/trainer_pt_utils.py", "plugin_data": null, "size": 61747, "suppressed": ["smdistributed.modelparallel.torch", "torch_xla.runtime", "smdistributed.modelparallel", "torch_xla", "smdistributed"], "version_id": "1.16.1"}