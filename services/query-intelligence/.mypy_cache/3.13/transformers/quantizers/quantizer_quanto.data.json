{".class": "MypyFile", "_fullname": "transformers.quantizers.quantizer_quanto", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "HfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.base.HfQuantizer", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.PreTrainedModel", "kind": "Gdef"}, "QuantoConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.QuantoConfig", "kind": "Gdef"}, "QuantoHfQuantizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.quantizers.base.HfQuantizer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer", "name": "QuantoHfQuantizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "transformers.quantizers.quantizer_quanto", "mro": ["transformers.quantizers.quantizer_quanto.QuantoHfQuantizer", "transformers.quantizers.base.HfQuantizer", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "quantization_config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "quantization_config", "kwargs"], "arg_types": ["transformers.quantizers.quantizer_quanto.QuantoHfQuantizer", "transformers.utils.quantization_config.QuantoConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of QuantoHfQuantizer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_model_after_weight_loading": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer._process_model_after_weight_loading", "name": "_process_model_after_weight_loading", "type": null}}, "_process_model_before_weight_loading": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "model", "keep_in_fp32_modules", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer._process_model_before_weight_loading", "name": "_process_model_before_weight_loading", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "model", "keep_in_fp32_modules", "kwargs"], "arg_types": ["transformers.quantizers.quantizer_quanto.QuantoHfQuantizer", "transformers.modeling_utils.PreTrainedModel", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_process_model_before_weight_loading of QuantoHfQuantizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "adjust_max_memory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_memory"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer.adjust_max_memory", "name": "adjust_max_memory", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_memory"], "arg_types": ["transformers.quantizers.quantizer_quanto.QuantoHfQuantizer", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "adjust_max_memory of QuantoHfQuantizer", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "adjust_target_dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "target_dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer.adjust_target_dtype", "name": "adjust_target_dtype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "target_dtype"], "arg_types": ["transformers.quantizers.quantizer_quanto.QuantoHfQuantizer", "torch._C.dtype"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "adjust_target_dtype of QuantoHfQuantizer", "ret_type": "torch._C.dtype", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_quantized_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "model", "param_value", "param_name", "state_dict", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer.check_quantized_param", "name": "check_quantized_param", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "model", "param_value", "param_name", "state_dict", "kwargs"], "arg_types": ["transformers.quantizers.quantizer_quanto.QuantoHfQuantizer", "transformers.modeling_utils.PreTrainedModel", "torch._tensor.Tensor", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "check_quantized_param of QuantoHfQuantizer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_quantized_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 2, 4], "arg_names": ["self", "model", "param_value", "param_name", "target_device", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer.create_quantized_param", "name": "create_quantized_param", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 2, 4], "arg_names": ["self", "model", "param_value", "param_name", "target_device", "args", "kwargs"], "arg_types": ["transformers.quantizers.quantizer_quanto.QuantoHfQuantizer", "transformers.modeling_utils.PreTrainedModel", "torch._tensor.Tensor", "builtins.str", "torch._C.device", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_quantized_param of QuantoHfQuantizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_serializable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "safe_serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer.is_serializable", "name": "is_serializable", "type": null}}, "is_trainable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer.is_trainable", "name": "is_trainable", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "model"], "arg_types": ["transformers.quantizers.quantizer_quanto.QuantoHfQuantizer", {".class": "UnionType", "items": ["transformers.modeling_utils.PreTrainedModel", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_trainable of QuantoHfQuantizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer.is_trainable", "name": "is_trainable", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "model"], "arg_types": ["transformers.quantizers.quantizer_quanto.QuantoHfQuantizer", {".class": "UnionType", "items": ["transformers.modeling_utils.PreTrainedModel", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_trainable of QuantoHfQuantizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer.post_init", "name": "post_init", "type": null}}, "required_packages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer.required_packages", "name": "required_packages", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "requires_calibration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer.requires_calibration", "name": "requires_calibration", "setter_type": null, "type": "builtins.bool"}}, "requires_parameters_quantization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer.requires_parameters_quantization", "name": "requires_parameters_quantization", "setter_type": null, "type": "builtins.bool"}}, "update_device_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "device_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer.update_device_map", "name": "update_device_map", "type": null}}, "update_missing_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "missing_keys", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer.update_missing_keys", "name": "update_missing_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "missing_keys", "prefix"], "arg_types": ["transformers.quantizers.quantizer_quanto.QuantoHfQuantizer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_missing_keys of QuantoHfQuantizer", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_torch_dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "torch_dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer.update_torch_dtype", "name": "update_torch_dtype", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "torch_dtype"], "arg_types": ["transformers.quantizers.quantizer_quanto.QuantoHfQuantizer", "torch._C.dtype"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_torch_dtype of QuantoHfQuantizer", "ret_type": "torch._C.dtype", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validate_environment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer.validate_environment", "name": "validate_environment", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.quantizer_quanto.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.quantizer_quanto.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.quantizer_quanto.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.quantizer_quanto.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.quantizer_quanto.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.quantizer_quanto.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "get_module_from_name": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizers_utils.get_module_from_name", "kind": "Gdef"}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef"}, "is_accelerate_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_accelerate_available", "kind": "Gdef"}, "is_optimum_quanto_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_optimum_quanto_available", "kind": "Gdef"}, "is_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_available", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.quantizers.quantizer_quanto.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "version": {".class": "SymbolTableNode", "cross_ref": "packaging.version", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/quantizers/quantizer_quanto.py"}