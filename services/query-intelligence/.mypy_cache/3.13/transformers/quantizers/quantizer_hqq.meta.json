{"data_mtime": 1752049733, "dep_lines": [18, 19, 20, 17, 18, 24, 15, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 118, 28], "dep_prios": [10, 5, 5, 5, 5, 25, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 5], "dependencies": ["transformers.utils.logging", "transformers.quantizers.base", "transformers.quantizers.quantizers_utils", "transformers.integrations", "transformers.utils", "transformers.modeling_utils", "typing", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "logging", "torch._C", "torch._tensor", "torch.cuda", "torch.cuda.memory", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.nn.parameter", "torch.types", "transformers.configuration_utils", "transformers.integrations.peft", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "abd76bc93dd57530ba3bf9c01896c032491c5f79", "id": "transformers.quantizers.quantizer_hqq", "ignore_all": true, "interface_hash": "31f03079a8ac9d2d1fa6221708f6e56b02b48864", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/quantizers/quantizer_hqq.py", "plugin_data": null, "size": 13175, "suppressed": ["hqq.core.quantize", "accelerate.hooks"], "version_id": "1.16.1"}