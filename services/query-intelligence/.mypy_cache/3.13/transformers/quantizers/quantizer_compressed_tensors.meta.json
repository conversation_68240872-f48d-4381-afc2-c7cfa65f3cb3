{"data_mtime": 1752049733, "dep_lines": [19, 20, 21, 19, 16, 17, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 52, 118], "dep_prios": [10, 5, 5, 5, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20], "dependencies": ["transformers.utils.logging", "transformers.utils.quantization_config", "transformers.quantizers.base", "transformers.utils", "os", "re", "torch", "builtins", "_frozen_importlib", "abc", "genericpath", "logging", "torch._C", "transformers.utils.hub", "transformers.utils.import_utils", "typing"], "hash": "8438fb2175e27ff58b85a80cb0328c92c7c0d522", "id": "transformers.quantizers.quantizer_compressed_tensors", "ignore_all": true, "interface_hash": "317d7f21305905d8a8dbb5290efbd53a9e07dcf6", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/quantizers/quantizer_compressed_tensors.py", "plugin_data": null, "size": 7450, "suppressed": ["compressed_tensors.compressors", "compressed_tensors.quantization"], "version_id": "1.16.1"}