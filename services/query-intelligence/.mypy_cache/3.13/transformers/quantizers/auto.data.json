{".class": "MypyFile", "_fullname": "transformers.quantizers.auto", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AUTO_QUANTIZATION_CONFIG_MAPPING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.quantizers.auto.AUTO_QUANTIZATION_CONFIG_MAPPING", "name": "AUTO_QUANTIZATION_CONFIG_MAPPING", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "AUTO_QUANTIZER_MAPPING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.quantizers.auto.AUTO_QUANTIZER_MAPPING", "name": "AUTO_QUANTIZER_MAPPING", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "abc.ABCMeta"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "AqlmConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.AqlmConfig", "kind": "Gdef"}, "AqlmHfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_aqlm.AqlmHfQuantizer", "kind": "Gdef"}, "AutoConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.configuration_auto.AutoConfig", "kind": "Gdef"}, "AutoHfQuantizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.quantizers.auto.AutoHfQuantizer", "name": "AutoHfQuantizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.quantizers.auto.AutoHfQuantizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.quantizers.auto", "mro": ["transformers.quantizers.auto.AutoHfQuantizer", "builtins.object"], "names": {".class": "SymbolTable", "from_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "quantization_config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "transformers.quantizers.auto.AutoHfQuantizer.from_config", "name": "from_config", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "quantization_config", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.quantizers.auto.AutoHfQuantizer"}, {".class": "UnionType", "items": ["transformers.utils.quantization_config.QuantizationConfigMixin", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_config of AutoHfQuantizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.quantizers.auto.AutoHfQuantizer.from_config", "name": "from_config", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "quantization_config", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.quantizers.auto.AutoHfQuantizer"}, {".class": "UnionType", "items": ["transformers.utils.quantization_config.QuantizationConfigMixin", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_config of AutoHfQuantizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_pretrained": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "pretrained_model_name_or_path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "transformers.quantizers.auto.AutoHfQuantizer.from_pretrained", "name": "from_pretrained", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.quantizers.auto.AutoHfQuantizer.from_pretrained", "name": "from_pretrained", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "pretrained_model_name_or_path", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.quantizers.auto.AutoHfQuantizer"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_pretrained of AutoHfQuantizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "merge_quantization_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "quantization_config", "quantization_config_from_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "transformers.quantizers.auto.AutoHfQuantizer.merge_quantization_configs", "name": "merge_quantization_configs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "quantization_config", "quantization_config_from_args"], "arg_types": [{".class": "TypeType", "item": "transformers.quantizers.auto.AutoHfQuantizer"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "transformers.utils.quantization_config.QuantizationConfigMixin"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.utils.quantization_config.QuantizationConfigMixin", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "merge_quantization_configs of AutoHfQuantizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.quantizers.auto.AutoHfQuantizer.merge_quantization_configs", "name": "merge_quantization_configs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "quantization_config", "quantization_config_from_args"], "arg_types": [{".class": "TypeType", "item": "transformers.quantizers.auto.AutoHfQuantizer"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "transformers.utils.quantization_config.QuantizationConfigMixin"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.utils.quantization_config.QuantizationConfigMixin", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "merge_quantization_configs of AutoHfQuantizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "supports_quant_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["quantization_config_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.quantizers.auto.AutoHfQuantizer.supports_quant_method", "name": "supports_quant_method", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.quantizers.auto.AutoHfQuantizer.supports_quant_method", "name": "supports_quant_method", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["quantization_config_dict"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "supports_quant_method of AutoHfQuantizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.quantizers.auto.AutoHfQuantizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.quantizers.auto.AutoHfQuantizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AutoQuantizationConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.quantizers.auto.AutoQuantizationConfig", "name": "AutoQuantizationConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.quantizers.auto.AutoQuantizationConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.quantizers.auto", "mro": ["transformers.quantizers.auto.AutoQuantizationConfig", "builtins.object"], "names": {".class": "SymbolTable", "from_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "quantization_config_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "transformers.quantizers.auto.AutoQuantizationConfig.from_dict", "name": "from_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "quantization_config_dict"], "arg_types": [{".class": "TypeType", "item": "transformers.quantizers.auto.AutoQuantizationConfig"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_dict of AutoQuantizationConfig", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.quantizers.auto.AutoQuantizationConfig.from_dict", "name": "from_dict", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "quantization_config_dict"], "arg_types": [{".class": "TypeType", "item": "transformers.quantizers.auto.AutoQuantizationConfig"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_dict of AutoQuantizationConfig", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_pretrained": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "pretrained_model_name_or_path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "transformers.quantizers.auto.AutoQuantizationConfig.from_pretrained", "name": "from_pretrained", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.quantizers.auto.AutoQuantizationConfig.from_pretrained", "name": "from_pretrained", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "pretrained_model_name_or_path", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.quantizers.auto.AutoQuantizationConfig"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_pretrained of AutoQuantizationConfig", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.quantizers.auto.AutoQuantizationConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.quantizers.auto.AutoQuantizationConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AutoRoundConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.AutoRoundConfig", "kind": "Gdef"}, "AutoRoundQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_auto_round.AutoRoundQuantizer", "kind": "Gdef"}, "AwqConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.AwqConfig", "kind": "Gdef"}, "AwqQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_awq.AwqQuantizer", "kind": "Gdef"}, "BitNetHfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_bitnet.BitNetHfQuantizer", "kind": "Gdef"}, "BitNetQuantConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.BitNetQuantConfig", "kind": "Gdef"}, "BitsAndBytesConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.BitsAndBytesConfig", "kind": "Gdef"}, "Bnb4BitHfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_bnb_4bit.Bnb4BitHfQuantizer", "kind": "Gdef"}, "Bnb8BitHfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_bnb_8bit.Bnb8BitHfQuantizer", "kind": "Gdef"}, "CompressedTensorsConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.CompressedTensorsConfig", "kind": "Gdef"}, "CompressedTensorsHfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_compressed_tensors.CompressedTensorsHfQuantizer", "kind": "Gdef"}, "EetqConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.EetqConfig", "kind": "Gdef"}, "EetqHfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_eetq.EetqHfQuantizer", "kind": "Gdef"}, "FbgemmFp8Config": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.FbgemmFp8Config", "kind": "Gdef"}, "FbgemmFp8HfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_fbgemm_fp8.FbgemmFp8HfQuantizer", "kind": "Gdef"}, "FineGrainedFP8Config": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.FineGrainedFP8Config", "kind": "Gdef"}, "FineGrainedFP8HfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_finegrained_fp8.FineGrainedFP8HfQuantizer", "kind": "Gdef"}, "GPTQConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.GPTQConfig", "kind": "Gdef"}, "GptqHfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_gptq.GptqHfQuantizer", "kind": "Gdef"}, "HfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.base.HfQuantizer", "kind": "Gdef"}, "HiggsConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.HiggsConfig", "kind": "Gdef"}, "HiggsHfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_higgs.HiggsHfQuantizer", "kind": "Gdef"}, "HqqConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.HqqConfig", "kind": "Gdef"}, "HqqHfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "QuantizationConfigMixin": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.QuantizationConfigMixin", "kind": "Gdef"}, "QuantizationMethod": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.QuantizationMethod", "kind": "Gdef"}, "QuantoConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.QuantoConfig", "kind": "Gdef"}, "QuantoHfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_quanto.QuantoHfQuantizer", "kind": "Gdef"}, "QuarkConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.QuarkConfig", "kind": "Gdef"}, "QuarkHfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_quark.QuarkHfQuantizer", "kind": "Gdef"}, "SpQRConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.SpQRConfig", "kind": "Gdef"}, "SpQRHfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_spqr.SpQRHfQuantizer", "kind": "Gdef"}, "TorchAoConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.TorchAoConfig", "kind": "Gdef"}, "TorchAoHfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_torchao.TorchAoHfQuantizer", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "VptqConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.quantization_config.VptqConfig", "kind": "Gdef"}, "VptqHfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizer_vptq.VptqHfQuantizer", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.auto.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.auto.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.auto.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.auto.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.auto.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.auto.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.quantizers.auto.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "register_quantization_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["method"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.quantizers.auto.register_quantization_config", "name": "register_quantization_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["method"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register_quantization_config", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_quantizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.quantizers.auto.register_quantizer", "name": "register_quantizer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register_quantizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/quantizers/auto.py"}