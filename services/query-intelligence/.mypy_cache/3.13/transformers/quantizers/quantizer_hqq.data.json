{".class": "MypyFile", "_fullname": "transformers.quantizers.quantizer_hqq", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "HfQuantizer": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.base.HfQuantizer", "kind": "Gdef"}, "HqqHfQuantizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.quantizers.base.HfQuantizer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer", "name": "HqqHfQuantizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "transformers.quantizers.quantizer_hqq", "mro": ["transformers.quantizers.quantizer_hqq.HqqHfQuantizer", "transformers.quantizers.base.HfQuantizer", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "quantization_config", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer.__init__", "name": "__init__", "type": null}}, "_patch_layer_for_multigpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hqq_layer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer._patch_layer_for_multigpu", "name": "_patch_layer_for_multigpu", "type": null}}, "_process_model_after_weight_loading": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer._process_model_after_weight_loading", "name": "_process_model_after_weight_loading", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "model", "kwargs"], "arg_types": ["transformers.quantizers.quantizer_hqq.HqqHfQuantizer", "transformers.modeling_utils.PreTrainedModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_process_model_after_weight_loading of HqqHfQuantizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_model_before_weight_loading": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer._process_model_before_weight_loading", "name": "_process_model_before_weight_loading", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "model", "kwargs"], "arg_types": ["transformers.quantizers.quantizer_hqq.HqqHfQuantizer", "transformers.modeling_utils.PreTrainedModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_process_model_before_weight_loading of HqqHfQuantizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_quantized_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "model", "param_value", "param_name", "state_dict", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer.check_quantized_param", "name": "check_quantized_param", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "model", "param_value", "param_name", "state_dict", "kwargs"], "arg_types": ["transformers.quantizers.quantizer_hqq.HqqHfQuantizer", "transformers.modeling_utils.PreTrainedModel", "torch._tensor.Tensor", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "check_quantized_param of HqqHfQuantizer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_quantized_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "model", "param_value", "param_name", "target_device", "state_dict", "unexpected_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer.create_quantized_param", "name": "create_quantized_param", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "model", "param_value", "param_name", "target_device", "state_dict", "unexpected_keys"], "arg_types": ["transformers.quantizers.quantizer_hqq.HqqHfQuantizer", "transformers.modeling_utils.PreTrainedModel", "torch._tensor.Tensor", "builtins.str", "torch._C.device", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_quantized_param of HqqHfQuantizer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_serializable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "safe_serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer.is_serializable", "name": "is_serializable", "type": null}}, "is_trainable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer.is_trainable", "name": "is_trainable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.quantizers.quantizer_hqq.HqqHfQuantizer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_trainable of HqqHfQuantizer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer.is_trainable", "name": "is_trainable", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.quantizers.quantizer_hqq.HqqHfQuantizer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_trainable of HqqHfQuantizer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "required_packages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer.required_packages", "name": "required_packages", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "requires_calibration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer.requires_calibration", "name": "requires_calibration", "setter_type": null, "type": "builtins.bool"}}, "requires_parameters_quantization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer.requires_parameters_quantization", "name": "requires_parameters_quantization", "setter_type": null, "type": "builtins.bool"}}, "torch_dtype": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer.torch_dtype", "name": "torch_dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "update_expected_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "expected_keys", "loaded_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer.update_expected_keys", "name": "update_expected_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "expected_keys", "loaded_keys"], "arg_types": ["transformers.quantizers.quantizer_hqq.HqqHfQuantizer", "transformers.modeling_utils.PreTrainedModel", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_expected_keys of HqqHfQuantizer", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_missing_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "model", "missing_keys", "prefix", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer.update_missing_keys", "name": "update_missing_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "model", "missing_keys", "prefix", "kwargs"], "arg_types": ["transformers.quantizers.quantizer_hqq.HqqHfQuantizer", "transformers.modeling_utils.PreTrainedModel", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_missing_keys of HqqHfQuantizer", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "use_keep_in_fp32_modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer.use_keep_in_fp32_modules", "name": "use_keep_in_fp32_modules", "setter_type": null, "type": "builtins.bool"}}, "using_multi_gpu": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer.using_multi_gpu", "name": "using_multi_gpu", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "validate_environment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer.validate_environment", "name": "validate_environment", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.quantizers.quantizer_hqq.HqqHfQuantizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.PreTrainedModel", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.quantizer_hqq.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.quantizer_hqq.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.quantizer_hqq.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.quantizer_hqq.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.quantizer_hqq.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.quantizers.quantizer_hqq.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "find_parent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["model", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.quantizers.quantizer_hqq.find_parent", "name": "find_parent", "type": null}}, "get_module_from_name": {".class": "SymbolTableNode", "cross_ref": "transformers.quantizers.quantizers_utils.get_module_from_name", "kind": "Gdef"}, "is_accelerate_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_accelerate_available", "kind": "Gdef"}, "is_hqq_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_hqq_available", "kind": "Gdef"}, "is_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_available", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.quantizers.quantizer_hqq.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "prepare_for_hqq_linear": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.hqq.prepare_for_hqq_linear", "kind": "Gdef"}, "remove_hook_from_module": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.quantizers.quantizer_hqq.remove_hook_from_module", "name": "remove_hook_from_module", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.quantizers.quantizer_hqq.remove_hook_from_module", "source_any": null, "type_of_any": 3}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/quantizers/quantizer_hqq.py"}