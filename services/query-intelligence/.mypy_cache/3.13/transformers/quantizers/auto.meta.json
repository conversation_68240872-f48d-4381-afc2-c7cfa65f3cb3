{"data_mtime": 1752049733, "dep_lines": [18, 19, 20, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 19, 15, 16, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 10, 5, 5, 30, 30, 30, 30], "dependencies": ["transformers.models.auto.configuration_auto", "transformers.utils.logging", "transformers.utils.quantization_config", "transformers.quantizers.base", "transformers.quantizers.quantizer_aqlm", "transformers.quantizers.quantizer_auto_round", "transformers.quantizers.quantizer_awq", "transformers.quantizers.quantizer_bitnet", "transformers.quantizers.quantizer_bnb_4bit", "transformers.quantizers.quantizer_bnb_8bit", "transformers.quantizers.quantizer_compressed_tensors", "transformers.quantizers.quantizer_eetq", "transformers.quantizers.quantizer_fbgemm_fp8", "transformers.quantizers.quantizer_finegrained_fp8", "transformers.quantizers.quantizer_gptq", "transformers.quantizers.quantizer_higgs", "transformers.quantizers.quantizer_hqq", "transformers.quantizers.quantizer_quanto", "transformers.quantizers.quantizer_quark", "transformers.quantizers.quantizer_spqr", "transformers.quantizers.quantizer_torchao", "transformers.quantizers.quantizer_vptq", "transformers.utils", "warnings", "typing", "builtins", "_frozen_importlib", "abc", "enum", "logging"], "hash": "da5e74b976ad19311f1babad296cf512aee6bc5e", "id": "transformers.quantizers.auto", "ignore_all": true, "interface_hash": "016fa5d983e6a0756f1fa45d066adb0326f3e163", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/quantizers/auto.py", "plugin_data": null, "size": 11205, "suppressed": [], "version_id": "1.16.1"}