{"data_mtime": 1752049733, "dep_lines": [19, 28, 18, 23, 28, 16, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 74, 32], "dep_prios": [5, 10, 5, 25, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 5], "dependencies": ["transformers.quantizers.base", "transformers.utils.logging", "transformers.file_utils", "transformers.modeling_utils", "transformers.utils", "typing", "torch", "builtins", "_frozen_importlib", "abc", "logging", "torch._C", "torch._tensor", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.nn.parameter", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.hub", "transformers.utils.import_utils"], "hash": "5b50c57d372de4fa1fa280b5a5318a1e0c5f005b", "id": "transformers.quantizers.quantizer_quark", "ignore_all": true, "interface_hash": "735f31b1924f8e36824c6770d0b8eadef47e294a", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/quantizers/quantizer_quark.py", "plugin_data": null, "size": 3850, "suppressed": ["quark.torch.export.api", "accelerate.utils"], "version_id": "1.16.1"}