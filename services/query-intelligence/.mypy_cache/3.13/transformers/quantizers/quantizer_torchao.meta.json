{"data_mtime": 1752049733, "dep_lines": [21, 22, 30, 31, 19, 26, 30, 36, 14, 15, 16, 17, 19, 35, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 147, 143, 240, 286], "dep_prios": [5, 5, 10, 5, 10, 25, 5, 10, 10, 10, 10, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 20, 20], "dependencies": ["transformers.quantizers.base", "transformers.quantizers.quantizers_utils", "transformers.utils.logging", "transformers.utils.quantization_config", "packaging.version", "transformers.modeling_utils", "transformers.utils", "torch.nn", "importlib", "re", "types", "typing", "packaging", "torch", "builtins", "_frozen_importlib", "_typeshed", "abc", "importlib.metadata", "logging", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.parameter", "transformers.configuration_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.hub", "transformers.utils.import_utils"], "hash": "c54c68afb601ffd7168ac44dd0dadf539e62f1fb", "id": "transformers.quantizers.quantizer_torchao", "ignore_all": true, "interface_hash": "151196f1e60feefda1b19a0ca6c832a742f90239", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/quantizers/quantizer_torchao.py", "plugin_data": null, "size": 16430, "suppressed": ["torchao.core.config", "accelerate.utils", "torchao.quantization", "<PERSON><PERSON>"], "version_id": "1.16.1"}