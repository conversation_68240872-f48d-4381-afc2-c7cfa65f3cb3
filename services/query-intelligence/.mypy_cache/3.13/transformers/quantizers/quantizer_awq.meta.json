{"data_mtime": 1752049733, "dep_lines": [19, 25, 26, 14, 17, 23, 25, 107, 14, 15, 17, 30, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 10, 10, 25, 5, 20, 20, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.quantizers.base", "transformers.utils.logging", "transformers.utils.quantization_config", "importlib.metadata", "packaging.version", "transformers.modeling_utils", "transformers.utils", "transformers.integrations", "importlib", "typing", "packaging", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "logging", "torch._C", "torch.cuda", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "transformers.configuration_utils", "transformers.integrations.awq", "transformers.integrations.peft", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "38c49c2cd6b8dc7406b6bce529b8d56d759442bc", "id": "transformers.quantizers.quantizer_awq", "ignore_all": true, "interface_hash": "b0233ce1ce8ca0791f101b7b6e628687469d3518", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/quantizers/quantizer_awq.py", "plugin_data": null, "size": 7250, "suppressed": [], "version_id": "1.16.1"}