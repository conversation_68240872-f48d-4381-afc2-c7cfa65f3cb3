{"data_mtime": 1752049733, "dep_lines": [16, 22, 20, 22, 87, 14, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 25, 5, 20, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.quantizers.base", "transformers.utils.logging", "transformers.modeling_utils", "transformers.utils", "transformers.integrations", "typing", "torch", "builtins", "_frozen_importlib", "abc", "logging", "torch._C", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "transformers.integrations.bitnet", "transformers.integrations.peft", "transformers.utils.hub", "transformers.utils.import_utils", "transformers.utils.quantization_config"], "hash": "dde5eaabe24089440eca31f85300cda88c80acc7", "id": "transformers.quantizers.quantizer_bitnet", "ignore_all": true, "interface_hash": "db8058c381d9d171be389e1eabb60393cd4b2d50", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/quantizers/quantizer_bitnet.py", "plugin_data": null, "size": 4675, "suppressed": [], "version_id": "1.16.1"}