{"data_mtime": 1752049733, "dep_lines": [19, 20, 26, 32, 17, 24, 26, 182, 14, 15, 17, 36, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 92, 153], "dep_prios": [5, 5, 10, 5, 10, 25, 5, 20, 10, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20], "dependencies": ["transformers.quantizers.base", "transformers.quantizers.quantizers_utils", "transformers.utils.logging", "transformers.utils.quantization_config", "packaging.version", "transformers.modeling_utils", "transformers.utils", "transformers.integrations", "importlib", "typing", "packaging", "torch", "builtins", "_frozen_importlib", "abc", "logging", "torch._C", "torch._tensor", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "transformers.configuration_utils", "transformers.integrations.peft", "transformers.integrations.quanto", "transformers.utils.hub", "transformers.utils.import_utils"], "hash": "92cfd628ae0e95ec8bfadfe840377d69acee328e", "id": "transformers.quantizers.quantizer_quanto", "ignore_all": true, "interface_hash": "62638297b88e05f1cc670ca0f90a4e0103d376c6", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/quantizers/quantizer_quanto.py", "plugin_data": null, "size": 7699, "suppressed": ["optimum.quanto", "accelerate.utils"], "version_id": "1.16.1"}