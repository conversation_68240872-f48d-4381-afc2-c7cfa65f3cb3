{"data_mtime": 1752049733, "dep_lines": [20, 21, 27, 18, 25, 27, 42, 93, 14, 15, 16, 18, 40, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 128, 149], "dep_prios": [5, 5, 10, 10, 25, 5, 5, 20, 10, 5, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20], "dependencies": ["transformers.quantizers.base", "transformers.quantizers.quantizers_utils", "transformers.utils.logging", "packaging.version", "transformers.modeling_utils", "transformers.utils", "transformers.pytorch_utils", "transformers.integrations", "importlib", "functools", "typing", "packaging", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "logging", "torch._C", "torch._tensor", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "transformers.configuration_utils", "transformers.integrations.bitsandbytes", "transformers.integrations.peft", "transformers.utils.hub", "transformers.utils.import_utils", "transformers.utils.quantization_config", "types"], "hash": "498380077f4b15ff6903ef019bfbca41c80e3375", "id": "transformers.quantizers.quantizer_bnb_4bit", "ignore_all": true, "interface_hash": "a8b87f1869bab8d0af149e7c323e386a6d6a1d6d", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/quantizers/quantizer_bnb_4bit.py", "plugin_data": null, "size": 16589, "suppressed": ["accelerate.utils", "bitsandbytes"], "version_id": "1.16.1"}