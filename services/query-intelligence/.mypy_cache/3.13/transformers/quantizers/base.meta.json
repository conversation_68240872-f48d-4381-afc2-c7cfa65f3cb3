{"data_mtime": 1752049733, "dep_lines": [339, 18, 19, 17, 23, 27, 278, 14, 15, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 317], "dep_prios": [20, 5, 5, 5, 25, 5, 20, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["transformers.models.llama4.modeling_llama4", "transformers.utils.quantization_config", "transformers.quantizers.quantizers_utils", "transformers.utils", "transformers.modeling_utils", "torch.nn", "transformers.integrations", "abc", "typing", "torch", "builtins", "_collections_abc", "_frozen_importlib", "enum", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "torch.nn.parameter", "transformers.integrations.peft", "transformers.models", "transformers.models.llama4", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "40007c1441332d2f09ce542034d5f1b31de386b4", "id": "transformers.quantizers.base", "ignore_all": true, "interface_hash": "857267b44a74a808e2a5b984cd2d39dd50738674", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/quantizers/base.py", "plugin_data": null, "size": 15149, "suppressed": ["accelerate"], "version_id": "1.16.1"}