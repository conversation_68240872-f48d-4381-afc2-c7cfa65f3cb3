{"data_mtime": 1752049733, "dep_lines": [23, 27, 22, 25, 26, 27, 16, 17, 18, 19, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 20, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.optim.lr_scheduler", "transformers.utils.logging", "torch.optim", "transformers.trainer_pt_utils", "transformers.trainer_utils", "transformers.utils", "math", "warnings", "functools", "typing", "torch", "builtins", "_collections_abc", "_frozen_importlib", "abc", "enum", "logging", "torch.autograd", "torch.autograd.grad_mode", "torch.optim.optimizer", "torch.utils", "torch.utils._contextlib", "transformers.utils.generic"], "hash": "b69469605ceacf695d2fcfbab05eb396967e1842", "id": "transformers.optimization", "ignore_all": true, "interface_hash": "45b9230b48b0bb0939b5cbb0c3bf52575367ec77", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/optimization.py", "plugin_data": null, "size": 36423, "suppressed": [], "version_id": "1.16.1"}