{"data_mtime": 1752049734, "dep_lines": [30, 48, 27, 47, 48, 16, 17, 18, 19, 20, 21, 22, 26, 29, 532, 1, 1, 1, 1, 1, 1, 1, 1, 1, 24, 25, 536, 540, 544], "dep_prios": [5, 10, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 20, 20, 20], "dependencies": ["transformers.models.auto.modeling_auto", "transformers.utils.logging", "huggingface_hub.utils", "transformers.training_args", "transformers.utils", "copy", "json", "os", "warnings", "dataclasses", "pathlib", "typing", "huggingface_hub", "transformers", "torch", "builtins", "_frozen_importlib", "_warnings", "abc", "collections", "huggingface_hub.errors", "huggingface_hub.hf_api", "logging", "transformers.utils.hub"], "hash": "f6b95ab7509cc9d02225233209d3e8d3bee0f91d", "id": "transformers.modelcard", "ignore_all": true, "interface_hash": "19e130e2f90f9b7474ff6283a7bc35010a6894d6", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/modelcard.py", "plugin_data": null, "size": 35711, "suppressed": ["requests", "yaml", "tensorflow", "datasets", "tokenizers"], "version_id": "1.16.1"}