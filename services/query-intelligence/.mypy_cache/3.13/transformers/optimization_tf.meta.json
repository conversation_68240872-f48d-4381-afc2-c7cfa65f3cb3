{"data_mtime": 1752049734, "dep_lines": [27, 17, 1, 1, 1, 25, 23, 19], "dep_prios": [5, 5, 5, 30, 30, 5, 5, 10], "dependencies": ["transformers.modeling_tf_utils", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "88e794020f44cc1b25305d6e451403f1534fa4c9", "id": "transformers.optimization_tf", "ignore_all": true, "interface_hash": "0cf04bb9bcc9a956451daada563bd1578e899a8c", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/optimization_tf.py", "plugin_data": null, "size": 16718, "suppressed": ["tensorflow.keras.optimizers.legacy", "tf_keras.optimizers.legacy", "tensorflow"], "version_id": "1.16.1"}