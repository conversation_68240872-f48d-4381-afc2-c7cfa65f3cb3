{"data_mtime": 1752049734, "dep_lines": [22, 23, 18, 20, 21, 22, 33, 16, 18, 32, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 5, 5, 10, 10, 20, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.utils.quantization_config", "packaging.version", "transformers.activations", "transformers.modeling_utils", "transformers.utils", "torch.nn", "importlib", "packaging", "torch", "builtins", "_frozen_importlib", "abc", "logging", "transformers.utils.import_utils", "typing"], "hash": "7330372398104d4215742e38a59d0b69f95f0511", "id": "transformers.integrations.awq", "ignore_all": true, "interface_hash": "929c5c92ed429eb3198b49b016e4ccf2297c8583", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/integrations/awq.py", "plugin_data": null, "size": 20579, "suppressed": [], "version_id": "1.16.1"}