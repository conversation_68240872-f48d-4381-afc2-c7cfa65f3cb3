{"data_mtime": 1752049734, "dep_lines": [19, 18, 20, 26, 27, 13, 14, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.generation.configuration_utils", "transformers.cache_utils", "transformers.masking_utils", "transformers.modeling_utils", "transformers.pytorch_utils", "logging", "typing", "torch", "builtins", "_frozen_importlib", "abc", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.export", "torch.export.exported_program", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "transformers.configuration_utils", "transformers.generation", "transformers.integrations.peft", "transformers.utils", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "12c5b360d5147c9ef14d0c272b8ced50394208b6", "id": "transformers.integrations.executorch", "ignore_all": true, "interface_hash": "8b42ef3c670c1ead02eda3ed44595d1a5e045cf6", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/integrations/executorch.py", "plugin_data": null, "size": 35577, "suppressed": [], "version_id": "1.16.1"}