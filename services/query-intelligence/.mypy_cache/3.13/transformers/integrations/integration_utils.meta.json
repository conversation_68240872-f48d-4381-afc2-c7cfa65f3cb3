{"data_mtime": 1752049734, "dep_lines": [39, 665, 20, 21, 35, 39, 90, 91, 92, 93, 937, 18, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 34, 35, 37, 53, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 829, 1494, 1495, 1186, 1492, 1539, 2051, 1386, 1538, 1536, 1602, 2125, 2126, 64, 670, 790, 1207, 1491, 1724, 1776, 2033, 2049, 2081, 2170], "dep_prios": [10, 20, 10, 10, 10, 5, 10, 5, 5, 5, 20, 10, 10, 20, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 20, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 10, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20], "dependencies": ["transformers.utils.logging", "torch.utils.tensorboard", "importlib.metadata", "importlib.util", "packaging.version", "transformers.utils", "transformers.modelcard", "transformers.trainer_callback", "transformers.trainer_utils", "transformers.training_args", "transformers.trainer", "copy", "functools", "importlib", "json", "numbers", "os", "pickle", "shutil", "sys", "tempfile", "dataclasses", "enum", "pathlib", "typing", "numpy", "packaging", "transformers", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "json.decoder", "logging", "numpy._typing", "numpy._typing._ufunc", "posixpath", "torch.utils", "torch.utils.tensorboard.writer", "torch.version", "transformers.utils.generic", "transformers.utils.import_utils", "types"], "hash": "e748cf8479c8ecd88de9073f3f387cad948b599b", "id": "transformers.integrations.integration_utils", "ignore_all": true, "interface_hash": "0aff2e38fa775db19edd4143af09066c5da0dae9", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/integrations/integration_utils.py", "plugin_data": null, "size": 105728, "suppressed": ["wandb.sdk.lib.config_util", "neptune.new.internal.utils", "neptune.new.metadata_containers.run", "azureml.core.run", "neptune.internal.utils", "neptune.new.exceptions", "flytekitplugins.deck.renderer", "dagshub.upload", "neptune.new", "neptune.exceptions", "neptune.utils", "dvclive.plots", "dvclive.utils", "comet_ml", "tensorboardX", "wandb", "mlflow", "neptune", "codecarbon", "clearml", "flytekit", "pandas", "dvclive", "swan<PERSON>b"], "version_id": "1.16.1"}