{"data_mtime": 1752049734, "dep_lines": [165, 1, 10, 1, 9, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "dep_prios": [20, 10, 10, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["transformers.models.llama.modeling_llama", "transformers.utils.logging", "torch.nn.functional", "transformers.utils", "torch.nn", "torch", "builtins", "_frozen_importlib", "abc", "logging", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.autograd", "torch.autograd.function", "torch.nn.modules", "torch.nn.modules.linear", "torch.nn.modules.module", "transformers.models", "transformers.models.llama", "transformers.utils.import_utils", "types", "typing"], "hash": "26878f8f5b40a19240e5dedb8ee0725fb7d32b66", "id": "transformers.integrations.bitnet", "ignore_all": true, "interface_hash": "3954c1a49a3a0877b04bced483c76facd6cab451", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/integrations/bitnet.py", "plugin_data": null, "size": 15718, "suppressed": ["accelerate"], "version_id": "1.16.1"}