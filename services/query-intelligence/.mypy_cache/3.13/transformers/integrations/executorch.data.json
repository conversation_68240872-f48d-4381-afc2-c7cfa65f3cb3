{".class": "MypyFile", "_fullname": "transformers.integrations.executorch", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ALL_ATTENTION_FUNCTIONS": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.ALL_ATTENTION_FUNCTIONS", "kind": "Gdef"}, "ALL_MASK_ATTENTION_FUNCTIONS": {".class": "SymbolTableNode", "cross_ref": "transformers.masking_utils.ALL_MASK_ATTENTION_FUNCTIONS", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "DynamicCache": {".class": "SymbolTableNode", "cross_ref": "transformers.cache_utils.DynamicCache", "kind": "Gdef"}, "GenerationConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.generation.configuration_utils.GenerationConfig", "kind": "Gdef"}, "HybridCache": {".class": "SymbolTableNode", "cross_ref": "transformers.cache_utils.HybridCache", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.PreTrainedModel", "kind": "Gdef"}, "Seq2SeqLMDecoderExportableModuleWithStaticCache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.executorch.Seq2SeqLMDecoderExportableModuleWithStaticCache", "name": "Seq2SeqLMDecoderExportableModuleWithStaticCache", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.Seq2SeqLMDecoderExportableModuleWithStaticCache", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.executorch", "mro": ["transformers.integrations.executorch.Seq2SeqLMDecoderExportableModuleWithStaticCache", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "max_static_cache_length", "batch_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.Seq2SeqLMDecoderExportableModuleWithStaticCache.__init__", "name": "__init__", "type": null}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.executorch.Seq2SeqLMDecoderExportableModuleWithStaticCache.config", "name": "config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.executorch.Seq2SeqLMDecoderExportableModuleWithStaticCache.decoder", "name": "decoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "decoder_input_ids", "encoder_hidden_states", "cache_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.Seq2SeqLMDecoderExportableModuleWithStaticCache.forward", "name": "forward", "type": null}}, "lm_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.executorch.Seq2SeqLMDecoderExportableModuleWithStaticCache.lm_head", "name": "lm_head", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "static_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.executorch.Seq2SeqLMDecoderExportableModuleWithStaticCache.static_cache", "name": "static_cache", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.executorch.Seq2SeqLMDecoderExportableModuleWithStaticCache.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.executorch.Seq2SeqLMDecoderExportableModuleWithStaticCache", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Seq2SeqLMEncoderExportableModule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.executorch.Seq2SeqLMEncoderExportableModule", "name": "Seq2SeqLMEncoderExportableModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.Seq2SeqLMEncoderExportableModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.executorch", "mro": ["transformers.integrations.executorch.Seq2SeqLMEncoderExportableModule", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "encoder_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.Seq2SeqLMEncoderExportableModule.__init__", "name": "__init__", "type": null}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.executorch.Seq2SeqLMEncoderExportableModule.encoder", "name": "encoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.Seq2SeqLMEncoderExportableModule.forward", "name": "forward", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.executorch.Seq2SeqLMEncoderExportableModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.executorch.Seq2SeqLMEncoderExportableModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Seq2SeqLMExportableModule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.executorch.Seq2SeqLMExportableModule", "name": "Seq2SeqLMExportableModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.Seq2SeqLMExportableModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.executorch", "mro": ["transformers.integrations.executorch.Seq2SeqLMExportableModule", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "model", "batch_size", "max_hidden_seq_length", "cache_implementation", "max_cache_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.Seq2SeqLMExportableModule.__init__", "name": "__init__", "type": null}}, "_export_decoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "decoder_input_ids", "encoder_hidden_states", "cache_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.Seq2SeqLMExportableModule._export_decoder", "name": "_export_decoder", "type": null}}, "_export_encoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "encoder_input_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.Seq2SeqLMExportableModule._export_encoder", "name": "_export_encoder", "type": null}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.executorch.Seq2SeqLMExportableModule.config", "name": "config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.executorch.Seq2SeqLMExportableModule.encoder", "name": "encoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "export": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "encoder_input_ids", "decoder_input_ids", "encoder_hidden_states", "cache_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.Seq2SeqLMExportableModule.export", "name": "export", "type": null}}, "exported_decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.executorch.Seq2SeqLMExportableModule.exported_decoder", "name": "exported_decoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "exported_encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.executorch.Seq2SeqLMExportableModule.exported_encoder", "name": "exported_encoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "full_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.executorch.Seq2SeqLMExportableModule.full_model", "name": "full_model", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "prompt_token_ids", "max_new_tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.Seq2SeqLMExportableModule.generate", "name": "generate", "type": null}}, "generation_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.executorch.Seq2SeqLMExportableModule.generation_config", "name": "generation_config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "max_hidden_seq_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.executorch.Seq2SeqLMExportableModule.max_hidden_seq_length", "name": "max_hidden_seq_length", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.executorch.Seq2SeqLMExportableModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.executorch.Seq2SeqLMExportableModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StaticCache": {".class": "SymbolTableNode", "cross_ref": "transformers.cache_utils.StaticCache", "kind": "Gdef"}, "TorchExportableModuleForDecoderOnlyLM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.executorch.TorchExportableModuleForDecoderOnlyLM", "name": "TorchExportableModuleForDecoderOnlyLM", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.TorchExportableModuleForDecoderOnlyLM", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.executorch", "mro": ["transformers.integrations.executorch.TorchExportableModuleForDecoderOnlyLM", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "model", "max_batch_size", "max_cache_len"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.integrations.executorch.TorchExportableModuleForDecoderOnlyLM.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "model", "max_batch_size", "max_cache_len"], "arg_types": ["transformers.integrations.executorch.TorchExportableModuleForDecoderOnlyLM", "transformers.modeling_utils.PreTrainedModel", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TorchExportableModuleForDecoderOnlyLM", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "export": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "cache_position", "dynamic_shapes", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.integrations.executorch.TorchExportableModuleForDecoderOnlyLM.export", "name": "export", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "cache_position", "dynamic_shapes", "strict"], "arg_types": ["transformers.integrations.executorch.TorchExportableModuleForDecoderOnlyLM", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "export of TorchExportableModuleForDecoderOnlyLM", "ret_type": "torch.export.exported_program.ExportedProgram", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "input_ids", "cache_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.integrations.executorch.TorchExportableModuleForDecoderOnlyLM.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "input_ids", "cache_position"], "arg_types": ["transformers.integrations.executorch.TorchExportableModuleForDecoderOnlyLM", "torch._tensor.Tensor", "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of TorchExportableModuleForDecoderOnlyLM", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["exported_program", "tokenizer", "prompt", "max_new_tokens", "do_sample", "temperature", "top_k", "top_p", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.integrations.executorch.TorchExportableModuleForDecoderOnlyLM.generate", "name": "generate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["exported_program", "tokenizer", "prompt", "max_new_tokens", "do_sample", "temperature", "top_k", "top_p", "device"], "arg_types": ["torch.export.exported_program.ExportedProgram", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.int", "builtins.bool", "builtins.float", "builtins.int", "builtins.float", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate of TorchExportableModuleForDecoderOnlyLM", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.integrations.executorch.TorchExportableModuleForDecoderOnlyLM.generate", "name": "generate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["exported_program", "tokenizer", "prompt", "max_new_tokens", "do_sample", "temperature", "top_k", "top_p", "device"], "arg_types": ["torch.export.exported_program.ExportedProgram", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.int", "builtins.bool", "builtins.float", "builtins.int", "builtins.float", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate of TorchExportableModuleForDecoderOnlyLM", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.executorch.TorchExportableModuleForDecoderOnlyLM.model", "name": "model", "setter_type": null, "type": "transformers.integrations.executorch.TorchExportableModuleWithHybridCache"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.executorch.TorchExportableModuleForDecoderOnlyLM.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.executorch.TorchExportableModuleForDecoderOnlyLM", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TorchExportableModuleWithHybridCache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.executorch.TorchExportableModuleWithHybridCache", "name": "TorchExportableModuleWithHybridCache", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.TorchExportableModuleWithHybridCache", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.executorch", "mro": ["transformers.integrations.executorch.TorchExportableModuleWithHybridCache", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "model", "max_batch_size", "max_cache_len"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.integrations.executorch.TorchExportableModuleWithHybridCache.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "model", "max_batch_size", "max_cache_len"], "arg_types": ["transformers.integrations.executorch.TorchExportableModuleWithHybridCache", "transformers.modeling_utils.PreTrainedModel", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TorchExportableModuleWithHybridCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.executorch.TorchExportableModuleWithHybridCache.cache", "name": "cache", "setter_type": null, "type": "transformers.cache_utils.HybridCache"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "input_ids", "cache_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.integrations.executorch.TorchExportableModuleWithHybridCache.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "input_ids", "cache_position"], "arg_types": ["transformers.integrations.executorch.TorchExportableModuleWithHybridCache", "torch._tensor.Tensor", "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of TorchExportableModuleWithHybridCache", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.executorch.TorchExportableModuleWithHybridCache.model", "name": "model", "setter_type": null, "type": "transformers.modeling_utils.PreTrainedModel"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.executorch.TorchExportableModuleWithHybridCache.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.executorch.TorchExportableModuleWithHybridCache", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TorchExportableModuleWithStaticCache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.integrations.executorch.TorchExportableModuleWithStaticCache", "name": "TorchExportableModuleWithStaticCache", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.TorchExportableModuleWithStaticCache", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.integrations.executorch", "mro": ["transformers.integrations.executorch.TorchExportableModuleWithStaticCache", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.integrations.executorch.TorchExportableModuleWithStaticCache.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "model"], "arg_types": ["transformers.integrations.executorch.TorchExportableModuleWithStaticCache", "transformers.modeling_utils.PreTrainedModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TorchExportableModuleWithStaticCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "input_ids", "cache_position"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.integrations.executorch.TorchExportableModuleWithStaticCache.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "input_ids", "cache_position"], "arg_types": ["transformers.integrations.executorch.TorchExportableModuleWithStaticCache", "torch._tensor.Tensor", "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of TorchExportableModuleWithStaticCache", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["exported_program", "prompt_token_ids", "max_new_tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.integrations.executorch.TorchExportableModuleWithStaticCache.generate", "name": "generate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["exported_program", "prompt_token_ids", "max_new_tokens"], "arg_types": ["torch.export.exported_program.ExportedProgram", "torch._tensor.Tensor", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate of TorchExportableModuleWithStaticCache", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.integrations.executorch.TorchExportableModuleWithStaticCache.generate", "name": "generate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["exported_program", "prompt_token_ids", "max_new_tokens"], "arg_types": ["torch.export.exported_program.ExportedProgram", "torch._tensor.Tensor", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate of TorchExportableModuleWithStaticCache", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.executorch.TorchExportableModuleWithStaticCache.model", "name": "model", "setter_type": null, "type": "transformers.modeling_utils.PreTrainedModel"}}, "static_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.integrations.executorch.TorchExportableModuleWithStaticCache.static_cache", "name": "static_cache", "setter_type": null, "type": "transformers.cache_utils.StaticCache"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.integrations.executorch.TorchExportableModuleWithStaticCache.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.integrations.executorch.TorchExportableModuleWithStaticCache", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.executorch.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.executorch.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.executorch.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.executorch.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.executorch.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.integrations.executorch.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_ignore_causal_mask_sdpa": {".class": "SymbolTableNode", "cross_ref": "transformers.masking_utils._ignore_causal_mask_sdpa", "kind": "Gdef"}, "_is_torch_greater_or_equal_than_2_5": {".class": "SymbolTableNode", "cross_ref": "transformers.masking_utils._is_torch_greater_or_equal_than_2_5", "kind": "Gdef"}, "convert_and_export_with_cache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["model", "example_input_ids", "example_cache_position", "dynamic_shapes", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.convert_and_export_with_cache", "name": "convert_and_export_with_cache", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["model", "example_input_ids", "example_cache_position", "dynamic_shapes", "strict"], "arg_types": ["transformers.modeling_utils.PreTrainedModel", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_and_export_with_cache", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "export_with_dynamic_cache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["model", "example_input_ids", "example_attention_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.export_with_dynamic_cache", "name": "export_with_dynamic_cache", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["model", "example_input_ids", "example_attention_mask"], "arg_types": ["transformers.modeling_utils.PreTrainedModel", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "export_with_dynamic_cache", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_torch_greater_or_equal": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_greater_or_equal", "kind": "Gdef"}, "is_torch_greater_or_equal_than_2_3": {".class": "SymbolTableNode", "cross_ref": "transformers.pytorch_utils.is_torch_greater_or_equal_than_2_3", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "prepare_padding_mask": {".class": "SymbolTableNode", "cross_ref": "transformers.masking_utils.prepare_padding_mask", "kind": "Gdef"}, "sdpa_mask_without_vmap": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["batch_size", "cache_position", "kv_length", "kv_offset", "mask_function", "attention_mask", "local_size", "allow_is_causal_skip", "allow_torch_fix", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.integrations.executorch.sdpa_mask_without_vmap", "name": "sdpa_mask_without_vmap", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["batch_size", "cache_position", "kv_length", "kv_offset", "mask_function", "attention_mask", "local_size", "allow_is_causal_skip", "allow_torch_fix", "kwargs"], "arg_types": ["builtins.int", "torch._tensor.Tensor", "builtins.int", "builtins.int", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sdpa_mask_without_vmap", "ret_type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/integrations/executorch.py"}