{"data_mtime": 1752049734, "dep_lines": [49, 71, 3245, 28, 35, 38, 39, 40, 41, 42, 49, 76, 1851, 2944, 3245, 18, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 33, 37, 75, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 32, 34, 95, 92, 1457], "dep_prios": [10, 5, 20, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 5, 5, 20], "dependencies": ["transformers.utils.logging", "transformers.utils.hub", "transformers.models.auto", "collections.abc", "packaging.version", "transformers.activations_tf", "transformers.configuration_utils", "transformers.dynamic_module_utils", "transformers.generation", "transformers.tf_utils", "transformers.utils", "safetensors.tensorflow", "transformers.modelcard", "transformers.modeling_tf_pytorch_utils", "transformers.models", "__future__", "functools", "gc", "inspect", "json", "os", "pickle", "re", "warnings", "pathlib", "typing", "numpy", "transformers", "safetensors", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "_warnings", "abc", "genericpath", "io", "json.encoder", "logging", "packaging", "posixpath", "transformers.generation.configuration_utils", "transformers.generation.tf_utils", "transformers.tokenization_utils_base", "transformers.utils.import_utils", "types"], "hash": "9fad4b8dcf19cd9837efa56a1ec9e375a8e4aabe", "id": "transformers.modeling_tf_utils", "ignore_all": true, "interface_hash": "6f41ec0a4e4282cb26bf1ab592f51e19f5bcc7bd", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/modeling_tf_utils.py", "plugin_data": null, "size": 166511, "suppressed": ["h5py", "tensorflow", "keras", "tf_keras", "datasets"], "version_id": "1.16.1"}