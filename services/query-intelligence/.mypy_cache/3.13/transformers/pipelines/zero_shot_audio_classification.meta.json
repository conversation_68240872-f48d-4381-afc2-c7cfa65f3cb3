{"data_mtime": 1752049734, "dep_lines": [21, 25, 26, 21, 15, 16, 18, 1, 1, 1, 1, 1, 1, 19], "dep_prios": [10, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.utils.logging", "transformers.pipelines.audio_classification", "transformers.pipelines.base", "transformers.utils", "collections", "typing", "numpy", "builtins", "_frozen_importlib", "abc", "logging", "transformers.utils.doc", "transformers.utils.hub"], "hash": "5439dd2bfabccbbf28ab417b7c10097817e586e8", "id": "transformers.pipelines.zero_shot_audio_classification", "ignore_all": true, "interface_hash": "a659d03aeb0a1f282c94555f9bfb80ad985eb19b", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/pipelines/zero_shot_audio_classification.py", "plugin_data": null, "size": 6909, "suppressed": ["requests"], "version_id": "1.16.1"}