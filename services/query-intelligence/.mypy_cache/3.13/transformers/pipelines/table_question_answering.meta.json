{"data_mtime": 1752049734, "dep_lines": [19, 27, 13, 6, 7, 1, 2, 4, 17, 1, 1, 1, 1, 1, 1, 1, 1, 25, 46], "dep_prios": [5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 10, 20], "dependencies": ["transformers.models.auto.modeling_auto", "transformers.models.auto.modeling_tf_auto", "transformers.pipelines.base", "transformers.generation", "transformers.utils", "collections", "types", "numpy", "torch", "builtins", "_frozen_importlib", "abc", "transformers.generation.configuration_utils", "transformers.utils.doc", "transformers.utils.hub", "transformers.utils.import_utils", "typing"], "hash": "6963141372cae1a4615cd5543cc94a318d959079", "id": "transformers.pipelines.table_question_answering", "ignore_all": true, "interface_hash": "788d4707f6e63c1e09f9245c86bf9b39e2889ba6", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/pipelines/table_question_answering.py", "plugin_data": null, "size": 20852, "suppressed": ["tensorflow", "pandas"], "version_id": "1.16.1"}