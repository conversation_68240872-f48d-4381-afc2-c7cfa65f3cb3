{"data_mtime": 1752049734, "dep_lines": [36, 41, 19, 27, 18, 19, 31, 33, 16, 31, 39, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 10, 5, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.auto.modeling_tf_auto", "transformers.models.auto.modeling_auto", "transformers.utils.logging", "transformers.pipelines.base", "transformers.generation", "transformers.utils", "PIL.Image", "transformers.image_utils", "typing", "PIL", "torch", "builtins", "_frozen_importlib", "abc", "functools", "logging", "transformers.generation.configuration_utils", "transformers.utils.doc", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "6c70edc8a076daede9c7e757241ae16c8049465c", "id": "transformers.pipelines.image_to_text", "ignore_all": true, "interface_hash": "4ffcba2e635235e6ce2e9a4c536f16f52f6fb796", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/pipelines/image_to_text.py", "plugin_data": null, "size": 10217, "suppressed": [], "version_id": "1.16.1"}