{"data_mtime": 1752049734, "dep_lines": [39, 22, 29, 40, 17, 20, 21, 22, 33, 35, 16, 18, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.auto.modeling_auto", "transformers.utils.logging", "transformers.pipelines.base", "transformers.pipelines.pt_utils", "collections.abc", "transformers.generation", "transformers.processing_utils", "transformers.utils", "PIL.Image", "transformers.image_utils", "enum", "typing", "PIL", "builtins", "_frozen_importlib", "abc", "functools", "logging", "numpy", "transformers.generation.configuration_utils", "transformers.tokenization_utils_base", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "transformers.video_utils", "types"], "hash": "bd23980e090c02473e69fd053d7b8a775873af31", "id": "transformers.pipelines.image_text_to_text", "ignore_all": true, "interface_hash": "98b9ed503f5b6a9bbba1d9b462cbe9cb7d22b584", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/pipelines/image_text_to_text.py", "plugin_data": null, "size": 23285, "suppressed": [], "version_id": "1.16.1"}