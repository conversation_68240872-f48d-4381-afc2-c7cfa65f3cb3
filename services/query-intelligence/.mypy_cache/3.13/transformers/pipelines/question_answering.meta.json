{"data_mtime": 1752049734, "dep_lines": [35, 43, 12, 20, 41, 4, 9, 10, 11, 12, 26, 27, 1, 2, 3, 5, 7, 40, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 30, 33], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 25, 25, 10, 10, 10, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25, 10], "dependencies": ["transformers.models.auto.modeling_tf_auto", "transformers.models.auto.modeling_auto", "transformers.utils.logging", "transformers.pipelines.base", "torch.utils.data", "collections.abc", "transformers.data", "transformers.modelcard", "transformers.tokenization_utils", "transformers.utils", "transformers.modeling_tf_utils", "transformers.modeling_utils", "inspect", "types", "warnings", "typing", "numpy", "torch", "builtins", "_frozen_importlib", "abc", "collections", "logging", "torch._C", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.utils", "torch.utils.data.dataset", "transformers.data.processors", "transformers.data.processors.squad", "transformers.generation", "transformers.generation.tf_utils", "transformers.image_processing_base", "transformers.image_processing_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.processing_utils", "transformers.tokenization_utils_base", "transformers.utils.doc", "transformers.utils.hub", "transformers.utils.import_utils"], "hash": "08a9878e1d3f2903f293c6012c512bd9b9484a8d", "id": "transformers.pipelines.question_answering", "ignore_all": true, "interface_hash": "81139974db3cd888d329d08bf9a61183704cf314", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/pipelines/question_answering.py", "plugin_data": null, "size": 30712, "suppressed": ["tokenizers", "tensorflow"], "version_id": "1.16.1"}