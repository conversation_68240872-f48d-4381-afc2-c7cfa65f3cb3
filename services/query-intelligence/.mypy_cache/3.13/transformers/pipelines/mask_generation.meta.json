{"data_mtime": 1752049734, "dep_lines": [17, 5, 11, 4, 5, 20, 1, 2, 15, 20, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 25, 5, 5, 10, 25, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.auto.modeling_auto", "transformers.utils.logging", "transformers.pipelines.base", "transformers.image_utils", "transformers.utils", "PIL.Image", "collections", "typing", "torch", "PIL", "builtins", "_frozen_importlib", "abc", "logging", "transformers.utils.doc", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "361bb92e8157fec9e93a8794e1cc7384104e4ce6", "id": "transformers.pipelines.mask_generation", "ignore_all": true, "interface_hash": "e8a3f7c1c9733614559af9b971461ac5d8bb283b", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/pipelines/mask_generation.py", "plugin_data": null, "size": 14354, "suppressed": [], "version_id": "1.16.1"}