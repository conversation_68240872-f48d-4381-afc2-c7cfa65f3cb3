{".class": "MypyFile", "_fullname": "transformers.pipelines.question_answering", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ArgumentHandler": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.ArgumentHandler", "kind": "Gdef"}, "ChunkPipeline": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.ChunkPipeline", "kind": "Gdef"}, "Dataset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.question_answering.Dataset", "name": "Dataset", "setter_type": null, "type": {".class": "NoneType"}}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "MODEL_FOR_QUESTION_ANSWERING_MAPPING_NAMES": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_auto.MODEL_FOR_QUESTION_ANSWERING_MAPPING_NAMES", "kind": "Gdef"}, "ModelCard": {".class": "SymbolTableNode", "cross_ref": "transformers.modelcard.ModelCard", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PaddingStrategy": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.PaddingStrategy", "kind": "Gdef"}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.PreTrainedModel", "kind": "Gdef"}, "PreTrainedTokenizer": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils.PreTrainedTokenizer", "kind": "Gdef"}, "QuestionAnsweringArgumentHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.pipelines.base.ArgumentHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.pipelines.question_answering.QuestionAnsweringArgumentHandler", "name": "QuestionAnsweringArgumentHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringArgumentHandler", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "transformers.pipelines.question_answering", "mro": ["transformers.pipelines.question_answering.QuestionAnsweringArgumentHandler", "transformers.pipelines.base.ArgumentHandler", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringArgumentHandler.__call__", "name": "__call__", "type": null}}, "normalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringArgumentHandler.normalize", "name": "normalize", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.pipelines.question_answering.QuestionAnsweringArgumentHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.pipelines.question_answering.QuestionAnsweringArgumentHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QuestionAnsweringPipeline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.pipelines.base.ChunkPipeline"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.pipelines.question_answering.QuestionAnsweringPipeline", "name": "QuestionAnsweringPipeline", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringPipeline", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "transformers.pipelines.question_answering", "mro": ["transformers.pipelines.question_answering.QuestionAnsweringPipeline", "transformers.pipelines.base.ChunkPipeline", "transformers.pipelines.base.Pipeline", "transformers.pipelines.base._ScikitCompat", "abc.ABC", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringPipeline.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "model", "tokenizer", "modelcard", "framework", "task", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringPipeline.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "model", "tokenizer", "modelcard", "framework", "task", "kwargs"], "arg_types": ["transformers.pipelines.question_answering.QuestionAnsweringPipeline", {".class": "UnionType", "items": ["transformers.modeling_utils.PreTrainedModel", "transformers.modeling_tf_utils.TFPreTrainedModel"], "uses_pep604_syntax": false}, "transformers.tokenization_utils.PreTrainedTokenizer", {".class": "UnionType", "items": ["transformers.modelcard.ModelCard", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of QuestionAnsweringPipeline", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_args_parser": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringPipeline._args_parser", "name": "_args_parser", "setter_type": null, "type": "transformers.pipelines.question_answering.QuestionAnsweringArgumentHandler"}}, "_forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringPipeline._forward", "name": "_forward", "type": null}}, "_sanitize_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "padding", "topk", "top_k", "doc_stride", "max_answer_len", "max_seq_len", "max_question_len", "handle_impossible_answer", "align_to_words", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringPipeline._sanitize_parameters", "name": "_sanitize_parameters", "type": null}}, "create_sample": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["question", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringPipeline.create_sample", "name": "create_sample", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["question", "context"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_sample of QuestionAnsweringPipeline", "ret_type": {".class": "UnionType", "items": ["transformers.data.processors.squad.SquadExample", {".class": "Instance", "args": ["transformers.data.processors.squad.SquadExample"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringPipeline.create_sample", "name": "create_sample", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["question", "context"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_sample of QuestionAnsweringPipeline", "ret_type": {".class": "UnionType", "items": ["transformers.data.processors.squad.SquadExample", {".class": "Instance", "args": ["transformers.data.processors.squad.SquadExample"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "default_input_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringPipeline.default_input_names", "name": "default_input_names", "setter_type": null, "type": "builtins.str"}}, "get_answer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "answers", "target"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringPipeline.get_answer", "name": "get_answer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "answers", "target"], "arg_types": ["transformers.pipelines.question_answering.QuestionAnsweringPipeline", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_answer of QuestionAnsweringPipeline", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_indices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "enc", "s", "e", "sequence_index", "align_to_words"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringPipeline.get_indices", "name": "get_indices", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "enc", "s", "e", "sequence_index", "align_to_words"], "arg_types": ["transformers.pipelines.question_answering.QuestionAnsweringPipeline", {".class": "AnyType", "missing_import_name": "transformers.pipelines.question_answering.tokenizers", "source_any": null, "type_of_any": 3}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_indices of QuestionAnsweringPipeline", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_impossible_answer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringPipeline.handle_impossible_answer", "name": "handle_impossible_answer", "setter_type": null, "type": "builtins.bool"}}, "postprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "model_outputs", "top_k", "handle_impossible_answer", "max_answer_len", "align_to_words"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringPipeline.postprocess", "name": "postprocess", "type": null}}, "preprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "example", "padding", "doc_stride", "max_question_len", "max_seq_len"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringPipeline.preprocess", "name": "preprocess", "type": null}}, "span_to_answer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "text", "start", "end"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.pipelines.question_answering.QuestionAnsweringPipeline.span_to_answer", "name": "span_to_answer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "text", "start", "end"], "arg_types": ["transformers.pipelines.question_answering.QuestionAnsweringPipeline", "builtins.str", "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "span_to_answer of QuestionAnsweringPipeline", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.pipelines.question_answering.QuestionAnsweringPipeline.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.pipelines.question_answering.QuestionAnsweringPipeline", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SquadExample": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.squad.SquadExample", "kind": "Gdef"}, "SquadFeatures": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.squad.SquadFeatures", "kind": "Gdef"}, "TFPreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_tf_utils.TFPreTrainedModel", "kind": "Gdef"}, "TF_MODEL_FOR_QUESTION_ANSWERING_MAPPING_NAMES": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.modeling_tf_auto.TF_MODEL_FOR_QUESTION_ANSWERING_MAPPING_NAMES", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.question_answering.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.question_answering.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.question_answering.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.question_answering.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.question_answering.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.pipelines.question_answering.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "add_end_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_end_docstrings", "kind": "Gdef"}, "build_pipeline_init_args": {".class": "SymbolTableNode", "cross_ref": "transformers.pipelines.base.build_pipeline_init_args", "kind": "Gdef"}, "decode_spans": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["start", "end", "topk", "max_answer_len", "undesired_tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.question_answering.decode_spans", "name": "decode_spans", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["start", "end", "topk", "max_answer_len", "undesired_tokens"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "builtins.int", "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "decode_spans", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "is_tf_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_tf_available", "kind": "Gdef"}, "is_tokenizers_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_tokenizers_available", "kind": "Gdef"}, "is_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_available", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.pipelines.question_answering.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "select_starts_ends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["start", "end", "p_mask", "attention_mask", "min_null_score", "top_k", "handle_impossible_answer", "max_answer_len"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.pipelines.question_answering.select_starts_ends", "name": "select_starts_ends", "type": null}}, "squad_convert_examples_to_features": {".class": "SymbolTableNode", "cross_ref": "transformers.data.processors.squad.squad_convert_examples_to_features", "kind": "Gdef"}, "tf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.pipelines.question_answering.tf", "name": "tf", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.pipelines.question_answering.tf", "source_any": null, "type_of_any": 3}}}, "tokenizers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.pipelines.question_answering.tokenizers", "name": "tokenizers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.pipelines.question_answering.tokenizers", "source_any": null, "type_of_any": 3}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/pipelines/question_answering.py"}