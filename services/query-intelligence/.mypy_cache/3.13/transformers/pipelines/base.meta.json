{"data_mtime": 1752049734, "dep_lines": [64, 71, 37, 40, 68, 74, 29, 32, 33, 34, 35, 36, 38, 39, 40, 70, 80, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 30, 67, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 62], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.models.auto.modeling_tf_auto", "transformers.models.auto.modeling_auto", "transformers.models.auto", "transformers.utils.logging", "torch.utils.data", "transformers.pipelines.pt_utils", "os.path", "transformers.dynamic_module_utils", "transformers.feature_extraction_utils", "transformers.generation", "transformers.image_processing_utils", "transformers.modelcard", "transformers.processing_utils", "transformers.tokenization_utils", "transformers.utils", "transformers.modeling_utils", "transformers.modeling_tf_utils", "collections", "copy", "csv", "importlib", "json", "os", "pickle", "sys", "traceback", "types", "warnings", "abc", "contextlib", "typing", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "_warnings", "functools", "genericpath", "io", "json.decoder", "logging", "posixpath", "torch._C", "torch._tensor", "torch.distributed", "torch.distributed.distributed_c10d", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.utils", "torch.utils.data.dataset", "transformers.configuration_utils", "transformers.generation.configuration_utils", "transformers.generation.tf_utils", "transformers.image_processing_base", "transformers.integrations", "transformers.integrations.peft", "transformers.models", "transformers.models.auto.configuration_auto", "transformers.tokenization_utils_base", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils"], "hash": "b87b90e18e3c72d304ef2671c5b6df33b011f3e0", "id": "transformers.pipelines.base", "ignore_all": true, "interface_hash": "36f0ce1c696f81222b06a5102877c35032dbe404", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/pipelines/base.py", "plugin_data": null, "size": 69230, "suppressed": ["tensorflow"], "version_id": "1.16.1"}