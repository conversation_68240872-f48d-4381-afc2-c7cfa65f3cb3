{"data_mtime": 1752049734, "dep_lines": [38, 22, 23, 24, 20, 21, 22, 30, 31, 14, 15, 17, 36, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 18, 28, 383], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 25, 25, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 25, 20], "dependencies": ["transformers.models.auto.modeling_auto", "transformers.utils.logging", "transformers.pipelines.audio_utils", "transformers.pipelines.base", "transformers.generation", "transformers.tokenization_utils", "transformers.utils", "transformers.feature_extraction_sequence_utils", "transformers.modeling_utils", "collections", "typing", "numpy", "torch", "builtins", "_collections_abc", "_frozen_importlib", "abc", "logging", "torch._C", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "transformers.configuration_utils", "transformers.feature_extraction_utils", "transformers.generation.configuration_utils", "transformers.generation.tf_utils", "transformers.image_processing_base", "transformers.image_processing_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.modelcard", "transformers.modeling_tf_utils", "transformers.processing_utils", "transformers.tokenization_utils_base", "transformers.utils.hub", "transformers.utils.import_utils"], "hash": "e8f592c810c0280ade2bb7473bb72a4f12bfca21", "id": "transformers.pipelines.automatic_speech_recognition", "ignore_all": true, "interface_hash": "9d159da6924404433dfcda4f249e2b2408517e1c", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/pipelines/automatic_speech_recognition.py", "plugin_data": null, "size": 32971, "suppressed": ["requests", "pyctcdecode", "<PERSON><PERSON><PERSON>"], "version_id": "1.16.1"}