{"data_mtime": 1752049734, "dep_lines": [24, 27, 5, 13, 5, 17, 19, 28, 1, 2, 3, 17, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 10, 5, 5, 10, 5, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.auto.modeling_auto", "transformers.models.auto.modeling_tf_auto", "transformers.utils.logging", "transformers.pipelines.base", "transformers.utils", "PIL.Image", "transformers.image_utils", "transformers.tf_utils", "warnings", "collections", "typing", "PIL", "torch", "builtins", "_frozen_importlib", "abc", "functools", "logging", "transformers.utils.doc", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "2b6ab2f46670d7681a46aa635f01349e9109ca91", "id": "transformers.pipelines.zero_shot_image_classification", "ignore_all": true, "interface_hash": "af05ad6844c94e0201bca9b948877c305c7708cd", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/pipelines/zero_shot_image_classification.py", "plugin_data": null, "size": 8503, "suppressed": [], "version_id": "1.16.1"}