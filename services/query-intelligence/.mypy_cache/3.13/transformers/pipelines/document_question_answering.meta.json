{"data_mtime": 1752049734, "dep_lines": [41, 21, 29, 30, 20, 21, 34, 36, 15, 16, 18, 34, 39, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 46], "dep_prios": [5, 10, 5, 5, 5, 5, 10, 5, 10, 5, 10, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.models.auto.modeling_auto", "transformers.utils.logging", "transformers.pipelines.base", "transformers.pipelines.question_answering", "transformers.generation", "transformers.utils", "PIL.Image", "transformers.image_utils", "re", "typing", "numpy", "PIL", "torch", "builtins", "_collections_abc", "_frozen_importlib", "abc", "collections", "enum", "functools", "logging", "transformers.generation.configuration_utils", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "5223e6beac277afb3430774df077aa6adecd509b", "id": "transformers.pipelines.document_question_answering", "ignore_all": true, "interface_hash": "d7d055f19a2e0ea9a3e5d5bf2f988d1e9b0b3830", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/pipelines/document_question_answering.py", "plugin_data": null, "size": 25673, "suppressed": ["pytesseract"], "version_id": "1.16.1"}