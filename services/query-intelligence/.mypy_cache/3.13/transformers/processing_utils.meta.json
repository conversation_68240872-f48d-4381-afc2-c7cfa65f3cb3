{"data_mtime": 1752049733, "dep_lines": [37, 51, 69, 1325, 31, 33, 34, 35, 36, 38, 44, 51, 73, 1325, 18, 19, 20, 21, 22, 23, 24, 25, 26, 29, 30, 1325, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 20, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 10, 10, 10, 10, 10, 5, 10, 5, 5, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.chat_template_utils", "transformers.utils.logging", "transformers.utils.deprecation", "transformers.models.auto", "huggingface_hub.errors", "transformers.audio_utils", "transformers.dynamic_module_utils", "transformers.feature_extraction_utils", "transformers.image_utils", "transformers.video_utils", "transformers.tokenization_utils_base", "transformers.utils", "transformers.modeling_utils", "transformers.models", "copy", "inspect", "json", "os", "sys", "typing", "warnings", "dataclasses", "pathlib", "numpy", "typing_extensions", "transformers", "builtins", "PIL", "PIL.Image", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "collections", "enum", "functools", "logging", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "5eafabf6f13eae4f1d561374359a32631278b8ed", "id": "transformers.processing_utils", "ignore_all": true, "interface_hash": "cfdd0abb4825a6b0f5c206946d539f8722376926", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/processing_utils.py", "plugin_data": null, "size": 85919, "suppressed": [], "version_id": "1.16.1"}