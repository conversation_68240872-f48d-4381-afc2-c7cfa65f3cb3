{".class": "MypyFile", "_fullname": "transformers.training_args_seq2seq", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "GenerationConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.generation.configuration_utils.GenerationConfig", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Seq2SeqTrainingArguments": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.training_args.TrainingArguments"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.training_args_seq2seq.Seq2SeqTrainingArguments", "name": "Seq2SeqTrainingArguments", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.training_args_seq2seq.Seq2SeqTrainingArguments", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 820, "name": "output_dir", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 826, "name": "overwrite_output_dir", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 836, "name": "do_train", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 837, "name": "do_eval", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 838, "name": "do_predict", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 839, "name": "eval_strategy", "type": {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 843, "name": "prediction_loss_only", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 848, "name": "per_device_train_batch_size", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 851, "name": "per_device_eval_batch_size", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 855, "name": "per_gpu_train_batch_size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 864, "name": "per_gpu_eval_batch_size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 874, "name": "gradient_accumulation_steps", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 878, "name": "eval_accumulation_steps", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 883, "name": "eval_delay", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 893, "name": "torch_empty_cache_steps", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 902, "name": "learning_rate", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 903, "name": "weight_decay", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 904, "name": "adam_beta1", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 905, "name": "adam_beta2", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 906, "name": "adam_epsilon", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 907, "name": "max_grad_norm", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 909, "name": "num_train_epochs", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 910, "name": "max_steps", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 914, "name": "lr_scheduler_type", "type": {".class": "UnionType", "items": ["transformers.trainer_utils.SchedulerType", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 918, "name": "lr_scheduler_kwargs", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 926, "name": "warmup_ratio", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 929, "name": "warmup_steps", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 931, "name": "log_level", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 942, "name": "log_level_replica", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 949, "name": "log_on_each_node", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 958, "name": "logging_dir", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 959, "name": "logging_strategy", "type": {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 963, "name": "logging_first_step", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 964, "name": "logging_steps", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 973, "name": "logging_nan_inf_filter", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 974, "name": "save_strategy", "type": {".class": "UnionType", "items": ["transformers.trainer_utils.SaveStrategy", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 978, "name": "save_steps", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 987, "name": "save_total_limit", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1001, "name": "save_safetensors", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1007, "name": "save_on_each_node", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1016, "name": "save_only_model", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1027, "name": "restore_callback_states_from_checkpoint", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1033, "name": "no_cuda", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1037, "name": "use_cpu", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1043, "name": "use_mps_device", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1050, "name": "seed", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1051, "name": "data_seed", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1052, "name": "jit_mode_eval", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1055, "name": "use_ipex", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1064, "name": "bf16", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1073, "name": "fp16", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1077, "name": "fp16_opt_level", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1086, "name": "half_precision_backend", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1093, "name": "bf16_full_eval", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1102, "name": "fp16_full_eval", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1106, "name": "tf32", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1115, "name": "local_rank", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1116, "name": "ddp_backend", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1123, "name": "tpu_num_cores", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1126, "name": "tpu_metrics_debug", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1134, "name": "debug", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["transformers.debug_utils.DebugOption"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1145, "name": "dataloader_drop_last", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1148, "name": "eval_steps", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1157, "name": "dataloader_num_workers", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1166, "name": "dataloader_prefetch_factor", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1175, "name": "past_index", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1180, "name": "run_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1186, "name": "disable_tqdm", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1190, "name": "remove_unused_columns", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1193, "name": "label_names", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1196, "name": "load_best_model_at_end", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1205, "name": "metric_for_best_model", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1208, "name": "greater_is_better", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1211, "name": "ignore_data_skip", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1220, "name": "fsdp", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["transformers.trainer_utils.FSDPOption"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1232, "name": "fsdp_min_num_params", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1241, "name": "fsdp_config", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1250, "name": "fsdp_transformer_layer_cls_to_wrap", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1259, "name": "accelerator_config", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1268, "name": "deepspeed", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1277, "name": "label_smoothing_factor", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1287, "name": "optim", "type": {".class": "UnionType", "items": ["transformers.training_args.OptimizerNames", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1291, "name": "optim_args", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1292, "name": "adafactor", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1293, "name": "group_by_length", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1297, "name": "length_column_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1301, "name": "report_to", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1304, "name": "ddp_find_unused_parameters", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1313, "name": "ddp_bucket_cap_mb", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1322, "name": "ddp_broadcast_buffers", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1331, "name": "dataloader_pin_memory", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1334, "name": "dataloader_persistent_workers", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1340, "name": "skip_memory_metrics", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1343, "name": "use_legacy_prediction_loop", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1346, "name": "push_to_hub", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1349, "name": "resume_from_checkpoint", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1353, "name": "hub_model_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1356, "name": "hub_strategy", "type": {".class": "UnionType", "items": ["transformers.trainer_utils.HubStrategy", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1360, "name": "hub_token", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1361, "name": "hub_private_repo", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1367, "name": "hub_always_push", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1371, "name": "hub_revision", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1377, "name": "gradient_checkpointing", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1383, "name": "gradient_checkpointing_kwargs", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1389, "name": "include_inputs_for_metrics", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1395, "name": "include_for_metrics", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1402, "name": "eval_do_concat_batches", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1409, "name": "fp16_backend", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1416, "name": "push_to_hub_model_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1419, "name": "push_to_hub_organization", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1422, "name": "push_to_hub_token", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1425, "name": "_n_gpu", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1426, "name": "mp_parameters", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1431, "name": "auto_find_batch_size", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1440, "name": "full_determinism", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1449, "name": "torch<PERSON><PERSON><PERSON>", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1455, "name": "ray_scope", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1468, "name": "ddp_timeout", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1474, "name": "torch_compile", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1477, "name": "torch_compile_backend", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1483, "name": "torch_compile_mode", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1490, "name": "include_tokens_per_second", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1495, "name": "include_num_input_tokens_seen", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1502, "name": "neftune_noise_alpha", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1509, "name": "optim_target_modules", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1516, "name": "batch_eval_metrics", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1521, "name": "eval_on_start", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1528, "name": "use_liger_kernel", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1533, "name": "liger_kernel_config", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1546, "name": "eval_use_gather_object", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1553, "name": "average_tokens_across_devices", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 51, "name": "sortish_sampler", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 52, "name": "predict_with_generate", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 55, "name": "generation_max_length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 64, "name": "generation_num_beams", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 73, "name": "generation_config", "type": {".class": "UnionType", "items": ["builtins.str", "pathlib.Path", "transformers.generation.configuration_utils.GenerationConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "transformers.training_args_seq2seq", "mro": ["transformers.training_args_seq2seq.Seq2SeqTrainingArguments", "transformers.training_args.TrainingArguments", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "transformers.training_args_seq2seq.Seq2SeqTrainingArguments.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "output_dir", "overwrite_output_dir", "do_train", "do_eval", "do_predict", "eval_strategy", "prediction_loss_only", "per_device_train_batch_size", "per_device_eval_batch_size", "per_gpu_train_batch_size", "per_gpu_eval_batch_size", "gradient_accumulation_steps", "eval_accumulation_steps", "eval_delay", "torch_empty_cache_steps", "learning_rate", "weight_decay", "adam_beta1", "adam_beta2", "adam_epsilon", "max_grad_norm", "num_train_epochs", "max_steps", "lr_scheduler_type", "lr_scheduler_kwargs", "warmup_ratio", "warmup_steps", "log_level", "log_level_replica", "log_on_each_node", "logging_dir", "logging_strategy", "logging_first_step", "logging_steps", "logging_nan_inf_filter", "save_strategy", "save_steps", "save_total_limit", "save_safetensors", "save_on_each_node", "save_only_model", "restore_callback_states_from_checkpoint", "no_cuda", "use_cpu", "use_mps_device", "seed", "data_seed", "jit_mode_eval", "use_ipex", "bf16", "fp16", "fp16_opt_level", "half_precision_backend", "bf16_full_eval", "fp16_full_eval", "tf32", "local_rank", "ddp_backend", "tpu_num_cores", "tpu_metrics_debug", "debug", "dataloader_drop_last", "eval_steps", "dataloader_num_workers", "dataloader_prefetch_factor", "past_index", "run_name", "disable_tqdm", "remove_unused_columns", "label_names", "load_best_model_at_end", "metric_for_best_model", "greater_is_better", "ignore_data_skip", "fsdp", "fsdp_min_num_params", "fsdp_config", "fsdp_transformer_layer_cls_to_wrap", "accelerator_config", "deepspeed", "label_smoothing_factor", "optim", "optim_args", "adafactor", "group_by_length", "length_column_name", "report_to", "ddp_find_unused_parameters", "ddp_bucket_cap_mb", "ddp_broadcast_buffers", "dataloader_pin_memory", "dataloader_persistent_workers", "skip_memory_metrics", "use_legacy_prediction_loop", "push_to_hub", "resume_from_checkpoint", "hub_model_id", "hub_strategy", "hub_token", "hub_private_repo", "hub_always_push", "hub_revision", "gradient_checkpointing", "gradient_checkpointing_kwargs", "include_inputs_for_metrics", "include_for_metrics", "eval_do_concat_batches", "fp16_backend", "push_to_hub_model_id", "push_to_hub_organization", "push_to_hub_token", "mp_parameters", "auto_find_batch_size", "full_determinism", "torch<PERSON><PERSON><PERSON>", "ray_scope", "ddp_timeout", "torch_compile", "torch_compile_backend", "torch_compile_mode", "include_tokens_per_second", "include_num_input_tokens_seen", "neftune_noise_alpha", "optim_target_modules", "batch_eval_metrics", "eval_on_start", "use_liger_kernel", "liger_kernel_config", "eval_use_gather_object", "average_tokens_across_devices", "sortish_sampler", "predict_with_generate", "generation_max_length", "generation_num_beams", "generation_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.training_args_seq2seq.Seq2SeqTrainingArguments.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "output_dir", "overwrite_output_dir", "do_train", "do_eval", "do_predict", "eval_strategy", "prediction_loss_only", "per_device_train_batch_size", "per_device_eval_batch_size", "per_gpu_train_batch_size", "per_gpu_eval_batch_size", "gradient_accumulation_steps", "eval_accumulation_steps", "eval_delay", "torch_empty_cache_steps", "learning_rate", "weight_decay", "adam_beta1", "adam_beta2", "adam_epsilon", "max_grad_norm", "num_train_epochs", "max_steps", "lr_scheduler_type", "lr_scheduler_kwargs", "warmup_ratio", "warmup_steps", "log_level", "log_level_replica", "log_on_each_node", "logging_dir", "logging_strategy", "logging_first_step", "logging_steps", "logging_nan_inf_filter", "save_strategy", "save_steps", "save_total_limit", "save_safetensors", "save_on_each_node", "save_only_model", "restore_callback_states_from_checkpoint", "no_cuda", "use_cpu", "use_mps_device", "seed", "data_seed", "jit_mode_eval", "use_ipex", "bf16", "fp16", "fp16_opt_level", "half_precision_backend", "bf16_full_eval", "fp16_full_eval", "tf32", "local_rank", "ddp_backend", "tpu_num_cores", "tpu_metrics_debug", "debug", "dataloader_drop_last", "eval_steps", "dataloader_num_workers", "dataloader_prefetch_factor", "past_index", "run_name", "disable_tqdm", "remove_unused_columns", "label_names", "load_best_model_at_end", "metric_for_best_model", "greater_is_better", "ignore_data_skip", "fsdp", "fsdp_min_num_params", "fsdp_config", "fsdp_transformer_layer_cls_to_wrap", "accelerator_config", "deepspeed", "label_smoothing_factor", "optim", "optim_args", "adafactor", "group_by_length", "length_column_name", "report_to", "ddp_find_unused_parameters", "ddp_bucket_cap_mb", "ddp_broadcast_buffers", "dataloader_pin_memory", "dataloader_persistent_workers", "skip_memory_metrics", "use_legacy_prediction_loop", "push_to_hub", "resume_from_checkpoint", "hub_model_id", "hub_strategy", "hub_token", "hub_private_repo", "hub_always_push", "hub_revision", "gradient_checkpointing", "gradient_checkpointing_kwargs", "include_inputs_for_metrics", "include_for_metrics", "eval_do_concat_batches", "fp16_backend", "push_to_hub_model_id", "push_to_hub_organization", "push_to_hub_token", "mp_parameters", "auto_find_batch_size", "full_determinism", "torch<PERSON><PERSON><PERSON>", "ray_scope", "ddp_timeout", "torch_compile", "torch_compile_backend", "torch_compile_mode", "include_tokens_per_second", "include_num_input_tokens_seen", "neftune_noise_alpha", "optim_target_modules", "batch_eval_metrics", "eval_on_start", "use_liger_kernel", "liger_kernel_config", "eval_use_gather_object", "average_tokens_across_devices", "sortish_sampler", "predict_with_generate", "generation_max_length", "generation_num_beams", "generation_config"], "arg_types": ["transformers.training_args_seq2seq.Seq2SeqTrainingArguments", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", {".class": "UnionType", "items": ["transformers.trainer_utils.SchedulerType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.int", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.float", "builtins.bool", {".class": "UnionType", "items": ["transformers.trainer_utils.SaveStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["transformers.debug_utils.DebugOption"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["transformers.trainer_utils.FSDPOption"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["transformers.training_args.OptimizerNames", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.trainer_utils.HubStrategy", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "pathlib.Path", "transformers.generation.configuration_utils.GenerationConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Seq2SeqTrainingArguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "transformers.training_args_seq2seq.Seq2SeqTrainingArguments.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "output_dir"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "overwrite_output_dir"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "do_train"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "do_eval"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "do_predict"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval_strategy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "prediction_loss_only"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "per_device_train_batch_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "per_device_eval_batch_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "per_gpu_train_batch_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "per_gpu_eval_batch_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gradient_accumulation_steps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval_accumulation_steps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval_delay"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "torch_empty_cache_steps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "learning_rate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "weight_decay"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "adam_beta1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "adam_beta2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "adam_epsilon"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max_grad_norm"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_train_epochs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max_steps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lr_scheduler_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lr_scheduler_kwargs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "warmup_ratio"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "warmup_steps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "log_level"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "log_level_replica"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "log_on_each_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "logging_dir"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "logging_strategy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "logging_first_step"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "logging_steps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "logging_nan_inf_filter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "save_strategy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "save_steps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "save_total_limit"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "save_safetensors"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "save_on_each_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "save_only_model"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "restore_callback_states_from_checkpoint"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "no_cuda"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_cpu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_mps_device"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "seed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "data_seed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "jit_mode_eval"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_ipex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bf16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fp16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fp16_opt_level"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "half_precision_backend"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bf16_full_eval"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fp16_full_eval"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tf32"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "local_rank"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ddp_backend"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tpu_num_cores"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tpu_metrics_debug"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "debug"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataloader_drop_last"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval_steps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataloader_num_workers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataloader_prefetch_factor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "past_index"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "run_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "disable_tqdm"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "remove_unused_columns"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "label_names"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "load_best_model_at_end"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "metric_for_best_model"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "greater_is_better"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ignore_data_skip"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fsdp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fsdp_min_num_params"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fsdp_config"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fsdp_transformer_layer_cls_to_wrap"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "accelerator_config"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "deepspeed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "label_smoothing_factor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "optim"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "optim_args"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "adafactor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "group_by_length"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "length_column_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "report_to"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ddp_find_unused_parameters"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ddp_bucket_cap_mb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ddp_broadcast_buffers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataloader_pin_memory"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataloader_persistent_workers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip_memory_metrics"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_legacy_prediction_loop"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "push_to_hub"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "resume_from_checkpoint"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hub_model_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hub_strategy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hub_token"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hub_private_repo"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hub_always_push"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hub_revision"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gradient_checkpointing"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gradient_checkpointing_kwargs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "include_inputs_for_metrics"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "include_for_metrics"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval_do_concat_batches"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fp16_backend"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "push_to_hub_model_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "push_to_hub_organization"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "push_to_hub_token"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mp_parameters"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto_find_batch_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "full_determinism"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "torch<PERSON><PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ray_scope"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ddp_timeout"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "torch_compile"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "torch_compile_backend"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "torch_compile_mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "include_tokens_per_second"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "include_num_input_tokens_seen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "neftune_noise_alpha"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "optim_target_modules"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "batch_eval_metrics"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval_on_start"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_liger_kernel"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "liger_kernel_config"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval_use_gather_object"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "average_tokens_across_devices"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sortish_sampler"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "predict_with_generate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "generation_max_length"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "generation_num_beams"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "generation_config"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["output_dir", "overwrite_output_dir", "do_train", "do_eval", "do_predict", "eval_strategy", "prediction_loss_only", "per_device_train_batch_size", "per_device_eval_batch_size", "per_gpu_train_batch_size", "per_gpu_eval_batch_size", "gradient_accumulation_steps", "eval_accumulation_steps", "eval_delay", "torch_empty_cache_steps", "learning_rate", "weight_decay", "adam_beta1", "adam_beta2", "adam_epsilon", "max_grad_norm", "num_train_epochs", "max_steps", "lr_scheduler_type", "lr_scheduler_kwargs", "warmup_ratio", "warmup_steps", "log_level", "log_level_replica", "log_on_each_node", "logging_dir", "logging_strategy", "logging_first_step", "logging_steps", "logging_nan_inf_filter", "save_strategy", "save_steps", "save_total_limit", "save_safetensors", "save_on_each_node", "save_only_model", "restore_callback_states_from_checkpoint", "no_cuda", "use_cpu", "use_mps_device", "seed", "data_seed", "jit_mode_eval", "use_ipex", "bf16", "fp16", "fp16_opt_level", "half_precision_backend", "bf16_full_eval", "fp16_full_eval", "tf32", "local_rank", "ddp_backend", "tpu_num_cores", "tpu_metrics_debug", "debug", "dataloader_drop_last", "eval_steps", "dataloader_num_workers", "dataloader_prefetch_factor", "past_index", "run_name", "disable_tqdm", "remove_unused_columns", "label_names", "load_best_model_at_end", "metric_for_best_model", "greater_is_better", "ignore_data_skip", "fsdp", "fsdp_min_num_params", "fsdp_config", "fsdp_transformer_layer_cls_to_wrap", "accelerator_config", "deepspeed", "label_smoothing_factor", "optim", "optim_args", "adafactor", "group_by_length", "length_column_name", "report_to", "ddp_find_unused_parameters", "ddp_bucket_cap_mb", "ddp_broadcast_buffers", "dataloader_pin_memory", "dataloader_persistent_workers", "skip_memory_metrics", "use_legacy_prediction_loop", "push_to_hub", "resume_from_checkpoint", "hub_model_id", "hub_strategy", "hub_token", "hub_private_repo", "hub_always_push", "hub_revision", "gradient_checkpointing", "gradient_checkpointing_kwargs", "include_inputs_for_metrics", "include_for_metrics", "eval_do_concat_batches", "fp16_backend", "push_to_hub_model_id", "push_to_hub_organization", "push_to_hub_token", "_n_gpu", "mp_parameters", "auto_find_batch_size", "full_determinism", "torch<PERSON><PERSON><PERSON>", "ray_scope", "ddp_timeout", "torch_compile", "torch_compile_backend", "torch_compile_mode", "include_tokens_per_second", "include_num_input_tokens_seen", "neftune_noise_alpha", "optim_target_modules", "batch_eval_metrics", "eval_on_start", "use_liger_kernel", "liger_kernel_config", "eval_use_gather_object", "average_tokens_across_devices", "sortish_sampler", "predict_with_generate", "generation_max_length", "generation_num_beams", "generation_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.training_args_seq2seq.Seq2SeqTrainingArguments.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["output_dir", "overwrite_output_dir", "do_train", "do_eval", "do_predict", "eval_strategy", "prediction_loss_only", "per_device_train_batch_size", "per_device_eval_batch_size", "per_gpu_train_batch_size", "per_gpu_eval_batch_size", "gradient_accumulation_steps", "eval_accumulation_steps", "eval_delay", "torch_empty_cache_steps", "learning_rate", "weight_decay", "adam_beta1", "adam_beta2", "adam_epsilon", "max_grad_norm", "num_train_epochs", "max_steps", "lr_scheduler_type", "lr_scheduler_kwargs", "warmup_ratio", "warmup_steps", "log_level", "log_level_replica", "log_on_each_node", "logging_dir", "logging_strategy", "logging_first_step", "logging_steps", "logging_nan_inf_filter", "save_strategy", "save_steps", "save_total_limit", "save_safetensors", "save_on_each_node", "save_only_model", "restore_callback_states_from_checkpoint", "no_cuda", "use_cpu", "use_mps_device", "seed", "data_seed", "jit_mode_eval", "use_ipex", "bf16", "fp16", "fp16_opt_level", "half_precision_backend", "bf16_full_eval", "fp16_full_eval", "tf32", "local_rank", "ddp_backend", "tpu_num_cores", "tpu_metrics_debug", "debug", "dataloader_drop_last", "eval_steps", "dataloader_num_workers", "dataloader_prefetch_factor", "past_index", "run_name", "disable_tqdm", "remove_unused_columns", "label_names", "load_best_model_at_end", "metric_for_best_model", "greater_is_better", "ignore_data_skip", "fsdp", "fsdp_min_num_params", "fsdp_config", "fsdp_transformer_layer_cls_to_wrap", "accelerator_config", "deepspeed", "label_smoothing_factor", "optim", "optim_args", "adafactor", "group_by_length", "length_column_name", "report_to", "ddp_find_unused_parameters", "ddp_bucket_cap_mb", "ddp_broadcast_buffers", "dataloader_pin_memory", "dataloader_persistent_workers", "skip_memory_metrics", "use_legacy_prediction_loop", "push_to_hub", "resume_from_checkpoint", "hub_model_id", "hub_strategy", "hub_token", "hub_private_repo", "hub_always_push", "hub_revision", "gradient_checkpointing", "gradient_checkpointing_kwargs", "include_inputs_for_metrics", "include_for_metrics", "eval_do_concat_batches", "fp16_backend", "push_to_hub_model_id", "push_to_hub_organization", "push_to_hub_token", "_n_gpu", "mp_parameters", "auto_find_batch_size", "full_determinism", "torch<PERSON><PERSON><PERSON>", "ray_scope", "ddp_timeout", "torch_compile", "torch_compile_backend", "torch_compile_mode", "include_tokens_per_second", "include_num_input_tokens_seen", "neftune_noise_alpha", "optim_target_modules", "batch_eval_metrics", "eval_on_start", "use_liger_kernel", "liger_kernel_config", "eval_use_gather_object", "average_tokens_across_devices", "sortish_sampler", "predict_with_generate", "generation_max_length", "generation_num_beams", "generation_config"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", {".class": "UnionType", "items": ["transformers.trainer_utils.SchedulerType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.int", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.float", "builtins.bool", {".class": "UnionType", "items": ["transformers.trainer_utils.SaveStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["transformers.debug_utils.DebugOption"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["transformers.trainer_utils.FSDPOption"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["transformers.training_args.OptimizerNames", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.trainer_utils.HubStrategy", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "pathlib.Path", "transformers.generation.configuration_utils.GenerationConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Seq2SeqTrainingArguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "transformers.training_args_seq2seq.Seq2SeqTrainingArguments.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["output_dir", "overwrite_output_dir", "do_train", "do_eval", "do_predict", "eval_strategy", "prediction_loss_only", "per_device_train_batch_size", "per_device_eval_batch_size", "per_gpu_train_batch_size", "per_gpu_eval_batch_size", "gradient_accumulation_steps", "eval_accumulation_steps", "eval_delay", "torch_empty_cache_steps", "learning_rate", "weight_decay", "adam_beta1", "adam_beta2", "adam_epsilon", "max_grad_norm", "num_train_epochs", "max_steps", "lr_scheduler_type", "lr_scheduler_kwargs", "warmup_ratio", "warmup_steps", "log_level", "log_level_replica", "log_on_each_node", "logging_dir", "logging_strategy", "logging_first_step", "logging_steps", "logging_nan_inf_filter", "save_strategy", "save_steps", "save_total_limit", "save_safetensors", "save_on_each_node", "save_only_model", "restore_callback_states_from_checkpoint", "no_cuda", "use_cpu", "use_mps_device", "seed", "data_seed", "jit_mode_eval", "use_ipex", "bf16", "fp16", "fp16_opt_level", "half_precision_backend", "bf16_full_eval", "fp16_full_eval", "tf32", "local_rank", "ddp_backend", "tpu_num_cores", "tpu_metrics_debug", "debug", "dataloader_drop_last", "eval_steps", "dataloader_num_workers", "dataloader_prefetch_factor", "past_index", "run_name", "disable_tqdm", "remove_unused_columns", "label_names", "load_best_model_at_end", "metric_for_best_model", "greater_is_better", "ignore_data_skip", "fsdp", "fsdp_min_num_params", "fsdp_config", "fsdp_transformer_layer_cls_to_wrap", "accelerator_config", "deepspeed", "label_smoothing_factor", "optim", "optim_args", "adafactor", "group_by_length", "length_column_name", "report_to", "ddp_find_unused_parameters", "ddp_bucket_cap_mb", "ddp_broadcast_buffers", "dataloader_pin_memory", "dataloader_persistent_workers", "skip_memory_metrics", "use_legacy_prediction_loop", "push_to_hub", "resume_from_checkpoint", "hub_model_id", "hub_strategy", "hub_token", "hub_private_repo", "hub_always_push", "hub_revision", "gradient_checkpointing", "gradient_checkpointing_kwargs", "include_inputs_for_metrics", "include_for_metrics", "eval_do_concat_batches", "fp16_backend", "push_to_hub_model_id", "push_to_hub_organization", "push_to_hub_token", "_n_gpu", "mp_parameters", "auto_find_batch_size", "full_determinism", "torch<PERSON><PERSON><PERSON>", "ray_scope", "ddp_timeout", "torch_compile", "torch_compile_backend", "torch_compile_mode", "include_tokens_per_second", "include_num_input_tokens_seen", "neftune_noise_alpha", "optim_target_modules", "batch_eval_metrics", "eval_on_start", "use_liger_kernel", "liger_kernel_config", "eval_use_gather_object", "average_tokens_across_devices", "sortish_sampler", "predict_with_generate", "generation_max_length", "generation_num_beams", "generation_config"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", {".class": "UnionType", "items": ["transformers.trainer_utils.SchedulerType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.int", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.float", "builtins.bool", {".class": "UnionType", "items": ["transformers.trainer_utils.SaveStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["transformers.debug_utils.DebugOption"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["transformers.trainer_utils.FSDPOption"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["transformers.training_args.OptimizerNames", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.trainer_utils.HubStrategy", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "pathlib.Path", "transformers.generation.configuration_utils.GenerationConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Seq2SeqTrainingArguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__replace__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "output_dir", "overwrite_output_dir", "do_train", "do_eval", "do_predict", "eval_strategy", "prediction_loss_only", "per_device_train_batch_size", "per_device_eval_batch_size", "per_gpu_train_batch_size", "per_gpu_eval_batch_size", "gradient_accumulation_steps", "eval_accumulation_steps", "eval_delay", "torch_empty_cache_steps", "learning_rate", "weight_decay", "adam_beta1", "adam_beta2", "adam_epsilon", "max_grad_norm", "num_train_epochs", "max_steps", "lr_scheduler_type", "lr_scheduler_kwargs", "warmup_ratio", "warmup_steps", "log_level", "log_level_replica", "log_on_each_node", "logging_dir", "logging_strategy", "logging_first_step", "logging_steps", "logging_nan_inf_filter", "save_strategy", "save_steps", "save_total_limit", "save_safetensors", "save_on_each_node", "save_only_model", "restore_callback_states_from_checkpoint", "no_cuda", "use_cpu", "use_mps_device", "seed", "data_seed", "jit_mode_eval", "use_ipex", "bf16", "fp16", "fp16_opt_level", "half_precision_backend", "bf16_full_eval", "fp16_full_eval", "tf32", "local_rank", "ddp_backend", "tpu_num_cores", "tpu_metrics_debug", "debug", "dataloader_drop_last", "eval_steps", "dataloader_num_workers", "dataloader_prefetch_factor", "past_index", "run_name", "disable_tqdm", "remove_unused_columns", "label_names", "load_best_model_at_end", "metric_for_best_model", "greater_is_better", "ignore_data_skip", "fsdp", "fsdp_min_num_params", "fsdp_config", "fsdp_transformer_layer_cls_to_wrap", "accelerator_config", "deepspeed", "label_smoothing_factor", "optim", "optim_args", "adafactor", "group_by_length", "length_column_name", "report_to", "ddp_find_unused_parameters", "ddp_bucket_cap_mb", "ddp_broadcast_buffers", "dataloader_pin_memory", "dataloader_persistent_workers", "skip_memory_metrics", "use_legacy_prediction_loop", "push_to_hub", "resume_from_checkpoint", "hub_model_id", "hub_strategy", "hub_token", "hub_private_repo", "hub_always_push", "hub_revision", "gradient_checkpointing", "gradient_checkpointing_kwargs", "include_inputs_for_metrics", "include_for_metrics", "eval_do_concat_batches", "fp16_backend", "push_to_hub_model_id", "push_to_hub_organization", "push_to_hub_token", "mp_parameters", "auto_find_batch_size", "full_determinism", "torch<PERSON><PERSON><PERSON>", "ray_scope", "ddp_timeout", "torch_compile", "torch_compile_backend", "torch_compile_mode", "include_tokens_per_second", "include_num_input_tokens_seen", "neftune_noise_alpha", "optim_target_modules", "batch_eval_metrics", "eval_on_start", "use_liger_kernel", "liger_kernel_config", "eval_use_gather_object", "average_tokens_across_devices", "sortish_sampler", "predict_with_generate", "generation_max_length", "generation_num_beams", "generation_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.training_args_seq2seq.Seq2SeqTrainingArguments.__replace__", "name": "__replace__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "output_dir", "overwrite_output_dir", "do_train", "do_eval", "do_predict", "eval_strategy", "prediction_loss_only", "per_device_train_batch_size", "per_device_eval_batch_size", "per_gpu_train_batch_size", "per_gpu_eval_batch_size", "gradient_accumulation_steps", "eval_accumulation_steps", "eval_delay", "torch_empty_cache_steps", "learning_rate", "weight_decay", "adam_beta1", "adam_beta2", "adam_epsilon", "max_grad_norm", "num_train_epochs", "max_steps", "lr_scheduler_type", "lr_scheduler_kwargs", "warmup_ratio", "warmup_steps", "log_level", "log_level_replica", "log_on_each_node", "logging_dir", "logging_strategy", "logging_first_step", "logging_steps", "logging_nan_inf_filter", "save_strategy", "save_steps", "save_total_limit", "save_safetensors", "save_on_each_node", "save_only_model", "restore_callback_states_from_checkpoint", "no_cuda", "use_cpu", "use_mps_device", "seed", "data_seed", "jit_mode_eval", "use_ipex", "bf16", "fp16", "fp16_opt_level", "half_precision_backend", "bf16_full_eval", "fp16_full_eval", "tf32", "local_rank", "ddp_backend", "tpu_num_cores", "tpu_metrics_debug", "debug", "dataloader_drop_last", "eval_steps", "dataloader_num_workers", "dataloader_prefetch_factor", "past_index", "run_name", "disable_tqdm", "remove_unused_columns", "label_names", "load_best_model_at_end", "metric_for_best_model", "greater_is_better", "ignore_data_skip", "fsdp", "fsdp_min_num_params", "fsdp_config", "fsdp_transformer_layer_cls_to_wrap", "accelerator_config", "deepspeed", "label_smoothing_factor", "optim", "optim_args", "adafactor", "group_by_length", "length_column_name", "report_to", "ddp_find_unused_parameters", "ddp_bucket_cap_mb", "ddp_broadcast_buffers", "dataloader_pin_memory", "dataloader_persistent_workers", "skip_memory_metrics", "use_legacy_prediction_loop", "push_to_hub", "resume_from_checkpoint", "hub_model_id", "hub_strategy", "hub_token", "hub_private_repo", "hub_always_push", "hub_revision", "gradient_checkpointing", "gradient_checkpointing_kwargs", "include_inputs_for_metrics", "include_for_metrics", "eval_do_concat_batches", "fp16_backend", "push_to_hub_model_id", "push_to_hub_organization", "push_to_hub_token", "mp_parameters", "auto_find_batch_size", "full_determinism", "torch<PERSON><PERSON><PERSON>", "ray_scope", "ddp_timeout", "torch_compile", "torch_compile_backend", "torch_compile_mode", "include_tokens_per_second", "include_num_input_tokens_seen", "neftune_noise_alpha", "optim_target_modules", "batch_eval_metrics", "eval_on_start", "use_liger_kernel", "liger_kernel_config", "eval_use_gather_object", "average_tokens_across_devices", "sortish_sampler", "predict_with_generate", "generation_max_length", "generation_num_beams", "generation_config"], "arg_types": ["transformers.training_args_seq2seq.Seq2SeqTrainingArguments", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", {".class": "UnionType", "items": ["transformers.trainer_utils.SchedulerType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.int", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.float", "builtins.bool", {".class": "UnionType", "items": ["transformers.trainer_utils.SaveStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["transformers.debug_utils.DebugOption"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["transformers.trainer_utils.FSDPOption"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["transformers.training_args.OptimizerNames", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.trainer_utils.HubStrategy", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "pathlib.Path", "transformers.generation.configuration_utils.GenerationConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__replace__ of Seq2SeqTrainingArguments", "ret_type": "transformers.training_args_seq2seq.Seq2SeqTrainingArguments", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "generation_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args_seq2seq.Seq2SeqTrainingArguments.generation_config", "name": "generation_config", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", "pathlib.Path", "transformers.generation.configuration_utils.GenerationConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "generation_max_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args_seq2seq.Seq2SeqTrainingArguments.generation_max_length", "name": "generation_max_length", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "generation_num_beams": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args_seq2seq.Seq2SeqTrainingArguments.generation_num_beams", "name": "generation_num_beams", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "predict_with_generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args_seq2seq.Seq2SeqTrainingArguments.predict_with_generate", "name": "predict_with_generate", "setter_type": null, "type": "builtins.bool"}}, "sortish_sampler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args_seq2seq.Seq2SeqTrainingArguments.sortish_sampler", "name": "sortish_sampler", "setter_type": null, "type": "builtins.bool"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.training_args_seq2seq.Seq2SeqTrainingArguments.to_dict", "name": "to_dict", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.training_args_seq2seq.Seq2SeqTrainingArguments.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.training_args_seq2seq.Seq2SeqTrainingArguments", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TrainingArguments": {".class": "SymbolTableNode", "cross_ref": "transformers.training_args.TrainingArguments", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.training_args_seq2seq.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.training_args_seq2seq.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.training_args_seq2seq.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.training_args_seq2seq.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.training_args_seq2seq.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.training_args_seq2seq.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "add_start_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "field": {".class": "SymbolTableNode", "cross_ref": "dataclasses.field", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.training_args_seq2seq.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/training_args_seq2seq.py"}