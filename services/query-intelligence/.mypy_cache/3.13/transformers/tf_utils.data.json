{".class": "MypyFile", "_fullname": "transformers.tf_utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BatchEncoding": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.BatchEncoding", "kind": "Gdef"}, "BatchFeature": {".class": "SymbolTableNode", "cross_ref": "transformers.feature_extraction_utils.BatchFeature", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.tf_utils.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.tf_utils.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.tf_utils.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.tf_utils.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.tf_utils.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.tf_utils.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "check_embeddings_within_bounds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["tensor", "embed_dim", "tensor_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.tf_utils.check_embeddings_within_bounds", "name": "check_embeddings_within_bounds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["tensor", "embed_dim", "tensor_name"], "arg_types": [{".class": "AnyType", "missing_import_name": "transformers.tf_utils.tf", "source_any": null, "type_of_any": 3}, "builtins.int", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "check_embeddings_within_bounds", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_batch_encoding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.tf_utils.convert_batch_encoding", "name": "convert_batch_encoding", "type": null}}, "expand_1d": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.tf_utils.expand_1d", "name": "expand_1d", "type": null}}, "flatten": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["input", "start_dim", "end_dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.tf_utils.flatten", "name": "flatten", "type": null}}, "functional_layernorm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["inputs", "weight", "bias", "epsilon", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.tf_utils.functional_layernorm", "name": "functional_layernorm", "type": null}}, "invert_attention_mask": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["encoder_attention_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.tf_utils.invert_attention_mask", "name": "invert_attention_mask", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["encoder_attention_mask"], "arg_types": [{".class": "AnyType", "missing_import_name": "transformers.tf_utils.tf", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "invert_attention_mask", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.tf_utils.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_attributes_from_hdf5_group": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["group", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.tf_utils.load_attributes_from_hdf5_group", "name": "load_attributes_from_hdf5_group", "type": null}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.tf_utils.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "save_attributes_to_hdf5_group": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["group", "name", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.tf_utils.save_attributes_to_hdf5_group", "name": "save_attributes_to_hdf5_group", "type": null}}, "scaled_dot_product_attention": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["query", "key", "value", "attn_mask", "dropout_p", "is_causal", "scale"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.tf_utils.scaled_dot_product_attention", "name": "scaled_dot_product_attention", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["query", "key", "value", "attn_mask", "dropout_p", "is_causal", "scale"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "scaled_dot_product_attention", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shape_list": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.tf_utils.shape_list", "name": "shape_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tensor"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.tf_utils.tf", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "shape_list", "ret_type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stable_softmax": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["logits", "axis", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.tf_utils.stable_softmax", "name": "stable_softmax", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["logits", "axis", "name"], "arg_types": [{".class": "AnyType", "missing_import_name": "transformers.tf_utils.tf", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "stable_softmax", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.tf_utils.tf", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.tf_utils.tf", "name": "tf", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.tf_utils.tf", "source_any": null, "type_of_any": 3}}}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/tf_utils.py"}