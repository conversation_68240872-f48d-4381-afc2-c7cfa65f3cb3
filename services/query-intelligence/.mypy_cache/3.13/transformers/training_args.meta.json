{"data_mtime": 1752049733, "dep_lines": [37, 62, 63, 2049, 28, 29, 37, 72, 78, 1871, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 71, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 81, 98, 107, 1984, 75, 76, 81, 98, 107, 81, 107, 1745], "dep_prios": [10, 5, 5, 20, 5, 5, 5, 10, 5, 20, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 10, 20, 5, 5, 20, 20, 20, 20, 20, 20], "dependencies": ["transformers.utils.logging", "transformers.utils.generic", "transformers.utils.import_utils", "transformers.integrations.deepspeed", "transformers.debug_utils", "transformers.trainer_utils", "transformers.utils", "torch.distributed", "transformers.trainer_pt_utils", "transformers.integrations", "contextlib", "json", "math", "os", "warnings", "dataclasses", "datetime", "enum", "pathlib", "typing", "huggingface_hub", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "_warnings", "abc", "functools", "huggingface_hub.hf_api", "io", "json.decoder", "logging", "posixpath", "torch._C", "torch._C._distributed_c10d", "torch.backends", "torch.backends.cuda", "torch.backends.cudnn", "torch.cuda", "torch.distributed.distributed_c10d", "torch.version", "torch.xpu", "transformers.integrations.integration_utils", "types"], "hash": "df307b8d6c7001c2c4abf4fb25c2af558a219359", "id": "transformers.training_args", "ignore_all": true, "interface_hash": "a91f252af6c0a7f851e6f961fc4c7872ab722c18", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/training_args.py", "plugin_data": null, "size": 160841, "suppressed": ["torch_xla.core.xla_model", "torch_xla.distributed.xla_backend", "smdistributed.modelparallel.torch", "accelerate.utils.constants", "accelerate.state", "accelerate.utils", "torch_xla.core", "torch_xla.distributed", "smdistributed.modelparallel", "torch_xla", "smdistributed", "apex"], "version_id": "1.16.1"}