{".class": "MypyFile", "_fullname": "transformers.tokenization_utils_fast", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ADDED_TOKENS_FILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.tokenization_utils_fast.ADDED_TOKENS_FILE", "name": "ADDED_TOKENS_FILE", "setter_type": null, "type": "builtins.str"}}, "AddedToken": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.AddedToken", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BatchEncoding": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.BatchEncoding", "kind": "Gdef"}, "BpeTrainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.tokenization_utils_fast.BpeTrainer", "name": "BpeTrainer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_fast.BpeTrainer", "source_any": null, "type_of_any": 3}}}, "DecoderFast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.tokenization_utils_fast.DecoderFast", "name": "DecoderFast", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_fast.DecoderFast", "source_any": null, "type_of_any": 3}}}, "EncodingFast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.tokenization_utils_fast.EncodingFast", "name": "EncodingFast", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_fast.EncodingFast", "source_any": null, "type_of_any": 3}}}, "INIT_TOKENIZER_DOCSTRING": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.INIT_TOKENIZER_DOCSTRING", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "MODEL_TO_TRAINER_MAPPING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.tokenization_utils_fast.MODEL_TO_TRAINER_MAPPING", "name": "MODEL_TO_TRAINER_MAPPING", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_fast.BpeTrainer", "source_any": {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_fast.BpeTrainer", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PaddingStrategy": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.PaddingStrategy", "kind": "Gdef"}, "PreTokenizedInput": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.PreTokenizedInput", "kind": "Gdef"}, "PreTokenizedInputPair": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.PreTokenizedInputPair", "kind": "Gdef"}, "PreTrainedTokenizer": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils.PreTrainedTokenizer", "kind": "Gdef"}, "PreTrainedTokenizerBase": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.PreTrainedTokenizerBase", "kind": "Gdef"}, "PreTrainedTokenizerFast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.tokenization_utils_base.PreTrainedTokenizerBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast", "name": "PreTrainedTokenizerFast", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.tokenization_utils_fast", "mro": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast", "transformers.tokenization_utils_base.PreTrainedTokenizerBase", "transformers.tokenization_utils_base.SpecialTokensMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.__bool__", "name": "__bool__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__bool__ of PreTrainedTokenizerFast", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.__init__", "name": "__init__", "type": null}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__len__ of PreTrainedTokenizerFast", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "new_tokens", "special_tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast._add_tokens", "name": "_add_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "new_tokens", "special_tokens"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_base.AddedToken", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_add_tokens of PreTrainedTokenizerFast", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_batch_encode_plus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "batch_text_or_text_pairs", "add_special_tokens", "padding_strategy", "truncation_strategy", "max_length", "stride", "is_split_into_words", "pad_to_multiple_of", "padding_side", "return_tensors", "return_token_type_ids", "return_attention_mask", "return_overflowing_tokens", "return_special_tokens_mask", "return_offsets_mapping", "return_length", "verbose", "split_special_tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast._batch_encode_plus", "name": "_batch_encode_plus", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "batch_text_or_text_pairs", "add_special_tokens", "padding_strategy", "truncation_strategy", "max_length", "stride", "is_split_into_words", "pad_to_multiple_of", "padding_side", "return_tensors", "return_token_type_ids", "return_attention_mask", "return_overflowing_tokens", "return_special_tokens_mask", "return_offsets_mapping", "return_length", "verbose", "split_special_tokens"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.TextInputPair"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInputPair"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", "transformers.utils.generic.PaddingStrategy", "transformers.tokenization_utils_base.TruncationStrategy", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_batch_encode_plus of PreTrainedTokenizerFast", "ret_type": "transformers.tokenization_utils_base.BatchEncoding", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "encoding", "return_token_type_ids", "return_attention_mask", "return_overflowing_tokens", "return_special_tokens_mask", "return_offsets_mapping", "return_length", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast._convert_encoding", "name": "_convert_encoding", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "encoding", "return_token_type_ids", "return_attention_mask", "return_overflowing_tokens", "return_special_tokens_mask", "return_offsets_mapping", "return_length", "verbose"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast", {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_fast.EncodingFast", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_encoding of PreTrainedTokenizerFast", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_fast.EncodingFast", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_id_to_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast._convert_id_to_token", "name": "_convert_id_to_token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_id_to_token of PreTrainedTokenizerFast", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_token_to_id_with_added_voc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast._convert_token_to_id_with_added_voc", "name": "_convert_token_to_id_with_added_voc", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "token"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_token_to_id_with_added_voc of PreTrainedTokenizerFast", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "token_ids", "skip_special_tokens", "clean_up_tokenization_spaces", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast._decode", "name": "_decode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "token_ids", "skip_special_tokens", "clean_up_tokenization_spaces", "kwargs"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_decode of PreTrainedTokenizerFast", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_decode_use_source_tokenizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast._decode_use_source_tokenizer", "name": "_decode_use_source_tokenizer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_encode_plus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "text", "text_pair", "add_special_tokens", "padding_strategy", "truncation_strategy", "max_length", "stride", "is_split_into_words", "pad_to_multiple_of", "padding_side", "return_tensors", "return_token_type_ids", "return_attention_mask", "return_overflowing_tokens", "return_special_tokens_mask", "return_offsets_mapping", "return_length", "verbose", "split_special_tokens", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast._encode_plus", "name": "_encode_plus", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "text", "text_pair", "add_special_tokens", "padding_strategy", "truncation_strategy", "max_length", "stride", "is_split_into_words", "pad_to_multiple_of", "padding_side", "return_tensors", "return_token_type_ids", "return_attention_mask", "return_overflowing_tokens", "return_special_tokens_mask", "return_offsets_mapping", "return_length", "verbose", "split_special_tokens", "kwargs"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.tokenization_utils_base.PreTokenizedInput"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "transformers.utils.generic.PaddingStrategy", "transformers.tokenization_utils_base.TruncationStrategy", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_encode_plus of PreTrainedTokenizerFast", "ret_type": "transformers.tokenization_utils_base.BatchEncoding", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_save_pretrained": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "save_directory", "file_names", "legacy_format", "filename_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast._save_pretrained", "name": "_save_pretrained", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "save_directory", "file_names", "legacy_format", "filename_prefix"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}, {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_save_pretrained of PreTrainedTokenizerFast", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tokenizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast._tokenizer", "name": "_tokenizer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "add_prefix_space": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.add_prefix_space", "name": "add_prefix_space", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "added_tokens_decoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.added_tokens_decoder", "name": "added_tokens_decoder", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "added_tokens_decoder of PreTrainedTokenizerFast", "ret_type": {".class": "Instance", "args": ["builtins.int", {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_base.AddedToken", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.added_tokens_decoder", "name": "added_tokens_decoder", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "added_tokens_decoder of PreTrainedTokenizerFast", "ret_type": {".class": "Instance", "args": ["builtins.int", {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_base.AddedToken", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "added_tokens_encoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.added_tokens_encoder", "name": "added_tokens_encoder", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "added_tokens_encoder of PreTrainedTokenizerFast", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.added_tokens_encoder", "name": "added_tokens_encoder", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "added_tokens_encoder of PreTrainedTokenizerFast", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "additional_special_tokens": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.additional_special_tokens", "name": "additional_special_tokens", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "backend_tokenizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.backend_tokenizer", "name": "backend_tokenizer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "backend_tokenizer of PreTrainedTokenizerFast", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_fast.TokenizerFast", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.backend_tokenizer", "name": "backend_tokenizer", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "backend_tokenizer of PreTrainedTokenizerFast", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_fast.TokenizerFast", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "can_save_slow_tokenizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.can_save_slow_tokenizer", "name": "can_save_slow_tokenizer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "can_save_slow_tokenizer of PreTrainedTokenizerFast", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.can_save_slow_tokenizer", "name": "can_save_slow_tokenizer", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "can_save_slow_tokenizer of PreTrainedTokenizerFast", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "convert_ids_to_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "ids", "skip_special_tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.convert_ids_to_tokens", "name": "convert_ids_to_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "ids", "skip_special_tokens"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast", {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_ids_to_tokens of PreTrainedTokenizerFast", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_tokens_to_ids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.convert_tokens_to_ids", "name": "convert_tokens_to_ids", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tokens"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_tokens_to_ids of PreTrainedTokenizerFast", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_tokens_to_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.convert_tokens_to_string", "name": "convert_tokens_to_string", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tokens"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_tokens_to_string of PreTrainedTokenizerFast", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decoder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.decoder", "name": "decoder", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "decoder of PreTrainedTokenizerFast", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_fast.DecoderFast", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.decoder", "name": "decoder", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "decoder of PreTrainedTokenizerFast", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_fast.DecoderFast", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_added_vocab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.get_added_vocab", "name": "get_added_vocab", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_added_vocab of PreTrainedTokenizerFast", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_vocab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.get_vocab", "name": "get_vocab", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_vocab of PreTrainedTokenizerFast", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_fast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.is_fast", "name": "is_fast", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_fast of PreTrainedTokenizerFast", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.is_fast", "name": "is_fast", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_fast of PreTrainedTokenizerFast", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "num_special_tokens_to_add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "pair"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.num_special_tokens_to_add", "name": "num_special_tokens_to_add", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "pair"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "num_special_tokens_to_add of PreTrainedTokenizerFast", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_truncation_and_padding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "padding_strategy", "truncation_strategy", "max_length", "stride", "pad_to_multiple_of", "padding_side"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.set_truncation_and_padding", "name": "set_truncation_and_padding", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "padding_strategy", "truncation_strategy", "max_length", "stride", "pad_to_multiple_of", "padding_side"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast", "transformers.utils.generic.PaddingStrategy", "transformers.tokenization_utils_base.TruncationStrategy", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_truncation_and_padding of PreTrainedTokenizerFast", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "slow_tokenizer_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.slow_tokenizer_class", "name": "slow_tokenizer_class", "setter_type": null, "type": "transformers.tokenization_utils.PreTrainedTokenizer"}}, "tokenize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "text", "pair", "add_special_tokens", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.tokenize", "name": "tokenize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "text", "pair", "add_special_tokens", "kwargs"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tokenize of PreTrainedTokenizerFast", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "train_new_from_iterator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "text_iterator", "vocab_size", "length", "new_special_tokens", "special_tokens_map", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.train_new_from_iterator", "name": "train_new_from_iterator", "type": null}}, "vocab": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.vocab", "name": "vocab", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "vocab of PreTrainedTokenizerFast", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.vocab", "name": "vocab", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "vocab of PreTrainedTokenizerFast", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "vocab_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.vocab_file", "name": "vocab_file", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "vocab_files_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.vocab_files_names", "name": "vocab_files_names", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "vocab_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.vocab_size", "name": "vocab_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "vocab_size of PreTrainedTokenizerFast", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.vocab_size", "name": "vocab_size", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.tokenization_utils_fast.PreTrainedTokenizerFast"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "vocab_size of PreTrainedTokenizerFast", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.tokenization_utils_fast.PreTrainedTokenizerFast", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SPECIAL_TOKENS_MAP_FILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.tokenization_utils_fast.SPECIAL_TOKENS_MAP_FILE", "name": "SPECIAL_TOKENS_MAP_FILE", "setter_type": null, "type": "builtins.str"}}, "SpecialTokensMixin": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.SpecialTokensMixin", "kind": "Gdef"}, "TIKTOKEN_VOCAB_FILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.tokenization_utils_fast.TIKTOKEN_VOCAB_FILE", "name": "TIKTOKEN_VOCAB_FILE", "setter_type": null, "type": "builtins.str"}}, "TOKENIZER_CONFIG_FILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.tokenization_utils_fast.TOKENIZER_CONFIG_FILE", "name": "TOKENIZER_CONFIG_FILE", "setter_type": null, "type": "builtins.str"}}, "TOKENIZER_FILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.tokenization_utils_fast.TOKENIZER_FILE", "name": "TOKENIZER_FILE", "setter_type": null, "type": "builtins.str"}}, "TextInput": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.TextInput", "kind": "Gdef"}, "TextInputPair": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.TextInputPair", "kind": "Gdef"}, "TokenizerFast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.tokenization_utils_fast.TokenizerFast", "name": "TokenizerFast", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_fast.TokenizerFast", "source_any": null, "type_of_any": 3}}}, "TruncationStrategy": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.TruncationStrategy", "kind": "Gdef"}, "UnigramTrainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.tokenization_utils_fast.UnigramTrainer", "name": "UnigramTrainer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_fast.UnigramTrainer", "source_any": null, "type_of_any": 3}}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "VOCAB_FILES_NAMES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.tokenization_utils_fast.VOCAB_FILES_NAMES", "name": "VOCAB_FILES_NAMES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "WordLevelTrainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.tokenization_utils_fast.WordLevelTrainer", "name": "WordLevelTrainer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_fast.WordLevelTrainer", "source_any": null, "type_of_any": 3}}}, "WordPieceTrainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.tokenization_utils_fast.WordPieceTrainer", "name": "WordPieceTrainer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_fast.WordPieceTrainer", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.tokenization_utils_fast.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.tokenization_utils_fast.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.tokenization_utils_fast.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.tokenization_utils_fast.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.tokenization_utils_fast.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.tokenization_utils_fast.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "add_end_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_end_docstrings", "kind": "Gdef"}, "convert_gguf_tokenizer": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.ggml.convert_gguf_tokenizer", "kind": "Gdef"}, "convert_slow_tokenizer": {".class": "SymbolTableNode", "cross_ref": "transformers.convert_slow_tokenizer.convert_slow_tokenizer", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "load_gguf_checkpoint": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_gguf_pytorch_utils.load_gguf_checkpoint", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.tokenization_utils_fast.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "pre_tokenizers_fast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.tokenization_utils_fast.pre_tokenizers_fast", "name": "pre_tokenizers_fast", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.tokenization_utils_fast.pre_tokenizers_fast", "source_any": null, "type_of_any": 3}}}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/tokenization_utils_fast.py"}