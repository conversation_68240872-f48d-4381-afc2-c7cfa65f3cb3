{".class": "MypyFile", "_fullname": "transformers.training_args", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<subclass of \"builtins.str\" and \"transformers.trainer_pt_utils.AcceleratorConfig\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str", "transformers.trainer_pt_utils.AcceleratorConfig"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.training_args.<subclass of \"builtins.str\" and \"transformers.trainer_pt_utils.AcceleratorConfig\">", "name": "<subclass of \"builtins.str\" and \"transformers.trainer_pt_utils.AcceleratorConfig\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "transformers.training_args.<subclass of \"builtins.str\" and \"transformers.trainer_pt_utils.AcceleratorConfig\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.training_args", "mro": ["transformers.training_args.<subclass of \"builtins.str\" and \"transformers.trainer_pt_utils.AcceleratorConfig\">", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "transformers.trainer_pt_utils.AcceleratorConfig", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"transformers.trainer_pt_utils.AcceleratorConfig\" and \"builtins.dict[Any, Any]\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_pt_utils.AcceleratorConfig", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.training_args.<subclass of \"transformers.trainer_pt_utils.AcceleratorConfig\" and \"builtins.dict[Any, Any]\">", "name": "<subclass of \"transformers.trainer_pt_utils.AcceleratorConfig\" and \"builtins.dict[Any, Any]\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "transformers.training_args.<subclass of \"transformers.trainer_pt_utils.AcceleratorConfig\" and \"builtins.dict[Any, Any]\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.training_args", "mro": ["transformers.training_args.<subclass of \"transformers.trainer_pt_utils.AcceleratorConfig\" and \"builtins.dict[Any, Any]\">", "transformers.trainer_pt_utils.AcceleratorConfig", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ACCELERATE_MIN_VERSION": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.ACCELERATE_MIN_VERSION", "kind": "Gdef"}, "AcceleratorConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_pt_utils.AcceleratorConfig", "kind": "Gdef"}, "AcceleratorState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.training_args.AcceleratorState", "name": "AcceleratorState", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.training_args.AcceleratorState", "source_any": null, "type_of_any": 3}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "DebugOption": {".class": "SymbolTableNode", "cross_ref": "transformers.debug_utils.DebugOption", "kind": "Gdef"}, "DistributedType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.training_args.DistributedType", "name": "DistributedType", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.training_args.DistributedType", "source_any": null, "type_of_any": 3}}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "EvaluationStrategy": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.EvaluationStrategy", "kind": "Gdef"}, "ExplicitEnum": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.ExplicitEnum", "kind": "Gdef"}, "FSDPOption": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.FSDPOption", "kind": "Gdef"}, "HubStrategy": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.HubStrategy", "kind": "Gdef"}, "IntervalStrategy": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.IntervalStrategy", "kind": "Gdef"}, "OptimizerNames": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.generic.ExplicitEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.training_args.OptimizerNames", "name": "OptimizerNames", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "transformers.training_args.OptimizerNames", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "transformers.training_args", "mro": ["transformers.training_args.OptimizerNames", "transformers.utils.generic.ExplicitEnum", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "ADAFACTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.ADAFACTOR", "name": "ADAFACTOR", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "adafactor"}, "type_ref": "builtins.str"}}}, "ADAGRAD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.ADAGRAD", "name": "ADAGRAD", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "adagrad"}, "type_ref": "builtins.str"}}}, "ADALOMO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.ADALOMO", "name": "ADALOMO", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "adalo<PERSON>"}, "type_ref": "builtins.str"}}}, "ADAMW_8BIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.ADAMW_8BIT", "name": "ADAMW_8BIT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "adamw_8bit"}, "type_ref": "builtins.str"}}}, "ADAMW_ANYPRECISION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.ADAMW_ANYPRECISION", "name": "ADAMW_ANYPRECISION", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "adamw_anyprecision"}, "type_ref": "builtins.str"}}}, "ADAMW_APEX_FUSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.ADAMW_APEX_FUSED", "name": "ADAMW_APEX_FUSED", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "adamw_apex_fused"}, "type_ref": "builtins.str"}}}, "ADAMW_BNB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.ADAMW_BNB", "name": "ADAMW_BNB", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "adamw_bnb_8bit"}, "type_ref": "builtins.str"}}}, "ADAMW_TORCH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.ADAMW_TORCH", "name": "ADAMW_TORCH", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "adamw_torch"}, "type_ref": "builtins.str"}}}, "ADAMW_TORCH_4BIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.ADAMW_TORCH_4BIT", "name": "ADAMW_TORCH_4BIT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "adamw_torch_4bit"}, "type_ref": "builtins.str"}}}, "ADAMW_TORCH_8BIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.ADAMW_TORCH_8BIT", "name": "ADAMW_TORCH_8BIT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "adamw_torch_8bit"}, "type_ref": "builtins.str"}}}, "ADAMW_TORCH_FUSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.ADAMW_TORCH_FUSED", "name": "ADAMW_TORCH_FUSED", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "adamw_torch_fused"}, "type_ref": "builtins.str"}}}, "ADAMW_TORCH_NPU_FUSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.ADAMW_TORCH_NPU_FUSED", "name": "ADAMW_TORCH_NPU_FUSED", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "adamw_torch_npu_fused"}, "type_ref": "builtins.str"}}}, "ADAMW_TORCH_XLA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.ADAMW_TORCH_XLA", "name": "ADAMW_TORCH_XLA", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "adam<PERSON>_torch_xla"}, "type_ref": "builtins.str"}}}, "ADEMAMIX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.ADEMAMIX", "name": "ADEMAMIX", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ademamix"}, "type_ref": "builtins.str"}}}, "ADEMAMIX_8BIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.ADEMAMIX_8BIT", "name": "ADEMAMIX_8BIT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ademamix_8bit"}, "type_ref": "builtins.str"}}}, "APOLLO_ADAMW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.APOLLO_ADAMW", "name": "APOLLO_ADAMW", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "apollo_adamw"}, "type_ref": "builtins.str"}}}, "APOLLO_ADAMW_LAYERWISE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.APOLLO_ADAMW_LAYERWISE", "name": "APOLLO_ADAMW_LAYERWISE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "apollo_adamw_layerwise"}, "type_ref": "builtins.str"}}}, "GALORE_ADAFACTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.GALORE_ADAFACTOR", "name": "GALORE_ADAFACTOR", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "galore_adafactor"}, "type_ref": "builtins.str"}}}, "GALORE_ADAFACTOR_LAYERWISE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.GALORE_ADAFACTOR_LAYERWISE", "name": "GALORE_ADAFACTOR_LAYERWISE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "galore_adafactor_layerwise"}, "type_ref": "builtins.str"}}}, "GALORE_ADAMW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.GALORE_ADAMW", "name": "GALORE_ADAMW", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "galore_adamw"}, "type_ref": "builtins.str"}}}, "GALORE_ADAMW_8BIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.GALORE_ADAMW_8BIT", "name": "GALORE_ADAMW_8BIT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "galore_adamw_8bit"}, "type_ref": "builtins.str"}}}, "GALORE_ADAMW_8BIT_LAYERWISE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.GALORE_ADAMW_8BIT_LAYERWISE", "name": "GALORE_ADAMW_8BIT_LAYERWISE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "galore_adamw_8bit_layerwise"}, "type_ref": "builtins.str"}}}, "GALORE_ADAMW_LAYERWISE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.GALORE_ADAMW_LAYERWISE", "name": "GALORE_ADAMW_LAYERWISE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "galore_adamw_layerwise"}, "type_ref": "builtins.str"}}}, "GROKADAMW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.GROKADAMW", "name": "GROKADAMW", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "grokadamw"}, "type_ref": "builtins.str"}}}, "LION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.LION", "name": "LION", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "lion_32bit"}, "type_ref": "builtins.str"}}}, "LION_8BIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.LION_8BIT", "name": "LION_8BIT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "lion_8bit"}, "type_ref": "builtins.str"}}}, "LOMO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.LOMO", "name": "LOMO", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "lomo"}, "type_ref": "builtins.str"}}}, "PAGED_ADAMW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.PAGED_ADAMW", "name": "PAGED_ADAMW", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "paged_adamw_32bit"}, "type_ref": "builtins.str"}}}, "PAGED_ADAMW_8BIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.PAGED_ADAMW_8BIT", "name": "PAGED_ADAMW_8BIT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "paged_adamw_8bit"}, "type_ref": "builtins.str"}}}, "PAGED_ADEMAMIX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.PAGED_ADEMAMIX", "name": "PAGED_ADEMAMIX", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "paged_ademamix_32bit"}, "type_ref": "builtins.str"}}}, "PAGED_ADEMAMIX_8BIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.PAGED_ADEMAMIX_8BIT", "name": "PAGED_ADEMAMIX_8BIT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "paged_ademamix_8bit"}, "type_ref": "builtins.str"}}}, "PAGED_LION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.PAGED_LION", "name": "PAGED_LION", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "paged_lion_32bit"}, "type_ref": "builtins.str"}}}, "PAGED_LION_8BIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.PAGED_LION_8BIT", "name": "PAGED_LION_8BIT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "paged_lion_8bit"}, "type_ref": "builtins.str"}}}, "RMSPROP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.RMSPROP", "name": "RMSPROP", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "rmsprop"}, "type_ref": "builtins.str"}}}, "RMSPROP_32BIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.RMSPROP_32BIT", "name": "RMSPROP_32BIT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "rmsprop_bnb_32bit"}, "type_ref": "builtins.str"}}}, "RMSPROP_8BIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.RMSPROP_8BIT", "name": "RMSPROP_8BIT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "rmsprop_bnb_8bit"}, "type_ref": "builtins.str"}}}, "RMSPROP_BNB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.RMSPROP_BNB", "name": "RMSPROP_BNB", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "rmsprop_bnb"}, "type_ref": "builtins.str"}}}, "SCHEDULE_FREE_ADAMW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.SCHEDULE_FREE_ADAMW", "name": "SCHEDULE_FREE_ADAMW", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "schedule_free_adamw"}, "type_ref": "builtins.str"}}}, "SCHEDULE_FREE_RADAM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.SCHEDULE_FREE_RADAM", "name": "SCHEDULE_FREE_RADAM", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "schedule_free_radam"}, "type_ref": "builtins.str"}}}, "SCHEDULE_FREE_SGD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.SCHEDULE_FREE_SGD", "name": "SCHEDULE_FREE_SGD", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "schedule_free_sgd"}, "type_ref": "builtins.str"}}}, "SGD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.OptimizerNames.SGD", "name": "SGD", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "sgd"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.training_args.OptimizerNames.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.training_args.OptimizerNames", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ParallelMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.training_args.ParallelMode", "name": "ParallelMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "transformers.training_args.ParallelMode", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "transformers.training_args", "mro": ["transformers.training_args.ParallelMode", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "DISTRIBUTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.ParallelMode.DISTRIBUTED", "name": "DISTRIBUTED", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "distributed"}, "type_ref": "builtins.str"}}}, "NOT_DISTRIBUTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.ParallelMode.NOT_DISTRIBUTED", "name": "NOT_DISTRIBUTED", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "not_distributed"}, "type_ref": "builtins.str"}}}, "NOT_PARALLEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.ParallelMode.NOT_PARALLEL", "name": "NOT_PARALLEL", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "not_parallel"}, "type_ref": "builtins.str"}}}, "SAGEMAKER_DATA_PARALLEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.ParallelMode.SAGEMAKER_DATA_PARALLEL", "name": "SAGEMAKER_DATA_PARALLEL", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "sagemaker_data_parallel"}, "type_ref": "builtins.str"}}}, "SAGEMAKER_MODEL_PARALLEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.ParallelMode.SAGEMAKER_MODEL_PARALLEL", "name": "SAGEMAKER_MODEL_PARALLEL", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "sagemaker_model_parallel"}, "type_ref": "builtins.str"}}}, "TPU": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.ParallelMode.TPU", "name": "TPU", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "tpu"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.training_args.ParallelMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.training_args.ParallelMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PartialState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.training_args.PartialState", "name": "PartialState", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.training_args.PartialState", "source_any": null, "type_of_any": 3}}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "SaveStrategy": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.SaveStrategy", "kind": "Gdef"}, "SchedulerType": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.SchedulerType", "kind": "Gdef"}, "TrainingArguments": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.training_args.TrainingArguments", "name": "TrainingArguments", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.training_args.TrainingArguments", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 820, "name": "output_dir", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 826, "name": "overwrite_output_dir", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 836, "name": "do_train", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 837, "name": "do_eval", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 838, "name": "do_predict", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 839, "name": "eval_strategy", "type": {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 843, "name": "prediction_loss_only", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 848, "name": "per_device_train_batch_size", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 851, "name": "per_device_eval_batch_size", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 855, "name": "per_gpu_train_batch_size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 864, "name": "per_gpu_eval_batch_size", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 874, "name": "gradient_accumulation_steps", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 878, "name": "eval_accumulation_steps", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 883, "name": "eval_delay", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 893, "name": "torch_empty_cache_steps", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 902, "name": "learning_rate", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 903, "name": "weight_decay", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 904, "name": "adam_beta1", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 905, "name": "adam_beta2", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 906, "name": "adam_epsilon", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 907, "name": "max_grad_norm", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 909, "name": "num_train_epochs", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 910, "name": "max_steps", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 914, "name": "lr_scheduler_type", "type": {".class": "UnionType", "items": ["transformers.trainer_utils.SchedulerType", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 918, "name": "lr_scheduler_kwargs", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 926, "name": "warmup_ratio", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 929, "name": "warmup_steps", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 931, "name": "log_level", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 942, "name": "log_level_replica", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 949, "name": "log_on_each_node", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 958, "name": "logging_dir", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 959, "name": "logging_strategy", "type": {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 963, "name": "logging_first_step", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 964, "name": "logging_steps", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 973, "name": "logging_nan_inf_filter", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 974, "name": "save_strategy", "type": {".class": "UnionType", "items": ["transformers.trainer_utils.SaveStrategy", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 978, "name": "save_steps", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 987, "name": "save_total_limit", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1001, "name": "save_safetensors", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1007, "name": "save_on_each_node", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1016, "name": "save_only_model", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1027, "name": "restore_callback_states_from_checkpoint", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1033, "name": "no_cuda", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1037, "name": "use_cpu", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1043, "name": "use_mps_device", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1050, "name": "seed", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1051, "name": "data_seed", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1052, "name": "jit_mode_eval", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1055, "name": "use_ipex", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1064, "name": "bf16", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1073, "name": "fp16", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1077, "name": "fp16_opt_level", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1086, "name": "half_precision_backend", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1093, "name": "bf16_full_eval", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1102, "name": "fp16_full_eval", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1106, "name": "tf32", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1115, "name": "local_rank", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1116, "name": "ddp_backend", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1123, "name": "tpu_num_cores", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1126, "name": "tpu_metrics_debug", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1134, "name": "debug", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["transformers.debug_utils.DebugOption"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1145, "name": "dataloader_drop_last", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1148, "name": "eval_steps", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1157, "name": "dataloader_num_workers", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1166, "name": "dataloader_prefetch_factor", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1175, "name": "past_index", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1180, "name": "run_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1186, "name": "disable_tqdm", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1190, "name": "remove_unused_columns", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1193, "name": "label_names", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1196, "name": "load_best_model_at_end", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1205, "name": "metric_for_best_model", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1208, "name": "greater_is_better", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1211, "name": "ignore_data_skip", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1220, "name": "fsdp", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["transformers.trainer_utils.FSDPOption"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1232, "name": "fsdp_min_num_params", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1241, "name": "fsdp_config", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1250, "name": "fsdp_transformer_layer_cls_to_wrap", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1259, "name": "accelerator_config", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1268, "name": "deepspeed", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1277, "name": "label_smoothing_factor", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1287, "name": "optim", "type": {".class": "UnionType", "items": ["transformers.training_args.OptimizerNames", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1291, "name": "optim_args", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1292, "name": "adafactor", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1293, "name": "group_by_length", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1297, "name": "length_column_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1301, "name": "report_to", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1304, "name": "ddp_find_unused_parameters", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1313, "name": "ddp_bucket_cap_mb", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1322, "name": "ddp_broadcast_buffers", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1331, "name": "dataloader_pin_memory", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1334, "name": "dataloader_persistent_workers", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1340, "name": "skip_memory_metrics", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1343, "name": "use_legacy_prediction_loop", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1346, "name": "push_to_hub", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1349, "name": "resume_from_checkpoint", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1353, "name": "hub_model_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1356, "name": "hub_strategy", "type": {".class": "UnionType", "items": ["transformers.trainer_utils.HubStrategy", "builtins.str"], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1360, "name": "hub_token", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1361, "name": "hub_private_repo", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1367, "name": "hub_always_push", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1371, "name": "hub_revision", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1377, "name": "gradient_checkpointing", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1383, "name": "gradient_checkpointing_kwargs", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1389, "name": "include_inputs_for_metrics", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1395, "name": "include_for_metrics", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1402, "name": "eval_do_concat_batches", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1409, "name": "fp16_backend", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1416, "name": "push_to_hub_model_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1419, "name": "push_to_hub_organization", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1422, "name": "push_to_hub_token", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1425, "name": "_n_gpu", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1426, "name": "mp_parameters", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1431, "name": "auto_find_batch_size", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1440, "name": "full_determinism", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1449, "name": "torch<PERSON><PERSON><PERSON>", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1455, "name": "ray_scope", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1468, "name": "ddp_timeout", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1474, "name": "torch_compile", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1477, "name": "torch_compile_backend", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1483, "name": "torch_compile_mode", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1490, "name": "include_tokens_per_second", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1495, "name": "include_num_input_tokens_seen", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1502, "name": "neftune_noise_alpha", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1509, "name": "optim_target_modules", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1516, "name": "batch_eval_metrics", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1521, "name": "eval_on_start", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1528, "name": "use_liger_kernel", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1533, "name": "liger_kernel_config", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1546, "name": "eval_use_gather_object", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 1553, "name": "average_tokens_across_devices", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "transformers.training_args", "mro": ["transformers.training_args.TrainingArguments", "builtins.object"], "names": {".class": "SymbolTable", "_VALID_DICT_FIELDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments._VALID_DICT_FIELDS", "name": "_VALID_DICT_FIELDS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "transformers.training_args.TrainingArguments.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "output_dir", "overwrite_output_dir", "do_train", "do_eval", "do_predict", "eval_strategy", "prediction_loss_only", "per_device_train_batch_size", "per_device_eval_batch_size", "per_gpu_train_batch_size", "per_gpu_eval_batch_size", "gradient_accumulation_steps", "eval_accumulation_steps", "eval_delay", "torch_empty_cache_steps", "learning_rate", "weight_decay", "adam_beta1", "adam_beta2", "adam_epsilon", "max_grad_norm", "num_train_epochs", "max_steps", "lr_scheduler_type", "lr_scheduler_kwargs", "warmup_ratio", "warmup_steps", "log_level", "log_level_replica", "log_on_each_node", "logging_dir", "logging_strategy", "logging_first_step", "logging_steps", "logging_nan_inf_filter", "save_strategy", "save_steps", "save_total_limit", "save_safetensors", "save_on_each_node", "save_only_model", "restore_callback_states_from_checkpoint", "no_cuda", "use_cpu", "use_mps_device", "seed", "data_seed", "jit_mode_eval", "use_ipex", "bf16", "fp16", "fp16_opt_level", "half_precision_backend", "bf16_full_eval", "fp16_full_eval", "tf32", "local_rank", "ddp_backend", "tpu_num_cores", "tpu_metrics_debug", "debug", "dataloader_drop_last", "eval_steps", "dataloader_num_workers", "dataloader_prefetch_factor", "past_index", "run_name", "disable_tqdm", "remove_unused_columns", "label_names", "load_best_model_at_end", "metric_for_best_model", "greater_is_better", "ignore_data_skip", "fsdp", "fsdp_min_num_params", "fsdp_config", "fsdp_transformer_layer_cls_to_wrap", "accelerator_config", "deepspeed", "label_smoothing_factor", "optim", "optim_args", "adafactor", "group_by_length", "length_column_name", "report_to", "ddp_find_unused_parameters", "ddp_bucket_cap_mb", "ddp_broadcast_buffers", "dataloader_pin_memory", "dataloader_persistent_workers", "skip_memory_metrics", "use_legacy_prediction_loop", "push_to_hub", "resume_from_checkpoint", "hub_model_id", "hub_strategy", "hub_token", "hub_private_repo", "hub_always_push", "hub_revision", "gradient_checkpointing", "gradient_checkpointing_kwargs", "include_inputs_for_metrics", "include_for_metrics", "eval_do_concat_batches", "fp16_backend", "push_to_hub_model_id", "push_to_hub_organization", "push_to_hub_token", "mp_parameters", "auto_find_batch_size", "full_determinism", "torch<PERSON><PERSON><PERSON>", "ray_scope", "ddp_timeout", "torch_compile", "torch_compile_backend", "torch_compile_mode", "include_tokens_per_second", "include_num_input_tokens_seen", "neftune_noise_alpha", "optim_target_modules", "batch_eval_metrics", "eval_on_start", "use_liger_kernel", "liger_kernel_config", "eval_use_gather_object", "average_tokens_across_devices"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.training_args.TrainingArguments.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "output_dir", "overwrite_output_dir", "do_train", "do_eval", "do_predict", "eval_strategy", "prediction_loss_only", "per_device_train_batch_size", "per_device_eval_batch_size", "per_gpu_train_batch_size", "per_gpu_eval_batch_size", "gradient_accumulation_steps", "eval_accumulation_steps", "eval_delay", "torch_empty_cache_steps", "learning_rate", "weight_decay", "adam_beta1", "adam_beta2", "adam_epsilon", "max_grad_norm", "num_train_epochs", "max_steps", "lr_scheduler_type", "lr_scheduler_kwargs", "warmup_ratio", "warmup_steps", "log_level", "log_level_replica", "log_on_each_node", "logging_dir", "logging_strategy", "logging_first_step", "logging_steps", "logging_nan_inf_filter", "save_strategy", "save_steps", "save_total_limit", "save_safetensors", "save_on_each_node", "save_only_model", "restore_callback_states_from_checkpoint", "no_cuda", "use_cpu", "use_mps_device", "seed", "data_seed", "jit_mode_eval", "use_ipex", "bf16", "fp16", "fp16_opt_level", "half_precision_backend", "bf16_full_eval", "fp16_full_eval", "tf32", "local_rank", "ddp_backend", "tpu_num_cores", "tpu_metrics_debug", "debug", "dataloader_drop_last", "eval_steps", "dataloader_num_workers", "dataloader_prefetch_factor", "past_index", "run_name", "disable_tqdm", "remove_unused_columns", "label_names", "load_best_model_at_end", "metric_for_best_model", "greater_is_better", "ignore_data_skip", "fsdp", "fsdp_min_num_params", "fsdp_config", "fsdp_transformer_layer_cls_to_wrap", "accelerator_config", "deepspeed", "label_smoothing_factor", "optim", "optim_args", "adafactor", "group_by_length", "length_column_name", "report_to", "ddp_find_unused_parameters", "ddp_bucket_cap_mb", "ddp_broadcast_buffers", "dataloader_pin_memory", "dataloader_persistent_workers", "skip_memory_metrics", "use_legacy_prediction_loop", "push_to_hub", "resume_from_checkpoint", "hub_model_id", "hub_strategy", "hub_token", "hub_private_repo", "hub_always_push", "hub_revision", "gradient_checkpointing", "gradient_checkpointing_kwargs", "include_inputs_for_metrics", "include_for_metrics", "eval_do_concat_batches", "fp16_backend", "push_to_hub_model_id", "push_to_hub_organization", "push_to_hub_token", "mp_parameters", "auto_find_batch_size", "full_determinism", "torch<PERSON><PERSON><PERSON>", "ray_scope", "ddp_timeout", "torch_compile", "torch_compile_backend", "torch_compile_mode", "include_tokens_per_second", "include_num_input_tokens_seen", "neftune_noise_alpha", "optim_target_modules", "batch_eval_metrics", "eval_on_start", "use_liger_kernel", "liger_kernel_config", "eval_use_gather_object", "average_tokens_across_devices"], "arg_types": ["transformers.training_args.TrainingArguments", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", {".class": "UnionType", "items": ["transformers.trainer_utils.SchedulerType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.int", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.float", "builtins.bool", {".class": "UnionType", "items": ["transformers.trainer_utils.SaveStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["transformers.debug_utils.DebugOption"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["transformers.trainer_utils.FSDPOption"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["transformers.training_args.OptimizerNames", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.trainer_utils.HubStrategy", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TrainingArguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "transformers.training_args.TrainingArguments.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "output_dir"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "overwrite_output_dir"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "do_train"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "do_eval"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "do_predict"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval_strategy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "prediction_loss_only"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "per_device_train_batch_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "per_device_eval_batch_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "per_gpu_train_batch_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "per_gpu_eval_batch_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gradient_accumulation_steps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval_accumulation_steps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval_delay"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "torch_empty_cache_steps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "learning_rate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "weight_decay"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "adam_beta1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "adam_beta2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "adam_epsilon"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max_grad_norm"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_train_epochs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max_steps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lr_scheduler_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lr_scheduler_kwargs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "warmup_ratio"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "warmup_steps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "log_level"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "log_level_replica"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "log_on_each_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "logging_dir"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "logging_strategy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "logging_first_step"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "logging_steps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "logging_nan_inf_filter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "save_strategy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "save_steps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "save_total_limit"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "save_safetensors"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "save_on_each_node"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "save_only_model"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "restore_callback_states_from_checkpoint"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "no_cuda"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_cpu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_mps_device"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "seed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "data_seed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "jit_mode_eval"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_ipex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bf16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fp16"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fp16_opt_level"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "half_precision_backend"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bf16_full_eval"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fp16_full_eval"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tf32"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "local_rank"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ddp_backend"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tpu_num_cores"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tpu_metrics_debug"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "debug"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataloader_drop_last"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval_steps"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataloader_num_workers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataloader_prefetch_factor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "past_index"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "run_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "disable_tqdm"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "remove_unused_columns"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "label_names"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "load_best_model_at_end"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "metric_for_best_model"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "greater_is_better"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ignore_data_skip"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fsdp"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fsdp_min_num_params"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fsdp_config"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fsdp_transformer_layer_cls_to_wrap"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "accelerator_config"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "deepspeed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "label_smoothing_factor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "optim"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "optim_args"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "adafactor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "group_by_length"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "length_column_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "report_to"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ddp_find_unused_parameters"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ddp_bucket_cap_mb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ddp_broadcast_buffers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataloader_pin_memory"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataloader_persistent_workers"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip_memory_metrics"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_legacy_prediction_loop"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "push_to_hub"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "resume_from_checkpoint"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hub_model_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hub_strategy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hub_token"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hub_private_repo"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hub_always_push"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hub_revision"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gradient_checkpointing"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gradient_checkpointing_kwargs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "include_inputs_for_metrics"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "include_for_metrics"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval_do_concat_batches"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fp16_backend"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "push_to_hub_model_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "push_to_hub_organization"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "push_to_hub_token"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mp_parameters"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto_find_batch_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "full_determinism"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "torch<PERSON><PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ray_scope"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ddp_timeout"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "torch_compile"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "torch_compile_backend"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "torch_compile_mode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "include_tokens_per_second"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "include_num_input_tokens_seen"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "neftune_noise_alpha"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "optim_target_modules"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "batch_eval_metrics"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval_on_start"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_liger_kernel"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "liger_kernel_config"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval_use_gather_object"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "average_tokens_across_devices"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.training_args.TrainingArguments.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-post_init of TrainingArguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["output_dir", "overwrite_output_dir", "do_train", "do_eval", "do_predict", "eval_strategy", "prediction_loss_only", "per_device_train_batch_size", "per_device_eval_batch_size", "per_gpu_train_batch_size", "per_gpu_eval_batch_size", "gradient_accumulation_steps", "eval_accumulation_steps", "eval_delay", "torch_empty_cache_steps", "learning_rate", "weight_decay", "adam_beta1", "adam_beta2", "adam_epsilon", "max_grad_norm", "num_train_epochs", "max_steps", "lr_scheduler_type", "lr_scheduler_kwargs", "warmup_ratio", "warmup_steps", "log_level", "log_level_replica", "log_on_each_node", "logging_dir", "logging_strategy", "logging_first_step", "logging_steps", "logging_nan_inf_filter", "save_strategy", "save_steps", "save_total_limit", "save_safetensors", "save_on_each_node", "save_only_model", "restore_callback_states_from_checkpoint", "no_cuda", "use_cpu", "use_mps_device", "seed", "data_seed", "jit_mode_eval", "use_ipex", "bf16", "fp16", "fp16_opt_level", "half_precision_backend", "bf16_full_eval", "fp16_full_eval", "tf32", "local_rank", "ddp_backend", "tpu_num_cores", "tpu_metrics_debug", "debug", "dataloader_drop_last", "eval_steps", "dataloader_num_workers", "dataloader_prefetch_factor", "past_index", "run_name", "disable_tqdm", "remove_unused_columns", "label_names", "load_best_model_at_end", "metric_for_best_model", "greater_is_better", "ignore_data_skip", "fsdp", "fsdp_min_num_params", "fsdp_config", "fsdp_transformer_layer_cls_to_wrap", "accelerator_config", "deepspeed", "label_smoothing_factor", "optim", "optim_args", "adafactor", "group_by_length", "length_column_name", "report_to", "ddp_find_unused_parameters", "ddp_bucket_cap_mb", "ddp_broadcast_buffers", "dataloader_pin_memory", "dataloader_persistent_workers", "skip_memory_metrics", "use_legacy_prediction_loop", "push_to_hub", "resume_from_checkpoint", "hub_model_id", "hub_strategy", "hub_token", "hub_private_repo", "hub_always_push", "hub_revision", "gradient_checkpointing", "gradient_checkpointing_kwargs", "include_inputs_for_metrics", "include_for_metrics", "eval_do_concat_batches", "fp16_backend", "push_to_hub_model_id", "push_to_hub_organization", "push_to_hub_token", "_n_gpu", "mp_parameters", "auto_find_batch_size", "full_determinism", "torch<PERSON><PERSON><PERSON>", "ray_scope", "ddp_timeout", "torch_compile", "torch_compile_backend", "torch_compile_mode", "include_tokens_per_second", "include_num_input_tokens_seen", "neftune_noise_alpha", "optim_target_modules", "batch_eval_metrics", "eval_on_start", "use_liger_kernel", "liger_kernel_config", "eval_use_gather_object", "average_tokens_across_devices"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.training_args.TrainingArguments.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["output_dir", "overwrite_output_dir", "do_train", "do_eval", "do_predict", "eval_strategy", "prediction_loss_only", "per_device_train_batch_size", "per_device_eval_batch_size", "per_gpu_train_batch_size", "per_gpu_eval_batch_size", "gradient_accumulation_steps", "eval_accumulation_steps", "eval_delay", "torch_empty_cache_steps", "learning_rate", "weight_decay", "adam_beta1", "adam_beta2", "adam_epsilon", "max_grad_norm", "num_train_epochs", "max_steps", "lr_scheduler_type", "lr_scheduler_kwargs", "warmup_ratio", "warmup_steps", "log_level", "log_level_replica", "log_on_each_node", "logging_dir", "logging_strategy", "logging_first_step", "logging_steps", "logging_nan_inf_filter", "save_strategy", "save_steps", "save_total_limit", "save_safetensors", "save_on_each_node", "save_only_model", "restore_callback_states_from_checkpoint", "no_cuda", "use_cpu", "use_mps_device", "seed", "data_seed", "jit_mode_eval", "use_ipex", "bf16", "fp16", "fp16_opt_level", "half_precision_backend", "bf16_full_eval", "fp16_full_eval", "tf32", "local_rank", "ddp_backend", "tpu_num_cores", "tpu_metrics_debug", "debug", "dataloader_drop_last", "eval_steps", "dataloader_num_workers", "dataloader_prefetch_factor", "past_index", "run_name", "disable_tqdm", "remove_unused_columns", "label_names", "load_best_model_at_end", "metric_for_best_model", "greater_is_better", "ignore_data_skip", "fsdp", "fsdp_min_num_params", "fsdp_config", "fsdp_transformer_layer_cls_to_wrap", "accelerator_config", "deepspeed", "label_smoothing_factor", "optim", "optim_args", "adafactor", "group_by_length", "length_column_name", "report_to", "ddp_find_unused_parameters", "ddp_bucket_cap_mb", "ddp_broadcast_buffers", "dataloader_pin_memory", "dataloader_persistent_workers", "skip_memory_metrics", "use_legacy_prediction_loop", "push_to_hub", "resume_from_checkpoint", "hub_model_id", "hub_strategy", "hub_token", "hub_private_repo", "hub_always_push", "hub_revision", "gradient_checkpointing", "gradient_checkpointing_kwargs", "include_inputs_for_metrics", "include_for_metrics", "eval_do_concat_batches", "fp16_backend", "push_to_hub_model_id", "push_to_hub_organization", "push_to_hub_token", "_n_gpu", "mp_parameters", "auto_find_batch_size", "full_determinism", "torch<PERSON><PERSON><PERSON>", "ray_scope", "ddp_timeout", "torch_compile", "torch_compile_backend", "torch_compile_mode", "include_tokens_per_second", "include_num_input_tokens_seen", "neftune_noise_alpha", "optim_target_modules", "batch_eval_metrics", "eval_on_start", "use_liger_kernel", "liger_kernel_config", "eval_use_gather_object", "average_tokens_across_devices"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", {".class": "UnionType", "items": ["transformers.trainer_utils.SchedulerType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.int", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.float", "builtins.bool", {".class": "UnionType", "items": ["transformers.trainer_utils.SaveStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["transformers.debug_utils.DebugOption"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["transformers.trainer_utils.FSDPOption"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["transformers.training_args.OptimizerNames", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.trainer_utils.HubStrategy", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of TrainingArguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "transformers.training_args.TrainingArguments.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["output_dir", "overwrite_output_dir", "do_train", "do_eval", "do_predict", "eval_strategy", "prediction_loss_only", "per_device_train_batch_size", "per_device_eval_batch_size", "per_gpu_train_batch_size", "per_gpu_eval_batch_size", "gradient_accumulation_steps", "eval_accumulation_steps", "eval_delay", "torch_empty_cache_steps", "learning_rate", "weight_decay", "adam_beta1", "adam_beta2", "adam_epsilon", "max_grad_norm", "num_train_epochs", "max_steps", "lr_scheduler_type", "lr_scheduler_kwargs", "warmup_ratio", "warmup_steps", "log_level", "log_level_replica", "log_on_each_node", "logging_dir", "logging_strategy", "logging_first_step", "logging_steps", "logging_nan_inf_filter", "save_strategy", "save_steps", "save_total_limit", "save_safetensors", "save_on_each_node", "save_only_model", "restore_callback_states_from_checkpoint", "no_cuda", "use_cpu", "use_mps_device", "seed", "data_seed", "jit_mode_eval", "use_ipex", "bf16", "fp16", "fp16_opt_level", "half_precision_backend", "bf16_full_eval", "fp16_full_eval", "tf32", "local_rank", "ddp_backend", "tpu_num_cores", "tpu_metrics_debug", "debug", "dataloader_drop_last", "eval_steps", "dataloader_num_workers", "dataloader_prefetch_factor", "past_index", "run_name", "disable_tqdm", "remove_unused_columns", "label_names", "load_best_model_at_end", "metric_for_best_model", "greater_is_better", "ignore_data_skip", "fsdp", "fsdp_min_num_params", "fsdp_config", "fsdp_transformer_layer_cls_to_wrap", "accelerator_config", "deepspeed", "label_smoothing_factor", "optim", "optim_args", "adafactor", "group_by_length", "length_column_name", "report_to", "ddp_find_unused_parameters", "ddp_bucket_cap_mb", "ddp_broadcast_buffers", "dataloader_pin_memory", "dataloader_persistent_workers", "skip_memory_metrics", "use_legacy_prediction_loop", "push_to_hub", "resume_from_checkpoint", "hub_model_id", "hub_strategy", "hub_token", "hub_private_repo", "hub_always_push", "hub_revision", "gradient_checkpointing", "gradient_checkpointing_kwargs", "include_inputs_for_metrics", "include_for_metrics", "eval_do_concat_batches", "fp16_backend", "push_to_hub_model_id", "push_to_hub_organization", "push_to_hub_token", "_n_gpu", "mp_parameters", "auto_find_batch_size", "full_determinism", "torch<PERSON><PERSON><PERSON>", "ray_scope", "ddp_timeout", "torch_compile", "torch_compile_backend", "torch_compile_mode", "include_tokens_per_second", "include_num_input_tokens_seen", "neftune_noise_alpha", "optim_target_modules", "batch_eval_metrics", "eval_on_start", "use_liger_kernel", "liger_kernel_config", "eval_use_gather_object", "average_tokens_across_devices"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", {".class": "UnionType", "items": ["transformers.trainer_utils.SchedulerType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.int", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.float", "builtins.bool", {".class": "UnionType", "items": ["transformers.trainer_utils.SaveStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["transformers.debug_utils.DebugOption"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["transformers.trainer_utils.FSDPOption"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["transformers.training_args.OptimizerNames", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.trainer_utils.HubStrategy", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of TrainingArguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.training_args.TrainingArguments.__post_init__", "name": "__post_init__", "type": null}}, "__replace__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "output_dir", "overwrite_output_dir", "do_train", "do_eval", "do_predict", "eval_strategy", "prediction_loss_only", "per_device_train_batch_size", "per_device_eval_batch_size", "per_gpu_train_batch_size", "per_gpu_eval_batch_size", "gradient_accumulation_steps", "eval_accumulation_steps", "eval_delay", "torch_empty_cache_steps", "learning_rate", "weight_decay", "adam_beta1", "adam_beta2", "adam_epsilon", "max_grad_norm", "num_train_epochs", "max_steps", "lr_scheduler_type", "lr_scheduler_kwargs", "warmup_ratio", "warmup_steps", "log_level", "log_level_replica", "log_on_each_node", "logging_dir", "logging_strategy", "logging_first_step", "logging_steps", "logging_nan_inf_filter", "save_strategy", "save_steps", "save_total_limit", "save_safetensors", "save_on_each_node", "save_only_model", "restore_callback_states_from_checkpoint", "no_cuda", "use_cpu", "use_mps_device", "seed", "data_seed", "jit_mode_eval", "use_ipex", "bf16", "fp16", "fp16_opt_level", "half_precision_backend", "bf16_full_eval", "fp16_full_eval", "tf32", "local_rank", "ddp_backend", "tpu_num_cores", "tpu_metrics_debug", "debug", "dataloader_drop_last", "eval_steps", "dataloader_num_workers", "dataloader_prefetch_factor", "past_index", "run_name", "disable_tqdm", "remove_unused_columns", "label_names", "load_best_model_at_end", "metric_for_best_model", "greater_is_better", "ignore_data_skip", "fsdp", "fsdp_min_num_params", "fsdp_config", "fsdp_transformer_layer_cls_to_wrap", "accelerator_config", "deepspeed", "label_smoothing_factor", "optim", "optim_args", "adafactor", "group_by_length", "length_column_name", "report_to", "ddp_find_unused_parameters", "ddp_bucket_cap_mb", "ddp_broadcast_buffers", "dataloader_pin_memory", "dataloader_persistent_workers", "skip_memory_metrics", "use_legacy_prediction_loop", "push_to_hub", "resume_from_checkpoint", "hub_model_id", "hub_strategy", "hub_token", "hub_private_repo", "hub_always_push", "hub_revision", "gradient_checkpointing", "gradient_checkpointing_kwargs", "include_inputs_for_metrics", "include_for_metrics", "eval_do_concat_batches", "fp16_backend", "push_to_hub_model_id", "push_to_hub_organization", "push_to_hub_token", "mp_parameters", "auto_find_batch_size", "full_determinism", "torch<PERSON><PERSON><PERSON>", "ray_scope", "ddp_timeout", "torch_compile", "torch_compile_backend", "torch_compile_mode", "include_tokens_per_second", "include_num_input_tokens_seen", "neftune_noise_alpha", "optim_target_modules", "batch_eval_metrics", "eval_on_start", "use_liger_kernel", "liger_kernel_config", "eval_use_gather_object", "average_tokens_across_devices"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.training_args.TrainingArguments.__replace__", "name": "__replace__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "output_dir", "overwrite_output_dir", "do_train", "do_eval", "do_predict", "eval_strategy", "prediction_loss_only", "per_device_train_batch_size", "per_device_eval_batch_size", "per_gpu_train_batch_size", "per_gpu_eval_batch_size", "gradient_accumulation_steps", "eval_accumulation_steps", "eval_delay", "torch_empty_cache_steps", "learning_rate", "weight_decay", "adam_beta1", "adam_beta2", "adam_epsilon", "max_grad_norm", "num_train_epochs", "max_steps", "lr_scheduler_type", "lr_scheduler_kwargs", "warmup_ratio", "warmup_steps", "log_level", "log_level_replica", "log_on_each_node", "logging_dir", "logging_strategy", "logging_first_step", "logging_steps", "logging_nan_inf_filter", "save_strategy", "save_steps", "save_total_limit", "save_safetensors", "save_on_each_node", "save_only_model", "restore_callback_states_from_checkpoint", "no_cuda", "use_cpu", "use_mps_device", "seed", "data_seed", "jit_mode_eval", "use_ipex", "bf16", "fp16", "fp16_opt_level", "half_precision_backend", "bf16_full_eval", "fp16_full_eval", "tf32", "local_rank", "ddp_backend", "tpu_num_cores", "tpu_metrics_debug", "debug", "dataloader_drop_last", "eval_steps", "dataloader_num_workers", "dataloader_prefetch_factor", "past_index", "run_name", "disable_tqdm", "remove_unused_columns", "label_names", "load_best_model_at_end", "metric_for_best_model", "greater_is_better", "ignore_data_skip", "fsdp", "fsdp_min_num_params", "fsdp_config", "fsdp_transformer_layer_cls_to_wrap", "accelerator_config", "deepspeed", "label_smoothing_factor", "optim", "optim_args", "adafactor", "group_by_length", "length_column_name", "report_to", "ddp_find_unused_parameters", "ddp_bucket_cap_mb", "ddp_broadcast_buffers", "dataloader_pin_memory", "dataloader_persistent_workers", "skip_memory_metrics", "use_legacy_prediction_loop", "push_to_hub", "resume_from_checkpoint", "hub_model_id", "hub_strategy", "hub_token", "hub_private_repo", "hub_always_push", "hub_revision", "gradient_checkpointing", "gradient_checkpointing_kwargs", "include_inputs_for_metrics", "include_for_metrics", "eval_do_concat_batches", "fp16_backend", "push_to_hub_model_id", "push_to_hub_organization", "push_to_hub_token", "mp_parameters", "auto_find_batch_size", "full_determinism", "torch<PERSON><PERSON><PERSON>", "ray_scope", "ddp_timeout", "torch_compile", "torch_compile_backend", "torch_compile_mode", "include_tokens_per_second", "include_num_input_tokens_seen", "neftune_noise_alpha", "optim_target_modules", "batch_eval_metrics", "eval_on_start", "use_liger_kernel", "liger_kernel_config", "eval_use_gather_object", "average_tokens_across_devices"], "arg_types": ["transformers.training_args.TrainingArguments", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", {".class": "UnionType", "items": ["transformers.trainer_utils.SchedulerType", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.int", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.float", "builtins.bool", {".class": "UnionType", "items": ["transformers.trainer_utils.SaveStrategy", "builtins.str"], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["transformers.debug_utils.DebugOption"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["transformers.trainer_utils.FSDPOption"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", {".class": "UnionType", "items": ["transformers.training_args.OptimizerNames", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.trainer_utils.HubStrategy", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__replace__ of TrainingArguments", "ret_type": "transformers.training_args.TrainingArguments", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.__repr__", "name": "__repr__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.training_args.TrainingArguments.__str__", "name": "__str__", "type": null}}, "_dict_torch_dtype_to_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "d"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.training_args.TrainingArguments._dict_torch_dtype_to_str", "name": "_dict_torch_dtype_to_str", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "d"], "arg_types": ["transformers.training_args.TrainingArguments", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_dict_torch_dtype_to_str of TrainingArguments", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_n_gpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments._n_gpu", "name": "_n_gpu", "setter_type": null, "type": "builtins.int"}}, "_no_sync_in_gradient_accumulation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.training_args.TrainingArguments._no_sync_in_gradient_accumulation", "name": "_no_sync_in_gradient_accumulation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.training_args.TrainingArguments._no_sync_in_gradient_accumulation", "name": "_no_sync_in_gradient_accumulation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_no_sync_in_gradient_accumulation of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_setup_devices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.training_args.TrainingArguments._setup_devices", "name": "_setup_devices", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_setup_devices of TrainingArguments", "ret_type": "torch._C.device", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.training_args.TrainingArguments._setup_devices", "name": "_setup_devices", "setter_type": null, "type": "transformers.utils.generic.cached_property"}}}, "accelerator_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.accelerator_config", "name": "accelerator_config", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "adafactor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.adafactor", "name": "adafactor", "setter_type": null, "type": "builtins.bool"}}, "adam_beta1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.adam_beta1", "name": "adam_beta1", "setter_type": null, "type": "builtins.float"}}, "adam_beta2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.adam_beta2", "name": "adam_beta2", "setter_type": null, "type": "builtins.float"}}, "adam_epsilon": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.adam_epsilon", "name": "adam_epsilon", "setter_type": null, "type": "builtins.float"}}, "auto_find_batch_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.auto_find_batch_size", "name": "auto_find_batch_size", "setter_type": null, "type": "builtins.bool"}}, "average_tokens_across_devices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.average_tokens_across_devices", "name": "average_tokens_across_devices", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "batch_eval_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.batch_eval_metrics", "name": "batch_eval_metrics", "setter_type": null, "type": "builtins.bool"}}, "bf16": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.bf16", "name": "bf16", "setter_type": null, "type": "builtins.bool"}}, "bf16_full_eval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.bf16_full_eval", "name": "bf16_full_eval", "setter_type": null, "type": "builtins.bool"}}, "data_seed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.data_seed", "name": "data_seed", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "dataloader_drop_last": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.dataloader_drop_last", "name": "dataloader_drop_last", "setter_type": null, "type": "builtins.bool"}}, "dataloader_num_workers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.dataloader_num_workers", "name": "dataloader_num_workers", "setter_type": null, "type": "builtins.int"}}, "dataloader_persistent_workers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.dataloader_persistent_workers", "name": "dataloader_persistent_workers", "setter_type": null, "type": "builtins.bool"}}, "dataloader_pin_memory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.dataloader_pin_memory", "name": "dataloader_pin_memory", "setter_type": null, "type": "builtins.bool"}}, "dataloader_prefetch_factor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.dataloader_prefetch_factor", "name": "dataloader_prefetch_factor", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ddp_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.ddp_backend", "name": "ddp_backend", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ddp_broadcast_buffers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.ddp_broadcast_buffers", "name": "ddp_broadcast_buffers", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ddp_bucket_cap_mb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.ddp_bucket_cap_mb", "name": "ddp_bucket_cap_mb", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ddp_find_unused_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.ddp_find_unused_parameters", "name": "ddp_find_unused_parameters", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ddp_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.ddp_timeout", "name": "ddp_timeout", "setter_type": null, "type": "builtins.int"}}, "ddp_timeout_delta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.training_args.TrainingArguments.ddp_timeout_delta", "name": "ddp_timeout_delta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ddp_timeout_delta of TrainingArguments", "ret_type": "datetime.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.training_args.TrainingArguments.ddp_timeout_delta", "name": "ddp_timeout_delta", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ddp_timeout_delta of TrainingArguments", "ret_type": "datetime.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.debug", "name": "debug", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["transformers.debug_utils.DebugOption"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}}}, "deepspeed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.deepspeed", "name": "deepspeed", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "deepspeed_plugin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.training_args.TrainingArguments.deepspeed_plugin", "name": "deepspeed_plugin", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "default_optim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.default_optim", "name": "default_optim", "setter_type": null, "type": "builtins.str"}}, "device": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.training_args.TrainingArguments.device", "name": "device", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "device of TrainingArguments", "ret_type": "torch._C.device", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.training_args.TrainingArguments.device", "name": "device", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "device of TrainingArguments", "ret_type": "torch._C.device", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "disable_tqdm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.disable_tqdm", "name": "disable_tqdm", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "distributed_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.training_args.TrainingArguments.distributed_state", "name": "distributed_state", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.training_args.PartialState", "source_any": {".class": "AnyType", "missing_import_name": "transformers.training_args.PartialState", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "do_eval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.do_eval", "name": "do_eval", "setter_type": null, "type": "builtins.bool"}}, "do_predict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.do_predict", "name": "do_predict", "setter_type": null, "type": "builtins.bool"}}, "do_train": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.do_train", "name": "do_train", "setter_type": null, "type": "builtins.bool"}}, "eval_accumulation_steps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.eval_accumulation_steps", "name": "eval_accumulation_steps", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "eval_batch_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.training_args.TrainingArguments.eval_batch_size", "name": "eval_batch_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "eval_batch_size of TrainingArguments", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.training_args.TrainingArguments.eval_batch_size", "name": "eval_batch_size", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "eval_batch_size of TrainingArguments", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "eval_delay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.eval_delay", "name": "eval_delay", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "eval_do_concat_batches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.eval_do_concat_batches", "name": "eval_do_concat_batches", "setter_type": null, "type": "builtins.bool"}}, "eval_on_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.eval_on_start", "name": "eval_on_start", "setter_type": null, "type": "builtins.bool"}}, "eval_steps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.eval_steps", "name": "eval_steps", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "eval_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.eval_strategy", "name": "eval_strategy", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}}}, "eval_use_gather_object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.eval_use_gather_object", "name": "eval_use_gather_object", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "fp16": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.fp16", "name": "fp16", "setter_type": null, "type": "builtins.bool"}}, "fp16_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.fp16_backend", "name": "fp16_backend", "setter_type": null, "type": "builtins.str"}}, "fp16_full_eval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.fp16_full_eval", "name": "fp16_full_eval", "setter_type": null, "type": "builtins.bool"}}, "fp16_opt_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.fp16_opt_level", "name": "fp16_opt_level", "setter_type": null, "type": "builtins.str"}}, "framework": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.framework", "name": "framework", "setter_type": null, "type": "builtins.str"}}, "fsdp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.fsdp", "name": "fsdp", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["transformers.trainer_utils.FSDPOption"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "fsdp_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.fsdp_config", "name": "fsdp_config", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "fsdp_min_num_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.fsdp_min_num_params", "name": "fsdp_min_num_params", "setter_type": null, "type": "builtins.int"}}, "fsdp_transformer_layer_cls_to_wrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.fsdp_transformer_layer_cls_to_wrap", "name": "fsdp_transformer_layer_cls_to_wrap", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "full_determinism": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.full_determinism", "name": "full_determinism", "setter_type": null, "type": "builtins.bool"}}, "get_process_log_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.training_args.TrainingArguments.get_process_log_level", "name": "get_process_log_level", "type": null}}, "get_warmup_steps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "num_training_steps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.training_args.TrainingArguments.get_warmup_steps", "name": "get_warmup_steps", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "num_training_steps"], "arg_types": ["transformers.training_args.TrainingArguments", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_warmup_steps of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gradient_accumulation_steps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.gradient_accumulation_steps", "name": "gradient_accumulation_steps", "setter_type": null, "type": "builtins.int"}}, "gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}, "gradient_checkpointing_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.gradient_checkpointing_kwargs", "name": "gradient_checkpointing_kwargs", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "greater_is_better": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.greater_is_better", "name": "greater_is_better", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "group_by_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.group_by_length", "name": "group_by_length", "setter_type": null, "type": "builtins.bool"}}, "half_precision_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.half_precision_backend", "name": "half_precision_backend", "setter_type": null, "type": "builtins.str"}}, "hf_deepspeed_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.training_args.TrainingArguments.hf_deepspeed_config", "name": "hf_deepspeed_config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "hub_always_push": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.hub_always_push", "name": "hub_always_push", "setter_type": null, "type": "builtins.bool"}}, "hub_model_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.hub_model_id", "name": "hub_model_id", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "hub_private_repo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.hub_private_repo", "name": "hub_private_repo", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "hub_revision": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.hub_revision", "name": "hub_revision", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "hub_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.hub_strategy", "name": "hub_strategy", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.trainer_utils.HubStrategy", "builtins.str"], "uses_pep604_syntax": false}}}, "hub_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.hub_token", "name": "hub_token", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ignore_data_skip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.ignore_data_skip", "name": "ignore_data_skip", "setter_type": null, "type": "builtins.bool"}}, "include_for_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.include_for_metrics", "name": "include_for_metrics", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "include_inputs_for_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.include_inputs_for_metrics", "name": "include_inputs_for_metrics", "setter_type": null, "type": "builtins.bool"}}, "include_num_input_tokens_seen": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.include_num_input_tokens_seen", "name": "include_num_input_tokens_seen", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "include_tokens_per_second": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.include_tokens_per_second", "name": "include_tokens_per_second", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "jit_mode_eval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.jit_mode_eval", "name": "jit_mode_eval", "setter_type": null, "type": "builtins.bool"}}, "label_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.label_names", "name": "label_names", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "label_smoothing_factor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.label_smoothing_factor", "name": "label_smoothing_factor", "setter_type": null, "type": "builtins.float"}}, "learning_rate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.learning_rate", "name": "learning_rate", "setter_type": null, "type": "builtins.float"}}, "length_column_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.length_column_name", "name": "length_column_name", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "liger_kernel_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.liger_kernel_config", "name": "liger_kernel_config", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "load_best_model_at_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.load_best_model_at_end", "name": "load_best_model_at_end", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "local_process_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.training_args.TrainingArguments.local_process_index", "name": "local_process_index", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.training_args.TrainingArguments.local_process_index", "name": "local_process_index", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "local_process_index of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "local_rank": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.local_rank", "name": "local_rank", "setter_type": null, "type": "builtins.int"}}, "log_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.log_level", "name": "log_level", "setter_type": null, "type": "builtins.str"}}, "log_level_replica": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.log_level_replica", "name": "log_level_replica", "setter_type": null, "type": "builtins.str"}}, "log_on_each_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.log_on_each_node", "name": "log_on_each_node", "setter_type": null, "type": "builtins.bool"}}, "logging_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.logging_dir", "name": "logging_dir", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "logging_first_step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.logging_first_step", "name": "logging_first_step", "setter_type": null, "type": "builtins.bool"}}, "logging_nan_inf_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.logging_nan_inf_filter", "name": "logging_nan_inf_filter", "setter_type": null, "type": "builtins.bool"}}, "logging_steps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.logging_steps", "name": "logging_steps", "setter_type": null, "type": "builtins.float"}}, "logging_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.logging_strategy", "name": "logging_strategy", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.trainer_utils.IntervalStrategy", "builtins.str"], "uses_pep604_syntax": false}}}, "lr_scheduler_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.lr_scheduler_kwargs", "name": "lr_scheduler_kwargs", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "lr_scheduler_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.lr_scheduler_type", "name": "lr_scheduler_type", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.trainer_utils.SchedulerType", "builtins.str"], "uses_pep604_syntax": false}}}, "main_process_first": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "local", "desc"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.training_args.TrainingArguments.main_process_first", "name": "main_process_first", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.training_args.TrainingArguments.main_process_first", "name": "main_process_first", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "local", "desc"], "arg_types": ["transformers.training_args.TrainingArguments", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "main_process_first of TrainingArguments", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "max_grad_norm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.max_grad_norm", "name": "max_grad_norm", "setter_type": null, "type": "builtins.float"}}, "max_steps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.max_steps", "name": "max_steps", "setter_type": null, "type": "builtins.int"}}, "metric_for_best_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.metric_for_best_model", "name": "metric_for_best_model", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "mp_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.mp_parameters", "name": "mp_parameters", "setter_type": null, "type": "builtins.str"}}, "n_gpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.training_args.TrainingArguments.n_gpu", "name": "n_gpu", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.training_args.TrainingArguments.n_gpu", "name": "n_gpu", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "n_gpu of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "neftune_noise_alpha": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.neftune_noise_alpha", "name": "neftune_noise_alpha", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "no_cuda": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.no_cuda", "name": "no_cuda", "setter_type": null, "type": "builtins.bool"}}, "num_train_epochs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.num_train_epochs", "name": "num_train_epochs", "setter_type": null, "type": "builtins.float"}}, "optim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.optim", "name": "optim", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.training_args.OptimizerNames", "builtins.str"], "uses_pep604_syntax": false}}}, "optim_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.optim_args", "name": "optim_args", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "optim_target_modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.optim_target_modules", "name": "optim_target_modules", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}}}, "output_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.output_dir", "name": "output_dir", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "overwrite_output_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.overwrite_output_dir", "name": "overwrite_output_dir", "setter_type": null, "type": "builtins.bool"}}, "parallel_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.training_args.TrainingArguments.parallel_mode", "name": "parallel_mode", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.training_args.TrainingArguments.parallel_mode", "name": "parallel_mode", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parallel_mode of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "past_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.past_index", "name": "past_index", "setter_type": null, "type": "builtins.int"}}, "per_device_eval_batch_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.per_device_eval_batch_size", "name": "per_device_eval_batch_size", "setter_type": null, "type": "builtins.int"}}, "per_device_train_batch_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.per_device_train_batch_size", "name": "per_device_train_batch_size", "setter_type": null, "type": "builtins.int"}}, "per_gpu_eval_batch_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.per_gpu_eval_batch_size", "name": "per_gpu_eval_batch_size", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "per_gpu_train_batch_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.per_gpu_train_batch_size", "name": "per_gpu_train_batch_size", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "place_model_on_device": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.training_args.TrainingArguments.place_model_on_device", "name": "place_model_on_device", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.training_args.TrainingArguments.place_model_on_device", "name": "place_model_on_device", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "place_model_on_device of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "prediction_loss_only": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.prediction_loss_only", "name": "prediction_loss_only", "setter_type": null, "type": "builtins.bool"}}, "process_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.training_args.TrainingArguments.process_index", "name": "process_index", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.training_args.TrainingArguments.process_index", "name": "process_index", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "process_index of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "push_to_hub": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.push_to_hub", "name": "push_to_hub", "setter_type": null, "type": "builtins.bool"}}, "push_to_hub_model_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.push_to_hub_model_id", "name": "push_to_hub_model_id", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "push_to_hub_organization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.push_to_hub_organization", "name": "push_to_hub_organization", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "push_to_hub_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.push_to_hub_token", "name": "push_to_hub_token", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ray_scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.ray_scope", "name": "ray_scope", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "remove_unused_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.remove_unused_columns", "name": "remove_unused_columns", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "report_to": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.report_to", "name": "report_to", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}}}, "restore_callback_states_from_checkpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.restore_callback_states_from_checkpoint", "name": "restore_callback_states_from_checkpoint", "setter_type": null, "type": "builtins.bool"}}, "resume_from_checkpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.resume_from_checkpoint", "name": "resume_from_checkpoint", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "run_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.run_name", "name": "run_name", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "save_on_each_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.save_on_each_node", "name": "save_on_each_node", "setter_type": null, "type": "builtins.bool"}}, "save_only_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.save_only_model", "name": "save_only_model", "setter_type": null, "type": "builtins.bool"}}, "save_safetensors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.save_safetensors", "name": "save_safetensors", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "save_steps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.save_steps", "name": "save_steps", "setter_type": null, "type": "builtins.float"}}, "save_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.save_strategy", "name": "save_strategy", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.trainer_utils.SaveStrategy", "builtins.str"], "uses_pep604_syntax": false}}}, "save_total_limit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.save_total_limit", "name": "save_total_limit", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "seed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.seed", "name": "seed", "setter_type": null, "type": "builtins.int"}}, "set_dataloader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "train_batch_size", "eval_batch_size", "drop_last", "num_workers", "pin_memory", "persistent_workers", "prefetch_factor", "auto_find_batch_size", "ignore_data_skip", "sampler_seed"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.training_args.TrainingArguments.set_dataloader", "name": "set_dataloader", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "train_batch_size", "eval_batch_size", "drop_last", "num_workers", "pin_memory", "persistent_workers", "prefetch_factor", "auto_find_batch_size", "ignore_data_skip", "sampler_seed"], "arg_types": ["transformers.training_args.TrainingArguments", "builtins.int", "builtins.int", "builtins.bool", "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_dataloader of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_evaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "strategy", "steps", "batch_size", "accumulation_steps", "delay", "loss_only", "jit_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.training_args.TrainingArguments.set_evaluate", "name": "set_evaluate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "strategy", "steps", "batch_size", "accumulation_steps", "delay", "loss_only", "jit_mode"], "arg_types": ["transformers.training_args.TrainingArguments", {".class": "UnionType", "items": ["builtins.str", "transformers.trainer_utils.IntervalStrategy"], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_evaluate of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_logging": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "strategy", "steps", "report_to", "level", "first_step", "nan_inf_filter", "on_each_node", "replica_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.training_args.TrainingArguments.set_logging", "name": "set_logging", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "strategy", "steps", "report_to", "level", "first_step", "nan_inf_filter", "on_each_node", "replica_level"], "arg_types": ["transformers.training_args.TrainingArguments", {".class": "UnionType", "items": ["builtins.str", "transformers.trainer_utils.IntervalStrategy"], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_logging of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_lr_scheduler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "num_epochs", "max_steps", "warmup_ratio", "warmup_steps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.training_args.TrainingArguments.set_lr_scheduler", "name": "set_lr_scheduler", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "num_epochs", "max_steps", "warmup_ratio", "warmup_steps"], "arg_types": ["transformers.training_args.TrainingArguments", {".class": "UnionType", "items": ["builtins.str", "transformers.trainer_utils.SchedulerType"], "uses_pep604_syntax": false}, "builtins.float", "builtins.int", "builtins.float", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_lr_scheduler of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_optimizer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "learning_rate", "weight_decay", "beta1", "beta2", "epsilon", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.training_args.TrainingArguments.set_optimizer", "name": "set_optimizer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "learning_rate", "weight_decay", "beta1", "beta2", "epsilon", "args"], "arg_types": ["transformers.training_args.TrainingArguments", {".class": "UnionType", "items": ["builtins.str", "transformers.training_args.OptimizerNames"], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_optimizer of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_push_to_hub": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "model_id", "strategy", "token", "private_repo", "always_push", "revision"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.training_args.TrainingArguments.set_push_to_hub", "name": "set_push_to_hub", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "model_id", "strategy", "token", "private_repo", "always_push", "revision"], "arg_types": ["transformers.training_args.TrainingArguments", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "transformers.trainer_utils.HubStrategy"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_push_to_hub of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "strategy", "steps", "total_limit", "on_each_node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.training_args.TrainingArguments.set_save", "name": "set_save", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "strategy", "steps", "total_limit", "on_each_node"], "arg_types": ["transformers.training_args.TrainingArguments", {".class": "UnionType", "items": ["builtins.str", "transformers.trainer_utils.IntervalStrategy"], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_save of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_testing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "batch_size", "loss_only", "jit_mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.training_args.TrainingArguments.set_testing", "name": "set_testing", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "batch_size", "loss_only", "jit_mode"], "arg_types": ["transformers.training_args.TrainingArguments", "builtins.int", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_testing of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_training": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "learning_rate", "batch_size", "weight_decay", "num_epochs", "max_steps", "gradient_accumulation_steps", "seed", "gradient_checkpointing"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.training_args.TrainingArguments.set_training", "name": "set_training", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "learning_rate", "batch_size", "weight_decay", "num_epochs", "max_steps", "gradient_accumulation_steps", "seed", "gradient_checkpointing"], "arg_types": ["transformers.training_args.TrainingArguments", "builtins.float", "builtins.int", "builtins.float", "builtins.float", "builtins.int", "builtins.int", "builtins.int", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_training of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "should_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.training_args.TrainingArguments.should_log", "name": "should_log", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.training_args.TrainingArguments.should_log", "name": "should_log", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "should_log of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "should_save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.training_args.TrainingArguments.should_save", "name": "should_save", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.training_args.TrainingArguments.should_save", "name": "should_save", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "should_save of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "skip_memory_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.skip_memory_metrics", "name": "skip_memory_metrics", "setter_type": null, "type": "builtins.bool"}}, "tf32": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.tf32", "name": "tf32", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.training_args.TrainingArguments.to_dict", "name": "to_dict", "type": null}}, "to_json_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.training_args.TrainingArguments.to_json_string", "name": "to_json_string", "type": null}}, "to_sanitized_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.training_args.TrainingArguments.to_sanitized_dict", "name": "to_sanitized_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_sanitized_dict of TrainingArguments", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch_compile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.torch_compile", "name": "torch_compile", "setter_type": null, "type": "builtins.bool"}}, "torch_compile_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.torch_compile_backend", "name": "torch_compile_backend", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "torch_compile_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.torch_compile_mode", "name": "torch_compile_mode", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "torch_empty_cache_steps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.torch_empty_cache_steps", "name": "torch_empty_cache_steps", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "torchdynamo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.torchdynamo", "name": "torch<PERSON><PERSON><PERSON>", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "tpu_metrics_debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.tpu_metrics_debug", "name": "tpu_metrics_debug", "setter_type": null, "type": "builtins.bool"}}, "tpu_num_cores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.tpu_num_cores", "name": "tpu_num_cores", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "train_batch_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "transformers.training_args.TrainingArguments.train_batch_size", "name": "train_batch_size", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "train_batch_size of TrainingArguments", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.training_args.TrainingArguments.train_batch_size", "name": "train_batch_size", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "train_batch_size of TrainingArguments", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "use_cpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.use_cpu", "name": "use_cpu", "setter_type": null, "type": "builtins.bool"}}, "use_ipex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.use_ipex", "name": "use_ipex", "setter_type": null, "type": "builtins.bool"}}, "use_legacy_prediction_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.use_legacy_prediction_loop", "name": "use_legacy_prediction_loop", "setter_type": null, "type": "builtins.bool"}}, "use_liger_kernel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.use_liger_kernel", "name": "use_liger_kernel", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "use_mps_device": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.use_mps_device", "name": "use_mps_device", "setter_type": null, "type": "builtins.bool"}}, "warmup_ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.warmup_ratio", "name": "warmup_ratio", "setter_type": null, "type": "builtins.float"}}, "warmup_steps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.warmup_steps", "name": "warmup_steps", "setter_type": null, "type": "builtins.int"}}, "weight_decay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.training_args.TrainingArguments.weight_decay", "name": "weight_decay", "setter_type": null, "type": "builtins.float"}}, "world_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "transformers.training_args.TrainingArguments.world_size", "name": "world_size", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "transformers.training_args.TrainingArguments.world_size", "name": "world_size", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.training_args.TrainingArguments"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "world_size of TrainingArguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "xla_fsdp_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.training_args.TrainingArguments.xla_fsdp_config", "name": "xla_fsdp_config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.training_args.TrainingArguments.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.training_args.TrainingArguments", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.training_args.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.training_args.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.training_args.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.training_args.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.training_args.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.training_args.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_convert_str_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["passed_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.training_args._convert_str_dict", "name": "_convert_str_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["passed_value"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_str_dict", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "asdict": {".class": "SymbolTableNode", "cross_ref": "dataclasses.asdict", "kind": "Gdef"}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.cached_property", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "default_logdir": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.training_args.default_logdir", "name": "default_logdir", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "default_logdir", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dist": {".class": "SymbolTableNode", "cross_ref": "torch.distributed", "kind": "Gdef"}, "field": {".class": "SymbolTableNode", "cross_ref": "dataclasses.field", "kind": "Gdef"}, "fields": {".class": "SymbolTableNode", "cross_ref": "dataclasses.fields", "kind": "Gdef"}, "get_full_repo_name": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.hf_api.get_full_repo_name", "kind": "Gdef"}, "get_int_from_env": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["env_keys", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.training_args.get_int_from_env", "name": "get_int_from_env", "type": null}}, "get_xla_device_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.training_args.get_xla_device_type", "name": "get_xla_device_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["device"], "arg_types": ["torch._C.device"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_xla_device_type", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_accelerate_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_accelerate_available", "kind": "Gdef"}, "is_apex_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_apex_available", "kind": "Gdef"}, "is_ipex_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_ipex_available", "kind": "Gdef"}, "is_optimum_neuron_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_optimum_neuron_available", "kind": "Gdef"}, "is_safetensors_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_safetensors_available", "kind": "Gdef"}, "is_sagemaker_dp_enabled": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_sagemaker_dp_enabled", "kind": "Gdef"}, "is_sagemaker_mp_enabled": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_sagemaker_mp_enabled", "kind": "Gdef"}, "is_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_available", "kind": "Gdef"}, "is_torch_bf16_gpu_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_bf16_gpu_available", "kind": "Gdef"}, "is_torch_cuda_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_cuda_available", "kind": "Gdef"}, "is_torch_hpu_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_hpu_available", "kind": "Gdef"}, "is_torch_mlu_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_mlu_available", "kind": "Gdef"}, "is_torch_mps_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_mps_available", "kind": "Gdef"}, "is_torch_musa_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_musa_available", "kind": "Gdef"}, "is_torch_neuroncore_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_neuroncore_available", "kind": "Gdef"}, "is_torch_npu_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_npu_available", "kind": "Gdef"}, "is_torch_tf32_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_tf32_available", "kind": "Gdef"}, "is_torch_xla_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_xla_available", "kind": "Gdef"}, "is_torch_xpu_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_xpu_available", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "log_levels": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.log_levels", "name": "log_levels", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "requires_backends": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.requires_backends", "kind": "Gdef"}, "smp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.training_args.smp", "name": "smp", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.training_args.smp", "source_any": null, "type_of_any": 3}}}, "strtobool": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.strtobool", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "trainer_log_levels": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.training_args.trainer_log_levels", "name": "trainer_log_levels", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "xbn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.training_args.xbn", "name": "xbn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.training_args.xbn", "source_any": null, "type_of_any": 3}}}, "xm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.training_args.xm", "name": "xm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.training_args.xm", "source_any": null, "type_of_any": 3}}}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/training_args.py"}