{"data_mtime": 1752049734, "dep_lines": [43, 52, 15, 21, 22, 29, 42, 43, 16, 17, 19, 59, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 65, 67], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["transformers.utils.logging", "transformers.utils.import_utils", "collections.abc", "transformers.image_processing_utils", "transformers.image_transforms", "transformers.image_utils", "transformers.processing_utils", "transformers.utils", "functools", "typing", "numpy", "torch", "builtins", "PIL", "PIL.Image", "_collections_abc", "_frozen_importlib", "abc", "collections", "enum", "logging", "torch._C", "torch._tensor", "transformers.feature_extraction_utils", "transformers.image_processing_base", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub"], "hash": "805abad98bcac060f3c9e40e53420bb3c5a78d4c", "id": "transformers.image_processing_utils_fast", "ignore_all": true, "interface_hash": "00adaed6b0332396c6fdab20df983c316cbe9ed3", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/image_processing_utils_fast.py", "plugin_data": null, "size": 26330, "suppressed": ["torchvision.transforms.v2", "torchvision.transforms"], "version_id": "1.16.1"}