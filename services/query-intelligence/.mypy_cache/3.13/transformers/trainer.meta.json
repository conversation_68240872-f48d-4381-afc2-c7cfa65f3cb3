{"data_mtime": 1752049734, "dep_lines": [69, 2990, 55, 59, 65, 66, 137, 178, 179, 180, 187, 22, 34, 42, 48, 51, 53, 54, 58, 60, 61, 62, 63, 64, 67, 68, 73, 74, 75, 78, 79, 89, 111, 136, 137, 219, 1414, 2036, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 36, 37, 48, 49, 50, 53, 57, 219, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1983, 2585, 195, 196, 202, 208, 1422, 1672, 1981, 1989, 5276, 195, 196, 197, 202, 208, 228, 229, 243, 525, 1430, 1438, 1462, 1527, 1670, 1849, 192, 195, 208, 222, 226, 294, 1240, 1564, 1596, 1628, 1640, 1692, 1849, 1959, 2744], "dep_prios": [5, 20, 5, 5, 5, 5, 10, 5, 5, 5, 5, 10, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 20, 10, 10, 5, 10, 20, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 10, 20, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 10, 10, 10, 10, 20, 20, 20, 20, 20, 20, 20, 10, 20, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 10, 5, 20, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20], "dependencies": ["transformers.models.auto.modeling_auto", "torch.nn.modules.module", "torch.utils.data", "transformers.data.data_collator", "transformers.integrations.deepspeed", "transformers.integrations.tpu", "transformers.utils.logging", "transformers.utils.deprecation", "transformers.utils.import_utils", "transformers.utils.quantization_config", "transformers.utils.notebook", "importlib.metadata", "collections.abc", "transformers.integrations", "huggingface_hub.utils", "torch.distributed", "packaging.version", "torch.nn", "transformers.configuration_utils", "transformers.debug_utils", "transformers.feature_extraction_sequence_utils", "transformers.feature_extraction_utils", "transformers.hyperparameter_search", "transformers.image_processing_utils", "transformers.modelcard", "transformers.modeling_utils", "transformers.optimization", "transformers.processing_utils", "transformers.pytorch_utils", "transformers.tokenization_utils_base", "transformers.trainer_callback", "transformers.trainer_pt_utils", "transformers.trainer_utils", "transformers.training_args", "transformers.utils", "safetensors.torch", "torch.optim", "transformers.modeling_outputs", "contextlib", "copy", "functools", "glob", "importlib", "inspect", "json", "math", "os", "random", "re", "shutil", "sys", "tempfile", "time", "warnings", "pathlib", "typing", "huggingface_hub", "numpy", "torch", "packaging", "transformers", "safetensors", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "_warnings", "abc", "collections", "concurrent", "concurrent.futures", "concurrent.futures._base", "enum", "genericpath", "huggingface_hub._space_api", "huggingface_hub.hf_api", "huggingface_hub.utils.tqdm", "io", "json.decoder", "logging", "numpy._core", "numpy._core.multiarray", "numpy._globals", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "numpy._typing._ufunc", "posixpath", "torch._C", "torch._C._VariableFunctions", "torch._C._distributed_c10d", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.cuda", "torch.cuda.memory", "torch.distributed.algorithms", "torch.distributed.algorithms.join", "torch.jit", "torch.jit._freeze", "torch.jit._trace", "torch.nn.modules", "torch.nn.modules.sparse", "torch.nn.parallel", "torch.nn.parallel.data_parallel", "torch.nn.parallel.distributed", "torch.nn.parameter", "torch.nn.utils", "torch.optim.lr_scheduler", "torch.optim.optimizer", "torch.serialization", "torch.types", "torch.utils", "torch.utils._contextlib", "torch.utils.data.dataloader", "torch.utils.data.dataset", "torch.utils.data.sampler", "transformers.data", "transformers.image_processing_base", "transformers.integrations.integration_utils", "transformers.integrations.peft", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.peft_utils", "types"], "hash": "a3e532f3f91968a75945c8397b9c2691875a0eac", "id": "transformers.trainer", "ignore_all": true, "interface_hash": "6265ecaf8fa60b17ef89f53c6ebe741728dd5405", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/trainer.py", "plugin_data": null, "size": 262432, "suppressed": ["torch_xla.distributed.fsdp.wrap", "torch.distributed._tensor.experimental", "torch_xla.core.xla_model", "torch_xla.debug.metrics", "torch_xla.distributed.spmd", "smdistributed.modelparallel.torch", "torch_xla.amp.syncfree", "torchao.prototype.low_bit_optim", "torch_xla.distributed.fsdp", "torch_xla.experimental.spmd_fully_sharded_data_parallel", "peft.utils.other", "torch_xla.core", "torch_xla.debug", "torch_xla.runtime", "torch_xla.distributed", "smdistributed.modelparallel", "accelerate.state", "accelerate.utils", "accelerate.data_loader", "liger_kernel.transformers", "torch_npu.optim", "apex.optimizers", "bitsandbytes.optim", "torchdistx.optimizers", "torchao.optim", "ray.train", "datasets", "torch_xla", "smdistributed", "peft", "accelerate", "optuna", "bitsandbytes", "galore_torch", "apollo_torch", "lomo_optim", "grokadamw", "schedulefree", "ray", "apex", "wandb"], "version_id": "1.16.1"}