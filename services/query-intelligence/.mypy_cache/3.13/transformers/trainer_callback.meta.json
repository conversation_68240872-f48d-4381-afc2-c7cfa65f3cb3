{"data_mtime": 1752049734, "dep_lines": [29, 27, 28, 29, 180, 18, 19, 20, 22, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 25], "dep_prios": [10, 5, 5, 20, 20, 5, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["transformers.utils.logging", "transformers.trainer_utils", "transformers.training_args", "transformers.utils", "transformers.integrations", "dataclasses", "json", "math", "typing", "numpy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "logging", "numpy._typing", "numpy._typing._ufunc", "transformers.integrations.integration_utils", "transformers.utils.generic", "types"], "hash": "1ba68097a27317a14b719d8175ad155db2cdad13", "id": "transformers.trainer_callback", "ignore_all": true, "interface_hash": "4757b66a88c9ebeeffba96b117de148a5a9c17fa", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/trainer_callback.py", "plugin_data": null, "size": 33611, "suppressed": ["tqdm.auto"], "version_id": "1.16.1"}