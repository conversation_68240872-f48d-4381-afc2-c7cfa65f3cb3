{".class": "MypyFile", "_fullname": "transformers.image_processing_utils_fast", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseImageProcessor": {".class": "SymbolTableNode", "cross_ref": "transformers.image_processing_utils.BaseImageProcessor", "kind": "Gdef"}, "BaseImageProcessorFast": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.image_processing_utils.BaseImageProcessor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast", "name": "BaseImageProcessorFast", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.image_processing_utils_fast", "mro": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", "transformers.image_processing_utils.BaseImageProcessor", "transformers.image_processing_base.ImageProcessingMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "images", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "images", "args", "kwargs"], "arg_types": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.image_utils.ImageInput"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnboundType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.image_processing_utils_fast.DefaultFastImageProcessorKwargs"}], "expr": null, "expr_fallback": null, "name": "Unpack"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of BaseImageProcessorFast", "ret_type": "transformers.image_processing_base.BatchFeature", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", {".class": "UnboundType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.image_processing_utils_fast.DefaultFastImageProcessorKwargs"}], "expr": null, "expr_fallback": null, "name": "Unpack"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BaseImageProcessorFast", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_further_process_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "size", "crop_size", "default_to_square", "image_mean", "image_std", "data_format", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast._further_process_kwargs", "name": "_further_process_kwargs", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "size", "crop_size", "default_to_square", "image_mean", "image_std", "data_format", "kwargs"], "arg_types": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", {".class": "UnionType", "items": ["transformers.image_utils.SizeDict", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.image_utils.SizeDict", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_further_process_kwargs of BaseImageProcessorFast", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fuse_mean_std_and_rescale_factor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "do_normalize", "image_mean", "image_std", "do_rescale", "rescale_factor", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast._fuse_mean_std_and_rescale_factor", "name": "_fuse_mean_std_and_rescale_factor", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "do_normalize", "image_mean", "image_std", "do_rescale", "rescale_factor", "device"], "arg_types": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_fuse_mean_std_and_rescale_factor of BaseImageProcessorFast", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast._fuse_mean_std_and_rescale_factor", "name": "_fuse_mean_std_and_rescale_factor", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_prepare_images_structure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "images"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast._prepare_images_structure", "name": "_prepare_images_structure", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "images"], "arg_types": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.image_utils.ImageInput"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_prepare_images_structure of BaseImageProcessorFast", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "transformers.image_utils.ImageInput"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_input_images": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "images", "do_convert_rgb", "input_data_format", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast._prepare_input_images", "name": "_prepare_input_images", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "images", "do_convert_rgb", "input_data_format", "device"], "arg_types": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.image_utils.ImageInput"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_prepare_input_images of BaseImageProcessorFast", "ret_type": {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_preprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "images", "do_resize", "size", "interpolation", "do_center_crop", "crop_size", "do_rescale", "rescale_factor", "do_normalize", "image_mean", "image_std", "disable_grouping", "return_tensors", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast._preprocess", "name": "_preprocess", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "images", "do_resize", "size", "interpolation", "do_center_crop", "crop_size", "do_rescale", "rescale_factor", "do_normalize", "image_mean", "image_std", "disable_grouping", "return_tensors", "kwargs"], "arg_types": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "transformers.image_utils.SizeDict", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.image_processing_utils_fast.F", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "transformers.image_utils.SizeDict", "builtins.bool", "builtins.float", "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_preprocess of BaseImageProcessorFast", "ret_type": "transformers.image_processing_base.BatchFeature", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_image": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "image", "do_convert_rgb", "input_data_format", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast._process_image", "name": "_process_image", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "image", "do_convert_rgb", "input_data_format", "device"], "arg_types": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.image_utils.ImageInput"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_process_image of BaseImageProcessorFast", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_valid_kwargs_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast._valid_kwargs_names", "name": "_valid_kwargs_names", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_validate_preprocess_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "do_rescale", "rescale_factor", "do_normalize", "image_mean", "image_std", "do_resize", "size", "do_center_crop", "crop_size", "resample", "return_tensors", "data_format", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast._validate_preprocess_kwargs", "name": "_validate_preprocess_kwargs", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "do_rescale", "rescale_factor", "do_normalize", "image_mean", "image_std", "do_resize", "size", "do_center_crop", "crop_size", "resample", "return_tensors", "data_format", "kwargs"], "arg_types": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "TupleType", "implicit": false, "items": ["builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.image_utils.SizeDict", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.image_utils.SizeDict", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PIL.Image.Resampling", {".class": "AnyType", "missing_import_name": "transformers.image_processing_utils_fast.F", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_preprocess_kwargs of BaseImageProcessorFast", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "center_crop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "image", "size", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.center_crop", "name": "center_crop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "image", "size", "kwargs"], "arg_types": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", "torch._tensor.Tensor", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "center_crop of BaseImageProcessorFast", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compile_friendly_resize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["image", "new_size", "interpolation", "antialias"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.compile_friendly_resize", "name": "compile_friendly_resize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["image", "new_size", "interpolation", "antialias"], "arg_types": ["torch._tensor.Tensor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.image_processing_utils_fast.F", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compile_friendly_resize of BaseImageProcessorFast", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.compile_friendly_resize", "name": "compile_friendly_resize", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["image", "new_size", "interpolation", "antialias"], "arg_types": ["torch._tensor.Tensor", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.image_processing_utils_fast.F", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compile_friendly_resize of BaseImageProcessorFast", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "convert_to_rgb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "image"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.convert_to_rgb", "name": "convert_to_rgb", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "image"], "arg_types": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.image_utils.ImageInput"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_to_rgb of BaseImageProcessorFast", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "transformers.image_utils.ImageInput"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "crop_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.crop_size", "name": "crop_size", "setter_type": null, "type": {".class": "NoneType"}}}, "data_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.data_format", "name": "data_format", "setter_type": null, "type": "transformers.image_utils.ChannelDimension"}}, "default_to_square": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.default_to_square", "name": "default_to_square", "setter_type": null, "type": "builtins.bool"}}, "device": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.device", "name": "device", "setter_type": null, "type": {".class": "NoneType"}}}, "do_center_crop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.do_center_crop", "name": "do_center_crop", "setter_type": null, "type": {".class": "NoneType"}}}, "do_convert_rgb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.do_convert_rgb", "name": "do_convert_rgb", "setter_type": null, "type": {".class": "NoneType"}}}, "do_normalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.do_normalize", "name": "do_normalize", "setter_type": null, "type": {".class": "NoneType"}}}, "do_rescale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.do_rescale", "name": "do_rescale", "setter_type": null, "type": {".class": "NoneType"}}}, "do_resize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.do_resize", "name": "do_resize", "setter_type": null, "type": {".class": "NoneType"}}}, "filter_out_unused_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.filter_out_unused_kwargs", "name": "filter_out_unused_kwargs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "arg_types": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "filter_out_unused_kwargs of BaseImageProcessorFast", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "image_mean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.image_mean", "name": "image_mean", "setter_type": null, "type": {".class": "NoneType"}}}, "image_std": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.image_std", "name": "image_std", "setter_type": null, "type": {".class": "NoneType"}}}, "input_data_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.input_data_format", "name": "input_data_format", "setter_type": null, "type": {".class": "NoneType"}}}, "model_input_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.model_input_names", "name": "model_input_names", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "normalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "image", "mean", "std", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.normalize", "name": "normalize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "image", "mean", "std", "kwargs"], "arg_types": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", "torch._tensor.Tensor", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "normalize of BaseImageProcessorFast", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preprocess": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "images", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.preprocess", "name": "preprocess", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "images", "args", "kwargs"], "arg_types": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", {".class": "TypeAliasType", "args": [], "type_ref": "transformers.image_utils.ImageInput"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnboundType", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "transformers.image_processing_utils_fast.DefaultFastImageProcessorKwargs"}], "expr": null, "expr_fallback": null, "name": "Unpack"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "preprocess of BaseImageProcessorFast", "ret_type": "transformers.image_processing_base.BatchFeature", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.preprocess", "name": "preprocess", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "resample": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.resample", "name": "resample", "setter_type": null, "type": {".class": "NoneType"}}}, "rescale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "image", "scale", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.rescale", "name": "rescale", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "image", "scale", "kwargs"], "arg_types": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", "torch._tensor.Tensor", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "rescale of BaseImageProcessorFast", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rescale_and_normalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "images", "do_rescale", "rescale_factor", "do_normalize", "image_mean", "image_std"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.rescale_and_normalize", "name": "rescale_and_normalize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "images", "do_rescale", "rescale_factor", "do_normalize", "image_mean", "image_std"], "arg_types": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", "torch._tensor.Tensor", "builtins.bool", "builtins.float", "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "rescale_and_normalize of BaseImageProcessorFast", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rescale_factor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.rescale_factor", "name": "rescale_factor", "setter_type": null, "type": "builtins.float"}}, "resize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "image", "size", "interpolation", "antialias", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.resize", "name": "resize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "image", "size", "interpolation", "antialias", "kwargs"], "arg_types": ["transformers.image_processing_utils_fast.BaseImageProcessorFast", "torch._tensor.Tensor", "transformers.image_utils.SizeDict", {".class": "AnyType", "missing_import_name": "transformers.image_processing_utils_fast.F", "source_any": null, "type_of_any": 3}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "resize of BaseImageProcessorFast", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "return_tensors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.return_tensors", "name": "return_tensors", "setter_type": null, "type": {".class": "NoneType"}}}, "size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.size", "name": "size", "setter_type": null, "type": {".class": "NoneType"}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.to_dict", "name": "to_dict", "type": null}}, "unused_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.unused_kwargs", "name": "unused_kwargs", "setter_type": null, "type": {".class": "NoneType"}}}, "valid_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.valid_kwargs", "name": "valid_kwargs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["do_resize", "size", "default_to_square", "resample", "do_center_crop", "crop_size", "do_rescale", "rescale_factor", "do_normalize", "image_mean", "image_std", "do_convert_rgb", "return_tensors", "data_format", "input_data_format", "device", "disable_grouping"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PIL.Image.Resampling", {".class": "AnyType", "missing_import_name": "transformers.image_processing_utils_fast.F", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypedDictType", "fallback": "transformers.image_processing_utils_fast.DefaultFastImageProcessorKwargs", "items": [["do_resize", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["default_to_square", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["resample", {".class": "UnionType", "items": ["PIL.Image.Resampling", {".class": "AnyType", "missing_import_name": "transformers.image_processing_utils_fast.F", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_center_crop", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["crop_size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_rescale", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["rescale_factor", {".class": "UnionType", "items": ["builtins.int", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_normalize", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["image_mean", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["image_std", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_convert_rgb", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["return_tensors", {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["data_format", {".class": "UnionType", "items": ["transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["input_data_format", {".class": "UnionType", "items": ["builtins.str", "transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["device", {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["disable_grouping", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.image_processing_utils_fast.BaseImageProcessorFast.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.image_processing_utils_fast.BaseImageProcessorFast", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BatchFeature": {".class": "SymbolTableNode", "cross_ref": "transformers.image_processing_base.BatchFeature", "kind": "Gdef"}, "ChannelDimension": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.ChannelDimension", "kind": "Gdef"}, "DefaultFastImageProcessorKwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.image_processing_utils_fast.DefaultFastImageProcessorKwargs", "name": "DefaultFastImageProcessorKwargs", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.image_processing_utils_fast.DefaultFastImageProcessorKwargs", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.image_processing_utils_fast", "mro": ["transformers.image_processing_utils_fast.DefaultFastImageProcessorKwargs", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["do_resize", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["default_to_square", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["resample", {".class": "UnionType", "items": ["PIL.Image.Resampling", {".class": "AnyType", "missing_import_name": "transformers.image_processing_utils_fast.F", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_center_crop", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["crop_size", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_rescale", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["rescale_factor", {".class": "UnionType", "items": ["builtins.int", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_normalize", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["image_mean", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["image_std", {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["do_convert_rgb", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["return_tensors", {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["data_format", {".class": "UnionType", "items": ["transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["input_data_format", {".class": "UnionType", "items": ["builtins.str", "transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["device", {".class": "UnionType", "items": ["torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}], ["disable_grouping", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}}}, "F": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.image_processing_utils_fast.F", "name": "F", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.image_processing_utils_fast.F", "source_any": null, "type_of_any": 3}}}, "ImageInput": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.ImageInput", "kind": "Gdef"}, "ImageType": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.ImageType", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PILImageResampling": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.PILImageResampling", "kind": "Gdef"}, "SizeDict": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.SizeDict", "kind": "Gdef"}, "TensorType": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.generic.TensorType", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "transformers.processing_utils.Unpack", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.image_processing_utils_fast.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.image_processing_utils_fast.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.image_processing_utils_fast.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.image_processing_utils_fast.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.image_processing_utils_fast.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.image_processing_utils_fast.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "auto_docstring": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.args_doc.auto_docstring", "kind": "Gdef"}, "convert_to_rgb": {".class": "SymbolTableNode", "cross_ref": "transformers.image_transforms.convert_to_rgb", "kind": "Gdef"}, "divide_to_patches": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["image", "patch_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.image_processing_utils_fast.divide_to_patches", "name": "divide_to_patches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["image", "patch_size"], "arg_types": [{".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "np.array"}, "torch._tensor.Tensor"], "uses_pep604_syntax": false}, "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "divide_to_patches", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "np.array"}, "torch._tensor.Tensor"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_image_size": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.get_image_size", "kind": "Gdef"}, "get_image_size_for_max_height_width": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.get_image_size_for_max_height_width", "kind": "Gdef"}, "get_image_type": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.get_image_type", "kind": "Gdef"}, "get_max_height_width": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["images"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.image_processing_utils_fast.get_max_height_width", "name": "get_max_height_width", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["images"], "arg_types": [{".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_max_height_width", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_resize_output_image_size": {".class": "SymbolTableNode", "cross_ref": "transformers.image_transforms.get_resize_output_image_size", "kind": "Gdef"}, "get_size_dict": {".class": "SymbolTableNode", "cross_ref": "transformers.image_processing_utils.get_size_dict", "kind": "Gdef"}, "get_size_with_aspect_ratio": {".class": "SymbolTableNode", "cross_ref": "transformers.image_transforms.get_size_with_aspect_ratio", "kind": "Gdef"}, "group_images_by_shape": {".class": "SymbolTableNode", "cross_ref": "transformers.image_transforms.group_images_by_shape", "kind": "Gdef"}, "infer_channel_dimension_format": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.infer_channel_dimension_format", "kind": "Gdef"}, "is_rocm_platform": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_rocm_platform", "kind": "Gdef"}, "is_torch_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torch_available", "kind": "Gdef"}, "is_torchvision_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torchvision_available", "kind": "Gdef"}, "is_torchvision_v2_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_torchvision_v2_available", "kind": "Gdef"}, "is_vision_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_vision_available", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_utils_fast.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "lru_cache": {".class": "SymbolTableNode", "cross_ref": "functools.lru_cache", "kind": "Gdef"}, "make_flat_list_of_images": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.make_flat_list_of_images", "kind": "Gdef"}, "max_across_indices": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.image_processing_utils_fast.max_across_indices", "name": "max_across_indices", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["values"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "max_across_indices", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "pil_torch_interpolation_mapping": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.pil_torch_interpolation_mapping", "kind": "Gdef"}, "reorder_images": {".class": "SymbolTableNode", "cross_ref": "transformers.image_transforms.reorder_images", "kind": "Gdef"}, "safe_squeeze": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["tensor", "axis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.image_processing_utils_fast.safe_squeeze", "name": "safe_squeeze", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["tensor", "axis"], "arg_types": ["torch._tensor.Tensor", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "safe_squeeze", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "validate_fast_preprocess_arguments": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["do_rescale", "rescale_factor", "do_normalize", "image_mean", "image_std", "do_pad", "size_divisibility", "do_center_crop", "crop_size", "do_resize", "size", "resample", "return_tensors", "data_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "transformers.image_processing_utils_fast.validate_fast_preprocess_arguments", "name": "validate_fast_preprocess_arguments", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["do_rescale", "rescale_factor", "do_normalize", "image_mean", "image_std", "do_pad", "size_divisibility", "do_center_crop", "crop_size", "do_resize", "size", "resample", "return_tensors", "data_format"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.image_utils.SizeDict", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.image_utils.SizeDict", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["PIL.Image.Resampling", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "transformers.utils.generic.TensorType", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["transformers.image_utils.ChannelDimension", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate_fast_preprocess_arguments", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "transformers.image_processing_utils_fast.validate_fast_preprocess_arguments", "name": "validate_fast_preprocess_arguments", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "validate_kwargs": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.validate_kwargs", "kind": "Gdef"}, "validate_preprocess_arguments": {".class": "SymbolTableNode", "cross_ref": "transformers.image_utils.validate_preprocess_arguments", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/image_processing_utils_fast.py"}