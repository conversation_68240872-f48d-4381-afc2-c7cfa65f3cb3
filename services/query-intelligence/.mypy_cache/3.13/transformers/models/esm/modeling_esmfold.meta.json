{"data_mtime": 1752049734, "dep_lines": [36, 35, 36, 27, 29, 17, 24, 28, 29, 15, 16, 18, 19, 20, 22, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 10, 5, 5, 5, 5, 10, 10, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.esm.openfold_utils.residue_constants", "transformers.models.esm.modeling_esm", "transformers.models.esm.openfold_utils", "transformers.integrations.deepspeed", "transformers.utils.logging", "collections.abc", "torch.nn", "transformers.modeling_outputs", "transformers.utils", "math", "sys", "dataclasses", "functools", "typing", "numpy", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "collections", "logging", "torch._C", "torch._C._VariableFunctions", "torch._jit_internal", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.jit", "torch.nn.init", "torch.nn.modules", "torch.nn.modules.activation", "torch.nn.modules.container", "torch.nn.modules.dropout", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.modules.normalization", "torch.nn.modules.sparse", "torch.nn.parameter", "torch.utils", "torch.utils._contextlib", "transformers.integrations", "transformers.integrations.peft", "transformers.modeling_utils", "transformers.models.esm.openfold_utils.rigid_utils", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "4b4626d370e3d4ebdd07a750a0d7ef98149bcc22", "id": "transformers.models.esm.modeling_esmfold", "ignore_all": true, "interface_hash": "9591d3373659d6987470428e5fa0bc640956cf5c", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/models/esm/modeling_esmfold.py", "plugin_data": null, "size": 86052, "suppressed": [], "version_id": "1.16.1"}