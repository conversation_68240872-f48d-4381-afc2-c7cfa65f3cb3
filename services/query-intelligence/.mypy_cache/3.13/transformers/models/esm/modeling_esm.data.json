{".class": "MypyFile", "_fullname": "transformers.models.esm.modeling_esm", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BCEWithLogitsLoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.BCEWithLogitsLoss", "kind": "Gdef", "module_public": false}, "BaseModelOutputWithPastAndCrossAttentions": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.BaseModelOutputWithPastAndCrossAttentions", "kind": "Gdef", "module_public": false}, "BaseModelOutputWithPoolingAndCrossAttentions": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.BaseModelOutputWithPoolingAndCrossAttentions", "kind": "Gdef", "module_public": false}, "CrossEntropyLoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.CrossEntropyLoss", "kind": "Gdef", "module_public": false}, "ESM_ATTENTION_CLASSES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.esm.modeling_esm.ESM_ATTENTION_CLASSES", "name": "ESM_ATTENTION_CLASSES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["config", "position_embedding_type"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.esm.modeling_esm.EsmSelfAttention", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "EsmAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmAttention", "name": "EsmAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmAttention", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "LayerNorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmAttention.LayerNorm", "name": "LayerNorm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmAttention.__init__", "name": "__init__", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "head_mask", "encoder_hidden_states", "encoder_attention_mask", "past_key_value", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmAttention.forward", "name": "forward", "type": null}}, "output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmAttention.output", "name": "output", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "prune_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "heads"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmAttention.prune_heads", "name": "prune_heads", "type": null}}, "pruned_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmAttention.pruned_heads", "name": "pruned_heads", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "self": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmAttention.self", "name": "self", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EsmClassificationHead": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmClassificationHead", "name": "EsmClassificationHead", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmClassificationHead", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmClassificationHead", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmClassificationHead.__init__", "name": "__init__", "type": null}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmClassificationHead.dense", "name": "dense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmClassificationHead.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "features", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmClassificationHead.forward", "name": "forward", "type": null}}, "out_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmClassificationHead.out_proj", "name": "out_proj", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmClassificationHead.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmClassificationHead", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EsmConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.esm.configuration_esm.EsmConfig", "kind": "Gdef", "module_public": false}, "EsmContactPredictionHead": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmContactPredictionHead", "name": "EsmContactPredictionHead", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmContactPredictionHead", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmContactPredictionHead", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "in_features", "bias", "eos_idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.esm.modeling_esm.EsmContactPredictionHead.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "in_features", "bias", "eos_idx"], "arg_types": ["transformers.models.esm.modeling_esm.EsmContactPredictionHead", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of EsmContactPredictionHead", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmContactPredictionHead.activation", "name": "activation", "setter_type": null, "type": "torch.nn.modules.activation.Sigmoid"}}, "eos_idx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmContactPredictionHead.eos_idx", "name": "eos_idx", "setter_type": null, "type": "builtins.int"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tokens", "attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmContactPredictionHead.forward", "name": "forward", "type": null}}, "in_features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmContactPredictionHead.in_features", "name": "in_features", "setter_type": null, "type": "builtins.int"}}, "regression": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmContactPredictionHead.regression", "name": "regression", "setter_type": null, "type": "torch.nn.modules.linear.Linear"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmContactPredictionHead.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmContactPredictionHead", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EsmEmbeddings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmEmbeddings", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmEmbeddings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmEmbeddings", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmEmbeddings.__init__", "name": "__init__", "type": null}}, "create_position_ids_from_inputs_embeds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inputs_embeds"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmEmbeddings.create_position_ids_from_inputs_embeds", "name": "create_position_ids_from_inputs_embeds", "type": null}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmEmbeddings.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "inputs_embeds", "past_key_values_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmEmbeddings.forward", "name": "forward", "type": null}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmEmbeddings.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "mask_token_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmEmbeddings.mask_token_id", "name": "mask_token_id", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "padding_idx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmEmbeddings.padding_idx", "name": "padding_idx", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "position_embedding_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmEmbeddings.position_embedding_type", "name": "position_embedding_type", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "position_embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmEmbeddings.position_embeddings", "name": "position_embeddings", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "token_dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmEmbeddings.token_dropout", "name": "token_dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "word_embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmEmbeddings.word_embeddings", "name": "word_embeddings", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmEmbeddings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EsmEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmEncoder", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmEncoder", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmEncoder.__init__", "name": "__init__", "type": null}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmEncoder.config", "name": "config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "emb_layer_norm_after": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmEncoder.emb_layer_norm_after", "name": "emb_layer_norm_after", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "head_mask", "encoder_hidden_states", "encoder_attention_mask", "past_key_values", "use_cache", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmEncoder.forward", "name": "forward", "type": null}}, "gradient_checkpointing": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmEncoder.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmEncoder.layer", "name": "layer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EsmFlashAttention2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.esm.modeling_esm.EsmSelfAttention"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmFlashAttention2", "name": "EsmFlashAttention2", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmFlashAttention2", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmFlashAttention2", "transformers.models.esm.modeling_esm.EsmSelfAttention", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "position_embedding_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmFlashAttention2.__init__", "name": "__init__", "type": null}}, "_flash_attn_uses_top_left_mask": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmFlashAttention2._flash_attn_uses_top_left_mask", "name": "_flash_attn_uses_top_left_mask", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout_prob": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmFlashAttention2.dropout_prob", "name": "dropout_prob", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "head_mask", "encoder_hidden_states", "encoder_attention_mask", "past_key_value", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.esm.modeling_esm.EsmFlashAttention2.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "head_mask", "encoder_hidden_states", "encoder_attention_mask", "past_key_value", "output_attentions"], "arg_types": ["transformers.models.esm.modeling_esm.EsmFlashAttention2", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of EsmFlashAttention2", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmFlashAttention2.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmFlashAttention2", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EsmForMaskedLM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.esm.modeling_esm.EsmPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmForMaskedLM", "name": "EsmForMaskedLM", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmForMaskedLM", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmForMaskedLM", "transformers.models.esm.modeling_esm.EsmPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmForMaskedLM.__init__", "name": "__init__", "type": null}}, "_tied_weights_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.esm.modeling_esm.EsmForMaskedLM._tied_weights_keys", "name": "_tied_weights_keys", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "esm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmForMaskedLM.esm", "name": "esm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "head_mask", "inputs_embeds", "encoder_hidden_states", "encoder_attention_mask", "labels", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.esm.modeling_esm.EsmForMaskedLM.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "head_mask", "inputs_embeds", "encoder_hidden_states", "encoder_attention_mask", "labels", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.esm.modeling_esm.EsmForMaskedLM", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of EsmForMaskedLM", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.MaskedLMOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmForMaskedLM.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_output_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmForMaskedLM.get_output_embeddings", "name": "get_output_embeddings", "type": null}}, "lm_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmForMaskedLM.lm_head", "name": "lm_head", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "predict_contacts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tokens", "attention_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmForMaskedLM.predict_contacts", "name": "predict_contacts", "type": null}}, "set_output_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_embeddings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmForMaskedLM.set_output_embeddings", "name": "set_output_embeddings", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmForMaskedLM.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmForMaskedLM", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EsmForSequenceClassification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.esm.modeling_esm.EsmPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmForSequenceClassification", "name": "EsmForSequenceClassification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmForSequenceClassification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmForSequenceClassification", "transformers.models.esm.modeling_esm.EsmPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmForSequenceClassification.__init__", "name": "__init__", "type": null}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmForSequenceClassification.classifier", "name": "classifier", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "esm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmForSequenceClassification.esm", "name": "esm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "head_mask", "inputs_embeds", "labels", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.esm.modeling_esm.EsmForSequenceClassification.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "head_mask", "inputs_embeds", "labels", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.esm.modeling_esm.EsmForSequenceClassification", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of EsmForSequenceClassification", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.SequenceClassifierOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmForSequenceClassification.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "num_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmForSequenceClassification.num_labels", "name": "num_labels", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmForSequenceClassification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmForSequenceClassification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EsmForTokenClassification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.esm.modeling_esm.EsmPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmForTokenClassification", "name": "EsmForTokenClassification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmForTokenClassification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmForTokenClassification", "transformers.models.esm.modeling_esm.EsmPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmForTokenClassification.__init__", "name": "__init__", "type": null}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmForTokenClassification.classifier", "name": "classifier", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmForTokenClassification.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "esm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmForTokenClassification.esm", "name": "esm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "head_mask", "inputs_embeds", "labels", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.esm.modeling_esm.EsmForTokenClassification.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "head_mask", "inputs_embeds", "labels", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.esm.modeling_esm.EsmForTokenClassification", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of EsmForTokenClassification", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "transformers.modeling_outputs.TokenClassifierOutput"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmForTokenClassification.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "num_labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmForTokenClassification.num_labels", "name": "num_labels", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmForTokenClassification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmForTokenClassification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EsmIntermediate": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmIntermediate", "name": "EsmIntermediate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmIntermediate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmIntermediate", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmIntermediate.__init__", "name": "__init__", "type": null}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmIntermediate.dense", "name": "dense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.esm.modeling_esm.EsmIntermediate.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.esm.modeling_esm.EsmIntermediate", "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of EsmIntermediate", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmIntermediate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmIntermediate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EsmLMHead": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmLMHead", "name": "EsmLMHead", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmLMHead", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmLMHead", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmLMHead.__init__", "name": "__init__", "type": null}}, "bias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmLMHead.bias", "name": "bias", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmLMHead.decoder", "name": "decoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmLMHead.dense", "name": "dense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "features", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmLMHead.forward", "name": "forward", "type": null}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmLMHead.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmLMHead.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmLMHead", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EsmLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_layers.GradientCheckpointingLayer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmLayer", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmLayer", "transformers.modeling_layers.GradientCheckpointingLayer", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "LayerNorm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmLayer.LayerNorm", "name": "LayerNorm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmLayer.__init__", "name": "__init__", "type": null}}, "add_cross_attention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmLayer.add_cross_attention", "name": "add_cross_attention", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "attention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmLayer.attention", "name": "attention", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "chunk_size_feed_forward": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmLayer.chunk_size_feed_forward", "name": "chunk_size_feed_forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "crossattention": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmLayer.crossattention", "name": "crossattention", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "feed_forward_chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attention_output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmLayer.feed_forward_chunk", "name": "feed_forward_chunk", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "head_mask", "encoder_hidden_states", "encoder_attention_mask", "past_key_value", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmLayer.forward", "name": "forward", "type": null}}, "intermediate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmLayer.intermediate", "name": "intermediate", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "is_decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmLayer.is_decoder", "name": "is_decoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmLayer.output", "name": "output", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "seq_len_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmLayer.seq_len_dim", "name": "seq_len_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EsmModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.esm.modeling_esm.EsmPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmModel", "name": "EsmModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmModel", "transformers.models.esm.modeling_esm.EsmPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "add_pooling_layer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmModel.__init__", "name": "__init__", "type": null}}, "_prune_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "heads_to_prune"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmModel._prune_heads", "name": "_prune_heads", "type": null}}, "contact_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmModel.contact_head", "name": "contact_head", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmModel.embeddings", "name": "embeddings", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmModel.encoder", "name": "encoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "head_mask", "inputs_embeds", "encoder_hidden_states", "encoder_attention_mask", "past_key_values", "use_cache", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.esm.modeling_esm.EsmModel.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "head_mask", "inputs_embeds", "encoder_hidden_states", "encoder_attention_mask", "past_key_values", "use_cache", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.esm.modeling_esm.EsmModel", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._<PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of EsmModel", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "transformers.modeling_outputs.BaseModelOutputWithPoolingAndCrossAttentions"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmModel.forward", "name": "forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "get_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmModel.get_input_embeddings", "name": "get_input_embeddings", "type": null}}, "pooler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmModel.pooler", "name": "pooler", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "predict_contacts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tokens", "attention_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmModel.predict_contacts", "name": "predict_contacts", "type": null}}, "set_input_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmModel.set_input_embeddings", "name": "set_input_embeddings", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EsmOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmOutput", "name": "EsmOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmOutput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmOutput", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmOutput.__init__", "name": "__init__", "type": null}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmOutput.dense", "name": "dense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmOutput.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "input_tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmOutput.forward", "name": "forward", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EsmPooler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmPooler", "name": "Esm<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmPooler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmPooler", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmPooler.__init__", "name": "__init__", "type": null}}, "activation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmPooler.activation", "name": "activation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmPooler.dense", "name": "dense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.esm.modeling_esm.EsmPooler.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_states"], "arg_types": ["transformers.models.esm.modeling_esm.EsmPooler", "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmPooler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmPooler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EsmPreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_utils.PreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmPreTrainedModel", "name": "EsmPreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmPreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmPreTrainedModel", "transformers.modeling_utils.PreTrainedModel", "torch.nn.modules.module.Module", "transformers.modeling_utils.ModuleUtilsMixin", "transformers.utils.hub.PushToHubMixin", "transformers.integrations.peft.PeftAdapterMixin", "builtins.object"], "names": {".class": "SymbolTable", "_init_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmPreTrainedModel._init_weights", "name": "_init_weights", "type": null}}, "_no_split_modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.esm.modeling_esm.EsmPreTrainedModel._no_split_modules", "name": "_no_split_modules", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_supports_flash_attn_2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.esm.modeling_esm.EsmPreTrainedModel._supports_flash_attn_2", "name": "_supports_flash_attn_2", "setter_type": null, "type": "builtins.bool"}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.esm.modeling_esm.EsmPreTrainedModel.base_model_prefix", "name": "base_model_prefix", "setter_type": null, "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.esm.modeling_esm.EsmPreTrainedModel.config_class", "name": "config_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["vocab_size", "mask_token_id", "pad_token_id", "hidden_size", "num_hidden_layers", "num_attention_heads", "intermediate_size", "hidden_dropout_prob", "attention_probs_dropout_prob", "max_position_embeddings", "initializer_range", "layer_norm_eps", "position_embedding_type", "use_cache", "emb_layer_norm_before", "token_dropout", "is_folding_model", "esmfold_config", "vocab_list", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.esm.configuration_esm.EsmConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.esm.modeling_esm.EsmPreTrainedModel.supports_gradient_checkpointing", "name": "supports_gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmPreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmPreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EsmSelfAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention", "name": "EsmSelfAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmSelfAttention", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config", "position_embedding_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention.__init__", "name": "__init__", "type": null}}, "all_head_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention.all_head_size", "name": "all_head_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "attention_head_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention.attention_head_size", "name": "attention_head_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention.config", "name": "config", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "distance_embedding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention.distance_embedding", "name": "distance_embedding", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "head_mask", "encoder_hidden_states", "encoder_attention_mask", "past_key_value", "output_attentions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "head_mask", "encoder_hidden_states", "encoder_attention_mask", "past_key_value", "output_attentions"], "arg_types": ["transformers.models.esm.modeling_esm.EsmSelfAttention", "torch._tensor.Tensor", {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch._<PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": ["torch._<PERSON><PERSON>"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of EsmSelf<PERSON><PERSON><PERSON>", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention.is_decoder", "name": "is_decoder", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention.key", "name": "key", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "max_position_embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention.max_position_embeddings", "name": "max_position_embeddings", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_attention_heads": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention.num_attention_heads", "name": "num_attention_heads", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "position_embedding_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention.position_embedding_type", "name": "position_embedding_type", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "query": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention.query", "name": "query", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "rotary_embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention.rotary_embeddings", "name": "rotary_embeddings", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "transpose_for_scores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention.transpose_for_scores", "name": "transpose_for_scores", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "x"], "arg_types": ["transformers.models.esm.modeling_esm.EsmSelfAttention", "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transpose_for_scores of EsmSelfAttention", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention.value", "name": "value", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmSelfAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmSelfAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EsmSelfOutput": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.EsmSelfOutput", "name": "EsmSelfOutput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmSelfOutput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.EsmSelfOutput", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmSelfOutput.__init__", "name": "__init__", "type": null}}, "dense": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmSelfOutput.dense", "name": "dense", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dropout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.EsmSelfOutput.dropout", "name": "dropout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hidden_states", "input_tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.EsmSelfOutput.forward", "name": "forward", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.EsmSelfOutput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.EsmSelfOutput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GradientCheckpointingLayer": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_layers.GradientCheckpointingLayer", "kind": "Gdef", "module_public": false}, "MSELoss": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.loss.MSELoss", "kind": "Gdef", "module_public": false}, "MaskedLMOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.MaskedLMOutput", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_utils.PreTrainedModel", "kind": "Gdef", "module_public": false}, "RotaryEmbedding": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.esm.modeling_esm.RotaryEmbedding", "name": "RotaryEmbedding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.RotaryEmbedding", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.esm.modeling_esm", "mro": ["transformers.models.esm.modeling_esm.RotaryEmbedding", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.esm.modeling_esm.RotaryEmbedding.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dim"], "arg_types": ["transformers.models.esm.modeling_esm.RotaryEmbedding", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RotaryEmbedding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cos_cached": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.RotaryEmbedding._cos_cached", "name": "_cos_cached", "setter_type": null, "type": {".class": "NoneType"}}}, "_seq_len_cached": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.RotaryEmbedding._seq_len_cached", "name": "_seq_len_cached", "setter_type": null, "type": {".class": "NoneType"}}}, "_sin_cached": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.esm.modeling_esm.RotaryEmbedding._sin_cached", "name": "_sin_cached", "setter_type": null, "type": {".class": "NoneType"}}}, "_update_cos_sin_tables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "x", "seq_dimension"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.RotaryEmbedding._update_cos_sin_tables", "name": "_update_cos_sin_tables", "type": null}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "q", "k"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.esm.modeling_esm.RotaryEmbedding.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "q", "k"], "arg_types": ["transformers.models.esm.modeling_esm.RotaryEmbedding", "torch._tensor.Tensor", "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of Rota<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.esm.modeling_esm.RotaryEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.esm.modeling_esm.RotaryEmbedding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SequenceClassifierOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.SequenceClassifierOutput", "kind": "Gdef", "module_public": false}, "TokenClassifierOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_outputs.TokenClassifierOutput", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.esm.modeling_esm.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.esm.modeling_esm.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.esm.modeling_esm.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.esm.modeling_esm.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.esm.modeling_esm.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.esm.modeling_esm.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.esm.modeling_esm.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_flash_attention_forward": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flash_attention_utils._flash_attention_forward", "kind": "Gdef", "module_public": false}, "apply_rotary_pos_emb": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "cos", "sin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.apply_rotary_pos_emb", "name": "apply_rotary_pos_emb", "type": null}}, "auto_docstring": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.args_doc.auto_docstring", "kind": "Gdef", "module_public": false}, "average_product_correct": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.average_product_correct", "name": "average_product_correct", "type": null}}, "create_position_ids_from_input_ids": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["input_ids", "padding_idx", "past_key_values_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.create_position_ids_from_input_ids", "name": "create_position_ids_from_input_ids", "type": null}}, "find_pruneable_heads_and_indices": {".class": "SymbolTableNode", "cross_ref": "transformers.pytorch_utils.find_pruneable_heads_and_indices", "kind": "Gdef", "module_public": false}, "flash_attn_supports_top_left_mask": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flash_attention_utils.flash_attn_supports_top_left_mask", "kind": "Gdef", "module_public": false}, "gelu": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.gelu", "name": "gelu", "type": null}}, "is_flash_attn_available": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flash_attention_utils.is_flash_attn_available", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.esm.modeling_esm.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef", "module_public": false}, "prune_linear_layer": {".class": "SymbolTableNode", "cross_ref": "transformers.pytorch_utils.prune_linear_layer", "kind": "Gdef", "module_public": false}, "rotate_half": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.rotate_half", "name": "rotate_half", "type": null}}, "symmetrize": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.esm.modeling_esm.symmetrize", "name": "symmetrize", "type": null}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/models/esm/modeling_esm.py"}