{"data_mtime": 1752049734, "dep_lines": [44, 24, 39, 24, 25, 28, 29, 30, 31, 37, 38, 39, 18, 19, 20, 21, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 20, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.clvp.configuration_clvp", "torch.utils.checkpoint", "transformers.utils.logging", "torch.utils", "torch.nn", "transformers.activations", "transformers.generation", "transformers.modeling_attn_mask_utils", "transformers.modeling_outputs", "transformers.modeling_utils", "transformers.pytorch_utils", "transformers.utils", "copy", "math", "dataclasses", "typing", "torch", "builtins", "_frozen_importlib", "_typeshed", "abc", "collections", "logging", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.functional", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.conv", "torch.nn.modules.dropout", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.modules.normalization", "torch.nn.modules.sparse", "torch.nn.parameter", "torch.utils._contextlib", "transformers.configuration_utils", "transformers.generation.configuration_utils", "transformers.generation.continuous_batching", "transformers.generation.utils", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "04436dcd7f7970e56b8a9038bd155dbac994eb59", "id": "transformers.models.clvp.modeling_clvp", "ignore_all": true, "interface_hash": "12a17a3e68c0a22dc1572f1e38ddda4e9d66a6e6", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/models/clvp/modeling_clvp.py", "plugin_data": null, "size": 88617, "suppressed": [], "version_id": "1.16.1"}