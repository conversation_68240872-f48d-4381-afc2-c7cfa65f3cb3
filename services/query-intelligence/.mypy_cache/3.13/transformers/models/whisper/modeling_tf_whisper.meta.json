{"data_mtime": 1752049734, "dep_lines": [45, 46, 27, 28, 44, 26, 29, 35, 43, 44, 17, 19, 20, 21, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 24], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.models.whisper.configuration_whisper", "transformers.models.whisper.tokenization_whisper", "transformers.generation.configuration_utils", "transformers.generation.tf_logits_process", "transformers.utils.logging", "transformers.activations_tf", "transformers.modeling_tf_outputs", "transformers.modeling_tf_utils", "transformers.tf_utils", "transformers.utils", "__future__", "math", "random", "typing", "numpy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "collections", "logging", "transformers.configuration_utils", "transformers.generation", "transformers.generation.tf_utils", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "773b952788090652b1d4cf1dc0067619a78967d3", "id": "transformers.models.whisper.modeling_tf_whisper", "ignore_all": true, "interface_hash": "283e2825e0dd7b94101998cd2ce8867b6c0f7be1", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/models/whisper/modeling_tf_whisper.py", "plugin_data": null, "size": 84866, "suppressed": ["tensorflow"], "version_id": "1.16.1"}