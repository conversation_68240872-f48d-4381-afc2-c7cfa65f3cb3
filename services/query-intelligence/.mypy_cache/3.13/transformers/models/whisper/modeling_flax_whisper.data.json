{".class": "MypyFile", "_fullname": "transformers.models.whisper.modeling_flax_whisper", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACT2FN": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.ACT2FN", "kind": "Gdef", "module_public": false}, "FLAX_WHISPER_AUDIO_CLASSIFICATION_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FLAX_WHISPER_AUDIO_CLASSIFICATION_DOCSTRING", "name": "FLAX_WHISPER_AUDIO_CLASSIFICATION_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "FLAX_WHISPER_CONDITIONAL_GENERATION_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FLAX_WHISPER_CONDITIONAL_GENERATION_DOCSTRING", "name": "FLAX_WHISPER_CONDITIONAL_GENERATION_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "FlaxBaseModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_outputs.FlaxBaseModelOutput", "kind": "Gdef", "module_public": false}, "FlaxBaseModelOutputWithPastAndCrossAttentions": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_outputs.FlaxBaseModelOutputWithPastAndCrossAttentions", "kind": "Gdef", "module_public": false}, "FlaxCausalLMOutputWithCrossAttentions": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_outputs.FlaxCausalLMOutputWithCrossAttentions", "kind": "Gdef", "module_public": false}, "FlaxPreTrainedModel": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.FlaxPreTrainedModel", "kind": "Gdef", "module_public": false}, "FlaxSeq2SeqLMOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_outputs.FlaxSeq2SeqLMOutput", "kind": "Gdef", "module_public": false}, "FlaxSeq2SeqModelOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_outputs.FlaxSeq2SeqModelOutput", "kind": "Gdef", "module_public": false}, "FlaxSequenceClassifierOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_outputs.FlaxSequenceClassifierOutput", "kind": "Gdef", "module_public": false}, "FlaxWhisperAttention": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention", "name": "FlaxWhisperAttention", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.whisper.modeling_flax_whisper", "mro": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "key_value_states", "attention_mask", "init_cache", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "key_value_states", "attention_mask", "init_cache", "deterministic"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention", {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of FlaxWhisperAttention", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_concatenate_to_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "key", "value", "query", "attention_mask"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention._concatenate_to_cache", "name": "_concatenate_to_cache", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "key", "value", "query", "attention_mask"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_concatenate_to_cache of FlaxWhisperAttention", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention._concatenate_to_cache", "name": "_concatenate_to_cache", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "_merge_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention._merge_heads", "name": "_merge_heads", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_merge_heads of FlaxWhisperAttention", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_split_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention._split_heads", "name": "_split_heads", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hidden_state"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_split_heads of FlaxWhisperAttention", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention.bias", "name": "bias", "setter_type": null, "type": "builtins.bool"}}, "causal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention.causal", "name": "causal", "setter_type": null, "type": "builtins.bool"}}, "causal_mask": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention.causal_mask", "name": "causal_mask", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.make_causal_mask", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.make_causal_mask", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention.config", "name": "config", "setter_type": null, "type": "transformers.models.whisper.configuration_whisper.WhisperConfig"}}, "dropout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention.dropout", "name": "dropout", "setter_type": null, "type": "builtins.float"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}}}, "embed_dim": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention.embed_dim", "name": "embed_dim", "setter_type": null, "type": "builtins.int"}}, "head_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention.head_dim", "name": "head_dim", "setter_type": null, "type": "builtins.int"}}, "k_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention.k_proj", "name": "k_proj", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "num_heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention.num_heads", "name": "num_heads", "setter_type": null, "type": "builtins.int"}}, "out_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention.out_proj", "name": "out_proj", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "q_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention.q_proj", "name": "q_proj", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention.setup", "name": "setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setup of FlaxWhisperAttention", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "v_proj": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention.v_proj", "name": "v_proj", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWhisperDecoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder", "name": "FlaxWhisperDecoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.whisper.modeling_flax_whisper", "mro": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "encoder_hidden_states", "init_cache", "output_attentions", "output_hidden_states", "return_dict", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_ids", "attention_mask", "position_ids", "encoder_hidden_states", "init_cache", "output_attentions", "output_hidden_states", "return_dict", "deterministic"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder", {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of FlaxWhisperDecoder", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder.config", "name": "config", "setter_type": null, "type": "transformers.models.whisper.configuration_whisper.WhisperConfig"}}, "dropout_layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder.dropout_layer", "name": "dropout_layer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}}}, "embed_positions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder.embed_positions", "name": "embed_positions", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "embed_tokens": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder.embed_tokens", "name": "embed_tokens", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder.layers", "name": "layers", "setter_type": null, "type": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayerCollection"}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder.setup", "name": "setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setup of FlaxWhisperDecoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWhisperDecoderLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer", "name": "FlaxWhisperDecoderLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.whisper.modeling_flax_whisper", "mro": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "encoder_hidden_states", "encoder_attention_mask", "init_cache", "output_attentions", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "encoder_hidden_states", "encoder_attention_mask", "init_cache", "output_attentions", "deterministic"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer", {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of FlaxWhisperDecoderLayer", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "activation_dropout_layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer.activation_dropout_layer", "name": "activation_dropout_layer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "activation_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer.activation_fn", "name": "activation_fn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_flax_utils.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_flax_utils.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer.config", "name": "config", "setter_type": null, "type": "transformers.models.whisper.configuration_whisper.WhisperConfig"}}, "dropout_layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer.dropout_layer", "name": "dropout_layer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}}}, "embed_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer.embed_dim", "name": "embed_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "encoder_attn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer.encoder_attn", "name": "encoder_attn", "setter_type": null, "type": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention"}}, "encoder_attn_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer.encoder_attn_layer_norm", "name": "encoder_attn_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "fc1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer.fc1", "name": "fc1", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "fc2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer.fc2", "name": "fc2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "final_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer.final_layer_norm", "name": "final_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "self_attn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer.self_attn", "name": "self_attn", "setter_type": null, "type": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention"}}, "self_attn_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer.self_attn_layer_norm", "name": "self_attn_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer.setup", "name": "setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setup of FlaxWhisperDecoderLayer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWhisperDecoderLayerCollection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayerCollection", "name": "FlaxWhisperDecoderLayerCollection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayerCollection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.whisper.modeling_flax_whisper", "mro": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayerCollection", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "encoder_hidden_states", "encoder_attention_mask", "deterministic", "init_cache", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayerCollection.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "encoder_hidden_states", "encoder_attention_mask", "deterministic", "init_cache", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayerCollection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of FlaxWhisperDecoderLayerCollection", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayerCollection.config", "name": "config", "setter_type": null, "type": "transformers.models.whisper.configuration_whisper.WhisperConfig"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayerCollection.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}}}, "gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayerCollection.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}, "layerdrop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayerCollection.layerdrop", "name": "layerdrop", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayerCollection.layers", "name": "layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayerCollection.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayerCollection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoderLayerCollection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWhisperEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder", "name": "FlaxWhisperEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.whisper.modeling_flax_whisper", "mro": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "input_features", "output_attentions", "output_hidden_states", "return_dict", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "input_features", "output_attentions", "output_hidden_states", "return_dict", "deterministic"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder", {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of FlaxWhisperEncoder", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder.config", "name": "config", "setter_type": null, "type": "transformers.models.whisper.configuration_whisper.WhisperConfig"}}, "conv1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder.conv1", "name": "conv1", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "conv2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder.conv2", "name": "conv2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "dropout_layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder.dropout_layer", "name": "dropout_layer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}}}, "embed_positions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder.embed_positions", "name": "embed_positions", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}, "layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder.layer_norm", "name": "layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder.layers", "name": "layers", "setter_type": null, "type": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayerCollection"}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder.setup", "name": "setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setup of FlaxWhisperEncoder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWhisperEncoderLayer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer", "name": "FlaxWhisperEncoderLayer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.whisper.modeling_flax_whisper", "mro": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "output_attentions", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "output_attentions", "deterministic"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer", {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of FlaxWhisperEncoderLayer", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "activation_dropout_layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer.activation_dropout_layer", "name": "activation_dropout_layer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "activation_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer.activation_fn", "name": "activation_fn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.modeling_flax_utils.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.modeling_flax_utils.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer.config", "name": "config", "setter_type": null, "type": "transformers.models.whisper.configuration_whisper.WhisperConfig"}}, "dropout_layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer.dropout_layer", "name": "dropout_layer", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}}}, "embed_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer.embed_dim", "name": "embed_dim", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "fc1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer.fc1", "name": "fc1", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "fc2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer.fc2", "name": "fc2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "final_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer.final_layer_norm", "name": "final_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "self_attn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer.self_attn", "name": "self_attn", "setter_type": null, "type": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperAttention"}}, "self_attn_layer_norm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer.self_attn_layer_norm", "name": "self_attn_layer_norm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer.setup", "name": "setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setup of FlaxWhisperEncoderLayer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWhisperEncoderLayerCollection": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayerCollection", "name": "FlaxWhisperEncoderLayerCollection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayerCollection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.whisper.modeling_flax_whisper", "mro": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayerCollection", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "deterministic", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayerCollection.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "hidden_states", "attention_mask", "deterministic", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayerCollection", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of FlaxWhisperEncoderLayerCollection", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayerCollection.config", "name": "config", "setter_type": null, "type": "transformers.models.whisper.configuration_whisper.WhisperConfig"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayerCollection.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}}}, "gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayerCollection.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}, "layerdrop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayerCollection.layerdrop", "name": "layerdrop", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayerCollection.layers", "name": "layers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayerCollection.setup", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayerCollection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoderLayerCollection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWhisperForAudioClassification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassification", "name": "FlaxWhisperForAudioClassification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassification", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.whisper.modeling_flax_whisper", "mro": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassification", "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel", "transformers.modeling_flax_utils.FlaxPreTrainedModel", "transformers.utils.hub.PushToHubMixin", "transformers.generation.flax_utils.FlaxGenerationMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "input_features", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassification.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "input_features", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng", "kwargs"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassification", {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.PRNGKey", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of FlaxWhisperForAudioClassification", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassification.__call__", "name": "__call__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassification.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}}}, "init_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rng", "input_shape", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassification.init_weights", "name": "init_weights", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rng", "input_shape", "params"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassification", {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jax", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.FrozenDict", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "init_weights of FlaxWhisperForAudioClassification", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.FrozenDict", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "module_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassification.module_class", "name": "module_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassificationModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWhisperForAudioClassificationModule": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassificationModule", "name": "FlaxWhisperForAudioClassificationModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassificationModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.whisper.modeling_flax_whisper", "mro": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassificationModule", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "input_features", "encoder_outputs", "output_attentions", "output_hidden_states", "return_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassificationModule.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "input_features", "encoder_outputs", "output_attentions", "output_hidden_states", "return_dict"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassificationModule", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of FlaxWhisperForAudioClassificationModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "classifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassificationModule.classifier", "name": "classifier", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassificationModule.config", "name": "config", "setter_type": null, "type": "transformers.models.whisper.configuration_whisper.WhisperConfig"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassificationModule.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassificationModule.encoder", "name": "encoder", "setter_type": null, "type": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder"}}, "gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassificationModule.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}, "layer_weights": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassificationModule.layer_weights", "name": "layer_weights", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "projector": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassificationModule.projector", "name": "projector", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassificationModule.setup", "name": "setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassificationModule"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setup of FlaxWhisperForAudioClassificationModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassificationModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForAudioClassificationModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWhisperForConditionalGeneration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGeneration", "name": "FlaxWhisperForConditionalGeneration", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGeneration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.whisper.modeling_flax_whisper", "mro": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGeneration", "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel", "transformers.modeling_flax_utils.FlaxPreTrainedModel", "transformers.utils.hub.PushToHubMixin", "transformers.generation.flax_utils.FlaxGenerationMixin", "builtins.object"], "names": {".class": "SymbolTable", "decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "decoder_input_ids", "encoder_outputs", "encoder_attention_mask", "decoder_attention_mask", "decoder_position_ids", "past_key_values", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGeneration.decode", "name": "decode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "decoder_input_ids", "encoder_outputs", "encoder_attention_mask", "decoder_attention_mask", "decoder_position_ids", "past_key_values", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGeneration", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.PRNGKey", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "decode of FlaxWhisperForConditionalGeneration", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGeneration.decode", "name": "decode", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGeneration.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}}}, "generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "input_features", "generation_config", "logits_processor", "return_timestamps", "task", "language", "is_multilingual", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGeneration.generate", "name": "generate", "type": null}}, "module_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGeneration.module_class", "name": "module_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGenerationModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prepare_inputs_for_generation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "decoder_input_ids", "max_length", "attention_mask", "decoder_attention_mask", "encoder_outputs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGeneration.prepare_inputs_for_generation", "name": "prepare_inputs_for_generation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "decoder_input_ids", "max_length", "attention_mask", "decoder_attention_mask", "encoder_outputs", "kwargs"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGeneration", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jax", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jax", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "prepare_inputs_for_generation of FlaxWhisperForConditionalGeneration", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_inputs_for_generation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "model_outputs", "model_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGeneration.update_inputs_for_generation", "name": "update_inputs_for_generation", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGeneration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGeneration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWhisperForConditionalGenerationModule": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGenerationModule", "name": "FlaxWhisperForConditionalGenerationModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGenerationModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.whisper.modeling_flax_whisper", "mro": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGenerationModule", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_features", "decoder_input_ids", "decoder_attention_mask", "decoder_position_ids", "position_ids", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGenerationModule.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_features", "decoder_input_ids", "decoder_attention_mask", "decoder_position_ids", "position_ids", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "deterministic"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGenerationModule", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of FlaxWhisperForConditionalGenerationModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_decoder_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGenerationModule._get_decoder_module", "name": "_get_decoder_module", "type": null}}, "_get_encoder_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGenerationModule._get_encoder_module", "name": "_get_encoder_module", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGenerationModule.config", "name": "config", "setter_type": null, "type": "transformers.models.whisper.configuration_whisper.WhisperConfig"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGenerationModule.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}}}, "gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGenerationModule.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}, "lm_head": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGenerationModule.lm_head", "name": "lm_head", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGenerationModule.model", "name": "model", "setter_type": null, "type": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule"}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGenerationModule.setup", "name": "setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGenerationModule"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setup of FlaxWhisperForConditionalGenerationModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGenerationModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperForConditionalGenerationModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWhisperModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModel", "name": "FlaxWhisperModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.whisper.modeling_flax_whisper", "mro": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModel", "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel", "transformers.modeling_flax_utils.FlaxPreTrainedModel", "transformers.utils.hub.PushToHubMixin", "transformers.generation.flax_utils.FlaxGenerationMixin", "builtins.object"], "names": {".class": "SymbolTable", "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModel.config", "name": "config", "setter_type": null, "type": "transformers.models.whisper.configuration_whisper.WhisperConfig"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModel.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}}}, "module_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModel.module_class", "name": "module_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWhisperModule": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule", "name": "FlaxWhisperModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.whisper.modeling_flax_whisper", "mro": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "input_features", "decoder_input_ids", "decoder_attention_mask", "decoder_position_ids", "output_attentions", "output_hidden_states", "return_dict", "deterministic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "input_features", "decoder_input_ids", "decoder_attention_mask", "decoder_position_ids", "output_attentions", "output_hidden_states", "return_dict", "deterministic"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule", {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of FlaxWhisperModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_decoder_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule._get_decoder_module", "name": "_get_decoder_module", "type": null}}, "_get_encoder_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule._get_encoder_module", "name": "_get_encoder_module", "type": null}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule.config", "name": "config", "setter_type": null, "type": "transformers.models.whisper.configuration_whisper.WhisperConfig"}}, "decoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule.decoder", "name": "decoder", "setter_type": null, "type": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperDecoder"}}, "dtype": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule.dtype", "name": "dtype", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}}}, "encoder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule.encoder", "name": "encoder", "setter_type": null, "type": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperEncoder"}}, "gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule.gradient_checkpointing", "name": "gradient_checkpointing", "setter_type": null, "type": "builtins.bool"}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule.setup", "name": "setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "setup of FlaxWhisperModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWhisperPreTrainedModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.modeling_flax_utils.FlaxPreTrainedModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel", "name": "FlaxWhisperPreTrainedModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.models.whisper.modeling_flax_whisper", "mro": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel", "transformers.modeling_flax_utils.FlaxPreTrainedModel", "transformers.utils.hub.PushToHubMixin", "transformers.generation.flax_utils.FlaxGenerationMixin", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_features", "decoder_input_ids", "attention_mask", "decoder_attention_mask", "position_ids", "decoder_position_ids", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "input_features", "decoder_input_ids", "attention_mask", "decoder_attention_mask", "position_ids", "decoder_position_ids", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel", {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.PRNGKey", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of FlaxWhisperPreTrainedModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel.__call__", "name": "__call__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "config", "input_shape", "seed", "dtype", "_do_init", "gradient_checkpointing", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "config", "input_shape", "seed", "dtype", "_do_init", "gradient_checkpointing", "kwargs"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel", "transformers.models.whisper.configuration_whisper.WhisperConfig", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of FlaxWhisperPreTrainedModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base_model_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel.base_model_prefix", "name": "base_model_prefix", "setter_type": null, "type": "builtins.str"}}, "config_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel.config_class", "name": "config_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["vocab_size", "num_mel_bins", "encoder_layers", "encoder_attention_heads", "decoder_layers", "decoder_attention_heads", "decoder_ffn_dim", "encoder_ffn_dim", "encoder_layerdrop", "decoder_layerdrop", "decoder_start_token_id", "use_cache", "is_encoder_decoder", "activation_function", "d_model", "dropout", "attention_dropout", "activation_dropout", "init_std", "scale_embedding", "max_source_positions", "max_target_positions", "pad_token_id", "bos_token_id", "eos_token_id", "suppress_tokens", "begin_suppress_tokens", "use_weighted_layer_sum", "classifier_proj_size", "apply_spec_augment", "mask_time_prob", "mask_time_length", "mask_time_min_masks", "mask_feature_prob", "mask_feature_length", "mask_feature_min_masks", "median_filter_width", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "transformers.models.whisper.configuration_whisper.WhisperConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "decoder_input_ids", "encoder_outputs", "encoder_attention_mask", "decoder_attention_mask", "decoder_position_ids", "past_key_values", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel.decode", "name": "decode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "decoder_input_ids", "encoder_outputs", "encoder_attention_mask", "decoder_attention_mask", "decoder_position_ids", "past_key_values", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.PRNGKey", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "decode of FlaxWhisperPreTrainedModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel.decode", "name": "decode", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "enable_gradient_checkpointing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel.enable_gradient_checkpointing", "name": "enable_gradient_checkpointing", "type": null}}, "encode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "input_features", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel.encode", "name": "encode", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "input_features", "attention_mask", "output_attentions", "output_hidden_states", "return_dict", "train", "params", "dropout_rng", "kwargs"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel", {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.PRNGKey", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "encode of FlaxWhisperPreTrainedModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel.encode", "name": "encode", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "init_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "batch_size", "max_length", "encoder_outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel.init_cache", "name": "init_cache", "type": null}}, "init_weights": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rng", "input_shape", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel.init_weights", "name": "init_weights", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rng", "input_shape", "params"], "arg_types": ["transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel", {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jax", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.FrozenDict", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "init_weights of FlaxWhisperPreTrainedModel", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.FrozenDict", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "main_input_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel.main_input_name", "name": "main_input_name", "setter_type": null, "type": "builtins.str"}}, "module_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel.module_class", "name": "module_class", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.models.whisper.modeling_flax_whisper.FlaxWhisperPreTrainedModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FlaxWhisperTimeStampLogitsProcessor": {".class": "SymbolTableNode", "cross_ref": "transformers.generation.flax_logits_process.FlaxWhisperTimeStampLogitsProcessor", "kind": "Gdef", "module_public": false}, "FrozenDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.FrozenDict", "name": "FrozenDict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.FrozenDict", "source_any": null, "type_of_any": 3}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PRNGKey": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.PRNGKey", "name": "PRNGKey", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.PRNGKey", "source_any": null, "type_of_any": 3}}}, "WHISPER_DECODE_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.WHISPER_DECODE_INPUTS_DOCSTRING", "name": "WHISPER_DECODE_INPUTS_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "WHISPER_ENCODE_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.WHISPER_ENCODE_INPUTS_DOCSTRING", "name": "WHISPER_ENCODE_INPUTS_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "WHISPER_INPUTS_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.WHISPER_INPUTS_DOCSTRING", "name": "WHISPER_INPUTS_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "WHISPER_START_DOCSTRING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.WHISPER_START_DOCSTRING", "name": "WHISPER_START_DOCSTRING", "setter_type": null, "type": "builtins.str"}}, "WhisperConfig": {".class": "SymbolTableNode", "cross_ref": "transformers.models.whisper.configuration_whisper.WhisperConfig", "kind": "Gdef", "module_public": false}, "_CHECKPOINT_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper._CHECKPOINT_FOR_DOC", "name": "_CHECKPOINT_FOR_DOC", "setter_type": null, "type": "builtins.str"}}, "_CONFIG_FOR_DOC": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper._CONFIG_FOR_DOC", "name": "_CONFIG_FOR_DOC", "setter_type": null, "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.models.whisper.modeling_flax_whisper.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "add_start_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings", "kind": "Gdef", "module_public": false}, "add_start_docstrings_to_model_forward": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.add_start_docstrings_to_model_forward", "kind": "Gdef", "module_public": false}, "append_call_sample_docstring": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.append_call_sample_docstring", "kind": "Gdef", "module_public": false}, "append_replace_return_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.append_replace_return_docstrings", "kind": "Gdef", "module_public": false}, "combine_masks": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.combine_masks", "name": "combine_masks", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.combine_masks", "source_any": null, "type_of_any": 3}}}, "dot_product_attention_weights": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.dot_product_attention_weights", "name": "dot_product_attention_weights", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.dot_product_attention_weights", "source_any": null, "type_of_any": 3}}}, "flatten_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.flatten_dict", "name": "flatten_dict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.flatten_dict", "source_any": null, "type_of_any": 3}}}, "freeze": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.freeze", "name": "freeze", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.freeze", "source_any": null, "type_of_any": 3}}}, "jax": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.jax", "name": "jax", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jax", "source_any": null, "type_of_any": 3}}}, "jnp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.jnp", "name": "jnp", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jnp", "source_any": null, "type_of_any": 3}}}, "lax": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.lax", "name": "lax", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.lax", "source_any": null, "type_of_any": 3}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef", "module_public": false}, "make_causal_mask": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.make_causal_mask", "name": "make_causal_mask", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.make_causal_mask", "source_any": null, "type_of_any": 3}}}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.nn", "name": "nn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn", "source_any": null, "type_of_any": 3}}}, "nn_partitioning": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.nn_partitioning", "name": "nn_partitioning", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn_partitioning", "source_any": null, "type_of_any": 3}}}, "overwrite_call_docstring": {".class": "SymbolTableNode", "cross_ref": "transformers.modeling_flax_utils.overwrite_call_docstring", "kind": "Gdef", "module_public": false}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef", "module_public": false}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef", "module_public": false}, "remat": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.models.whisper.modeling_flax_whisper.remat", "name": "remat", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn_partitioning", "source_any": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.nn_partitioning", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "replace_return_docstrings": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.replace_return_docstrings", "kind": "Gdef", "module_public": false}, "sinusoidal_embedding_init": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["key", "shape", "dtype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.models.whisper.modeling_flax_whisper.sinusoidal_embedding_init", "name": "sinusoidal_embedding_init", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["key", "shape", "dtype"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sinusoidal_embedding_init", "ret_type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.jax", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unflatten_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.unflatten_dict", "name": "unflatten_dict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.unflatten_dict", "source_any": null, "type_of_any": 3}}}, "unfreeze": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "transformers.models.whisper.modeling_flax_whisper.unfreeze", "name": "unfreeze", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "transformers.models.whisper.modeling_flax_whisper.unfreeze", "source_any": null, "type_of_any": 3}}}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/models/whisper/modeling_flax_whisper.py"}