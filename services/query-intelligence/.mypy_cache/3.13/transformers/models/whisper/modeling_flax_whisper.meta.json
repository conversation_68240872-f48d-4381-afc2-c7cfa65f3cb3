{"data_mtime": 1752049733, "dep_lines": [50, 33, 49, 34, 42, 49, 17, 18, 19, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 25, 28, 22, 24, 29, 31, 22, 23], "dep_prios": [5, 5, 10, 5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 10, 5, 5, 20, 5], "dependencies": ["transformers.models.whisper.configuration_whisper", "transformers.generation.flax_logits_process", "transformers.utils.logging", "transformers.modeling_flax_outputs", "transformers.modeling_flax_utils", "transformers.utils", "math", "random", "functools", "typing", "builtins", "_collections_abc", "_frozen_importlib", "abc", "collections", "logging", "transformers.configuration_utils", "transformers.generation", "transformers.generation.flax_utils", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "2eeda4beb86d0e0ebc7a53fc920083e0ec235c5d", "id": "transformers.models.whisper.modeling_flax_whisper", "ignore_all": true, "interface_hash": "523391579dfaca00de8cd0ea2edfedf96f37e7f4", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/models/whisper/modeling_flax_whisper.py", "plugin_data": null, "size": 73849, "suppressed": ["flax.core.frozen_dict", "flax.linen.attention", "flax.linen", "jax.numpy", "flax.traverse_util", "jax.random", "flax", "jax"], "version_id": "1.16.1"}