{"data_mtime": 1752049733, "dep_lines": [30, 31, 29, 27, 28, 29, 17, 18, 19, 20, 21, 22, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 25], "dep_prios": [5, 5, 10, 5, 5, 20, 10, 10, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["transformers.models.whisper.english_normalizer", "transformers.models.whisper.tokenization_whisper", "transformers.utils.logging", "transformers.tokenization_utils_base", "transformers.tokenization_utils_fast", "transformers.utils", "json", "os", "re", "warnings", "functools", "typing", "numpy", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "collections", "enum", "io", "json.decoder", "logging", "transformers.tokenization_utils", "transformers.utils.hub", "types"], "hash": "3cdce78d4e8222e5fb760b91abdfca64d1243d90", "id": "transformers.models.whisper.tokenization_whisper_fast", "ignore_all": true, "interface_hash": "a9416c039ad4fdb283fab4053ca99c0c993208d0", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/models/whisper/tokenization_whisper_fast.py", "plugin_data": null, "size": 30284, "suppressed": ["tokenizers"], "version_id": "1.16.1"}