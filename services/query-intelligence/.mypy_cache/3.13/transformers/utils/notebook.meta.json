{"data_mtime": 1752049734, "dep_lines": [22, 23, 15, 16, 17, 18, 1, 1, 1, 1, 1, 1, 1, 20, 20], "dep_prios": [5, 5, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 10, 20], "dependencies": ["transformers.trainer_callback", "transformers.trainer_utils", "os", "re", "time", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "transformers.utils.generic"], "hash": "d8645d331f1045414a6c144e3309f36dd5864b2d", "id": "transformers.utils.notebook", "ignore_all": true, "interface_hash": "323153b393d0079333ffe8cf0d673df9ff19c1e6", "mtime": 1751921833, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/transformers/utils/notebook.py", "plugin_data": null, "size": 15825, "suppressed": ["IPython.display", "IPython"], "version_id": "1.16.1"}