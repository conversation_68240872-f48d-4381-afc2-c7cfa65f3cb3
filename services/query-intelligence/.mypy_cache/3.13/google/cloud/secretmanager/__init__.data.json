{".class": "MypyFile", "_fullname": "google.cloud.secretmanager", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AccessSecretVersionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.service.AccessSecretVersionRequest", "kind": "Gdef"}, "AccessSecretVersionResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.service.AccessSecretVersionResponse", "kind": "Gdef"}, "AddSecretVersionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.service.AddSecretVersionRequest", "kind": "Gdef"}, "CreateSecretRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.service.CreateSecretRequest", "kind": "Gdef"}, "CustomerManagedEncryption": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryption", "kind": "Gdef"}, "CustomerManagedEncryptionStatus": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.resources.CustomerManagedEncryptionStatus", "kind": "Gdef"}, "DeleteSecretRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.service.DeleteSecretRequest", "kind": "Gdef"}, "DestroySecretVersionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.service.DestroySecretVersionRequest", "kind": "Gdef"}, "DisableSecretVersionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.service.DisableSecretVersionRequest", "kind": "Gdef"}, "EnableSecretVersionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.service.EnableSecretVersionRequest", "kind": "Gdef"}, "GetSecretRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.service.GetSecretRequest", "kind": "Gdef"}, "GetSecretVersionRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.service.GetSecretVersionRequest", "kind": "Gdef"}, "ListSecretVersionsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsRequest", "kind": "Gdef"}, "ListSecretVersionsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse", "kind": "Gdef"}, "ListSecretsRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.service.ListSecretsRequest", "kind": "Gdef"}, "ListSecretsResponse": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.service.ListSecretsResponse", "kind": "Gdef"}, "Replication": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.resources.Replication", "kind": "Gdef"}, "ReplicationStatus": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.resources.ReplicationStatus", "kind": "Gdef"}, "Rotation": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.resources.Rotation", "kind": "Gdef"}, "Secret": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.resources.Secret", "kind": "Gdef"}, "SecretManagerServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.services.secret_manager_service.async_client.SecretManagerServiceAsyncClient", "kind": "Gdef"}, "SecretManagerServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.services.secret_manager_service.client.SecretManagerServiceClient", "kind": "Gdef"}, "SecretPayload": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.resources.SecretPayload", "kind": "Gdef"}, "SecretVersion": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.resources.SecretVersion", "kind": "Gdef"}, "Topic": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.resources.Topic", "kind": "Gdef"}, "UpdateSecretRequest": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.service.UpdateSecretRequest", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.secretmanager.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.cloud.secretmanager.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}, "package_version": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager.gapic_version", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/google/cloud/secretmanager/__init__.py"}