{"data_mtime": 1752049735, "dep_lines": [40, 41, 38, 38, 27, 38, 23, 24, 24, 25, 26, 44, 23, 26, 16, 17, 18, 19, 20, 21, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 28, 29, 32, 33, 31, 35, 34, 36], "dep_prios": [5, 5, 10, 10, 5, 20, 10, 10, 10, 10, 10, 10, 20, 20, 10, 10, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 10, 5, 5, 10, 10], "dependencies": ["google.cloud.secretmanager_v1.services.secret_manager_service.transports.base", "google.cloud.secretmanager_v1.services.secret_manager_service.transports.grpc", "google.cloud.secretmanager_v1.types.resources", "google.cloud.secretmanager_v1.types.service", "google.auth.transport.grpc", "google.cloud.secretmanager_v1.types", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.grpc_helpers_async", "google.api_core.retry_async", "google.auth.credentials", "google.api_core.client_logging", "google.api_core", "google.auth", "inspect", "json", "logging", "pickle", "typing", "warnings", "google", "builtins", "_frozen_importlib", "_warnings", "abc", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method_async", "google.api_core.retry", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary_async", "google.auth._credentials_base", "google.auth.transport", "types"], "hash": "053f6f96bee7ee34b5f33765d4fe4818aeb1c29d", "id": "google.cloud.secretmanager_v1.services.secret_manager_service.transports.grpc_asyncio", "ignore_all": true, "interface_hash": "96239e7f67a3037ad1830a6dc7a12e38a37792ae", "mtime": 1751997149, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/google/cloud/secretmanager_v1/services/secret_manager_service/transports/grpc_asyncio.py", "plugin_data": null, "size": 41485, "suppressed": ["google.cloud.location", "google.iam.v1", "google.protobuf.json_format", "google.protobuf.message", "google.protobuf", "grpc.experimental", "grpc", "proto"], "version_id": "1.16.1"}