{"data_mtime": 1752049735, "dep_lines": [58, 59, 54, 57, 54, 55, 55, 40, 55, 32, 33, 34, 35, 36, 37, 40, 62, 32, 36, 37, 16, 17, 18, 19, 38, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 47, 48, 38], "dep_prios": [5, 5, 10, 5, 20, 10, 10, 10, 20, 10, 10, 10, 5, 10, 10, 20, 10, 20, 20, 20, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["google.cloud.secretmanager_v1.services.secret_manager_service.transports.base", "google.cloud.secretmanager_v1.services.secret_manager_service.transports.grpc_asyncio", "google.cloud.secretmanager_v1.services.secret_manager_service.pagers", "google.cloud.secretmanager_v1.services.secret_manager_service.client", "google.cloud.secretmanager_v1.services.secret_manager_service", "google.cloud.secretmanager_v1.types.resources", "google.cloud.secretmanager_v1.types.service", "google.cloud.secretmanager_v1.gapic_version", "google.cloud.secretmanager_v1.types", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.retry_async", "google.api_core.client_options", "google.auth.credentials", "google.oauth2.service_account", "google.cloud.secretmanager_v1", "google.api_core.client_logging", "google.api_core", "google.auth", "google.oauth2", "collections", "logging", "re", "typing", "google", "builtins", "_frozen_importlib", "abc", "enum", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.gapic_v1.routing_header", "google.api_core.retry", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary_async", "google.auth._credentials_base", "google.cloud.secretmanager_v1.services.secret_manager_service.transports", "types"], "hash": "095cd0a6d255719bc130564b3b28bb842e9238cf", "id": "google.cloud.secretmanager_v1.services.secret_manager_service.async_client", "ignore_all": true, "interface_hash": "aa7b804b6d3da9b15b3f2a3d0e8e8b37200bbeae", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/google/cloud/secretmanager_v1/services/secret_manager_service/async_client.py", "plugin_data": null, "size": 100015, "suppressed": ["google.cloud.location", "google.iam.v1", "google.protobuf"], "version_id": "1.16.1"}