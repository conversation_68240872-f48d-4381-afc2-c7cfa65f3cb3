{"data_mtime": 1752049735, "dep_lines": [29, 27, 27, 27, 20, 20, 20, 16, 17, 18, 1, 1, 1, 1, 1, 21, 22, 24], "dep_prios": [5, 10, 10, 20, 10, 10, 20, 10, 10, 5, 5, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["google.cloud.secretmanager_v1.services.secret_manager_service.transports.base", "google.cloud.secretmanager_v1.types.resources", "google.cloud.secretmanager_v1.types.service", "google.cloud.secretmanager_v1.types", "google.api_core.gapic_v1", "google.api_core.path_template", "google.api_core", "json", "re", "typing", "builtins", "_frozen_importlib", "abc", "google.api_core.client_info", "google.api_core.gapic_v1.client_info"], "hash": "01629d7eb91c88c4c2e9468fb050d56725782cda", "id": "google.cloud.secretmanager_v1.services.secret_manager_service.transports.rest_base", "ignore_all": true, "interface_hash": "dfb031b7b12a19755d23ded05ec0dadacb649e06", "mtime": 1751997149, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/google/cloud/secretmanager_v1/services/secret_manager_service/transports/rest_base.py", "plugin_data": null, "size": 35329, "suppressed": ["google.cloud.location", "google.iam.v1", "google.protobuf"], "version_id": "1.16.1"}