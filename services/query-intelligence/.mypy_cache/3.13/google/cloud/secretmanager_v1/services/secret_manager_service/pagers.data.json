{".class": "MypyFile", "_fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncIterator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef"}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "ListSecretVersionsAsyncPager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager", "name": "ListSecretVersionsAsyncPager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers", "mro": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aiter__ of ListSecretVersionsAsyncPager", "ret_type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.resources.SecretVersion"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getattr__ of ListSecretVersionsAsyncPager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "method", "request", "response", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "method", "request", "response", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "google.cloud.secretmanager_v1.types.service.ListSecretVersionsRequest", "google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.OptionalAsyncRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ListSecretVersionsAsyncPager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of ListSecretVersionsAsyncPager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_metadata": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager._metadata", "name": "_metadata", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager._method", "name": "_method", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager._request", "name": "_request", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsRequest"}}, "_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager._response", "name": "_response", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse"}}, "_retry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager._retry", "name": "_retry", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.OptionalAsyncRetry"}}}, "_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager._timeout", "name": "_timeout", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}}}, "pages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_generator", "is_coroutine", "is_async_generator", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager.pages", "name": "pages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pages of ListSecretVersionsAsyncPager", "ret_type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager.pages", "name": "pages", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pages of ListSecretVersionsAsyncPager", "ret_type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsAsyncPager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListSecretVersionsPager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager", "name": "ListSecretVersionsPager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers", "mro": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getattr__ of ListSecretVersionsPager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "method", "request", "response", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "method", "request", "response", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "google.cloud.secretmanager_v1.types.service.ListSecretVersionsRequest", "google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ListSecretVersionsPager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iter__ of ListSecretVersionsPager", "ret_type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.resources.SecretVersion"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of ListSecretVersionsPager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_metadata": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager._metadata", "name": "_metadata", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager._method", "name": "_method", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager._request", "name": "_request", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsRequest"}}, "_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager._response", "name": "_response", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse"}}, "_retry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager._retry", "name": "_retry", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.OptionalRetry"}}}, "_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager._timeout", "name": "_timeout", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}}}, "pages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_generator", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager.pages", "name": "pages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pages of ListSecretVersionsPager", "ret_type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager.pages", "name": "pages", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pages of ListSecretVersionsPager", "ret_type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.service.ListSecretVersionsResponse"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretVersionsPager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListSecretsAsyncPager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager", "name": "ListSecretsAsyncPager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers", "mro": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aiter__ of ListSecretsAsyncPager", "ret_type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.resources.Secret"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getattr__ of ListSecretsAsyncPager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "method", "request", "response", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "method", "request", "response", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.service.ListSecretsResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "google.cloud.secretmanager_v1.types.service.ListSecretsRequest", "google.cloud.secretmanager_v1.types.service.ListSecretsResponse", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.OptionalAsyncRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ListSecretsAsyncPager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of ListSecretsAsyncPager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_metadata": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager._metadata", "name": "_metadata", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager._method", "name": "_method", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.service.ListSecretsResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager._request", "name": "_request", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.service.ListSecretsRequest"}}, "_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager._response", "name": "_response", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.service.ListSecretsResponse"}}, "_retry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager._retry", "name": "_retry", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.OptionalAsyncRetry"}}}, "_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager._timeout", "name": "_timeout", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}}}, "pages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_generator", "is_coroutine", "is_async_generator", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager.pages", "name": "pages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pages of ListSecretsAsyncPager", "ret_type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.service.ListSecretsResponse"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager.pages", "name": "pages", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pages of ListSecretsAsyncPager", "ret_type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.service.ListSecretsResponse"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsAsyncPager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListSecretsPager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager", "name": "ListSecretsPager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers", "mro": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getattr__ of ListSecretsPager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "method", "request", "response", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "method", "request", "response", "retry", "timeout", "metadata"], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "google.cloud.secretmanager_v1.types.service.ListSecretsResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "google.cloud.secretmanager_v1.types.service.ListSecretsRequest", "google.cloud.secretmanager_v1.types.service.ListSecretsResponse", {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ListSecretsPager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iter__ of ListSecretsPager", "ret_type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.resources.Secret"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of ListSecretsPager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_metadata": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager._metadata", "name": "_metadata", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager._method", "name": "_method", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "google.cloud.secretmanager_v1.types.service.ListSecretsResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager._request", "name": "_request", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.service.ListSecretsRequest"}}, "_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager._response", "name": "_response", "setter_type": null, "type": "google.cloud.secretmanager_v1.types.service.ListSecretsResponse"}}, "_retry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager._retry", "name": "_retry", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.OptionalRetry"}}}, "_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager._timeout", "name": "_timeout", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}}}, "pages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_generator", "is_decorated", "is_trivial_self"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager.pages", "name": "pages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pages of ListSecretsPager", "ret_type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.service.ListSecretsResponse"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager.pages", "name": "pages", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pages of ListSecretsPager", "ret_type": {".class": "Instance", "args": ["google.cloud.secretmanager_v1.types.service.ListSecretsResponse"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.ListSecretsPager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OptionalAsyncRetry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.OptionalAsyncRetry", "line": 34, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.api_core.retry.retry_unary_async.AsyncRetry", "google.api_core.gapic_v1.method._MethodDefault", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "OptionalRetry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.OptionalRetry", "line": 33, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.api_core.retry.retry_unary.Retry", "google.api_core.gapic_v1.method._MethodDefault", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "gapic_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1", "kind": "Gdef"}, "resources": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.resources", "kind": "Gdef"}, "retries": {".class": "SymbolTableNode", "cross_ref": "google.api_core.retry", "kind": "Gdef"}, "retries_async": {".class": "SymbolTableNode", "cross_ref": "google.api_core.retry_async", "kind": "Gdef"}, "service": {".class": "SymbolTableNode", "cross_ref": "google.cloud.secretmanager_v1.types.service", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/google/cloud/secretmanager_v1/services/secret_manager_service/pagers.py"}