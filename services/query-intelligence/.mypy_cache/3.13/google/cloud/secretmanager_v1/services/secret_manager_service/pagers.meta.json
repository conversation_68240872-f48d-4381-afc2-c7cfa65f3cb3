{"data_mtime": 1752049734, "dep_lines": [41, 41, 41, 28, 29, 30, 28, 16, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 10, 10, 10, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["google.cloud.secretmanager_v1.types.resources", "google.cloud.secretmanager_v1.types.service", "google.cloud.secretmanager_v1.types", "google.api_core.gapic_v1", "google.api_core.retry", "google.api_core.retry_async", "google.api_core", "typing", "builtins", "_frozen_importlib", "abc", "enum", "google.api_core.gapic_v1.method", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary", "google.api_core.retry.retry_unary_async"], "hash": "3161e12b3f031110019f2b6541c1947f9ee4eddd", "id": "google.cloud.secretmanager_v1.services.secret_manager_service.pagers", "ignore_all": true, "interface_hash": "4dc349eacc8f6910f88ab751ae433d9e23d3b19a", "mtime": 1751997149, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/google/cloud/secretmanager_v1/services/secret_manager_service/pagers.py", "plugin_data": null, "size": 14153, "suppressed": [], "version_id": "1.16.1"}