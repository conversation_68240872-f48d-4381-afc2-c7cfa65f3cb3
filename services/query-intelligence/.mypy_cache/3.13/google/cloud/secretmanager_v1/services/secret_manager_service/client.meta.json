{"data_mtime": 1752049735, "dep_lines": [74, 75, 76, 77, 71, 71, 72, 72, 43, 44, 48, 72, 37, 38, 39, 40, 41, 42, 43, 45, 48, 56, 713, 37, 41, 45, 16, 17, 18, 19, 20, 21, 22, 35, 46, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 64, 65, 46], "dep_prios": [5, 5, 5, 5, 10, 20, 10, 10, 10, 5, 10, 20, 10, 10, 10, 10, 10, 5, 20, 10, 20, 10, 20, 20, 20, 20, 5, 5, 10, 10, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["google.cloud.secretmanager_v1.services.secret_manager_service.transports.base", "google.cloud.secretmanager_v1.services.secret_manager_service.transports.grpc", "google.cloud.secretmanager_v1.services.secret_manager_service.transports.grpc_asyncio", "google.cloud.secretmanager_v1.services.secret_manager_service.transports.rest", "google.cloud.secretmanager_v1.services.secret_manager_service.pagers", "google.cloud.secretmanager_v1.services.secret_manager_service", "google.cloud.secretmanager_v1.types.resources", "google.cloud.secretmanager_v1.types.service", "google.auth.transport.mtls", "google.auth.transport.grpc", "google.cloud.secretmanager_v1.gapic_version", "google.cloud.secretmanager_v1.types", "google.api_core.client_options", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.retry", "google.auth.credentials", "google.auth.exceptions", "google.auth.transport", "google.oauth2.service_account", "google.cloud.secretmanager_v1", "google.api_core.client_logging", "google.auth._default", "google.api_core", "google.auth", "google.oauth2", "collections", "http", "json", "logging", "os", "re", "typing", "warnings", "google", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.gapic_v1.routing_header", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary", "google.auth._credentials_base", "google.cloud.secretmanager_v1.services.secret_manager_service.transports", "google.cloud.secretmanager_v1.services.secret_manager_service.transports.rest_base", "types"], "hash": "7757c68cefa32ab784b574227af1c82eff13a2ec", "id": "google.cloud.secretmanager_v1.services.secret_manager_service.client", "ignore_all": true, "interface_hash": "cd48655033d39866d8009a71aaa30ab6ea72bba5", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/google/cloud/secretmanager_v1/services/secret_manager_service/client.py", "plugin_data": null, "size": 116516, "suppressed": ["google.cloud.location", "google.iam.v1", "google.protobuf"], "version_id": "1.16.1"}