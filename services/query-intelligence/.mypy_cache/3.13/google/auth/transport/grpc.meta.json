{"data_mtime": **********, "dep_lines": [24, 22, 23, 24, 25, 22, 25, 17, 19, 20, 1, 1, 1, 1, 28], "dep_prios": [10, 10, 10, 20, 10, 20, 20, 5, 10, 10, 5, 30, 30, 30, 10], "dependencies": ["google.auth.transport._mtls_helper", "google.auth.environment_vars", "google.auth.exceptions", "google.auth.transport", "google.oauth2.service_account", "google.auth", "google.oauth2", "__future__", "logging", "os", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "cf6d26e3b65f6ce0311c75981366befa68f4a542", "id": "google.auth.transport.grpc", "ignore_all": true, "interface_hash": "ab3a28db50ba4e0f0c90615cd74d1a9b27a537af", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/google/auth/transport/grpc.py", "plugin_data": null, "size": 13931, "suppressed": ["grpc"], "version_id": "1.16.1"}