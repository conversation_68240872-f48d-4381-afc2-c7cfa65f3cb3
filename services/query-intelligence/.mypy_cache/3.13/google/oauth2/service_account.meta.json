{"data_mtime": **********, "dep_lines": [76, 77, 78, 79, 80, 81, 82, 83, 76, 83, 73, 74, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 20, 20, 10, 10, 5, 30, 30, 30, 30], "dependencies": ["google.auth._helpers", "google.auth._service_account_info", "google.auth.credentials", "google.auth.exceptions", "google.auth.iam", "google.auth.jwt", "google.auth.metrics", "google.oauth2._client", "google.auth", "google.oauth2", "copy", "datetime", "builtins", "_frozen_importlib", "abc", "google.auth._credentials_base", "typing"], "hash": "cd5105388382469b439003be575ae5d7e9f86545", "id": "google.oauth2.service_account", "ignore_all": true, "interface_hash": "4739db1a4f4565f3d54f0f2a80fc5a54acd9648e", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/google/oauth2/service_account.py", "plugin_data": null, "size": 32232, "suppressed": [], "version_id": "1.16.1"}