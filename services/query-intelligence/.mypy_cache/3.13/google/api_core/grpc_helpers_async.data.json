{".class": "MypyFile", "_fullname": "google.api_core.grpc_helpers_async", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncGenerator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncGenerator", "kind": "Gdef"}, "AwaitableGrpcCall": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.api_core.grpc_helpers_async.AwaitableGrpcCall", "line": 157, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin"}}}, "FakeStreamUnaryCall": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedStreamUnaryCall"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.api_core.grpc_helpers_async.FakeStreamUnaryCall", "name": "FakeStreamUnaryCall", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.api_core.grpc_helpers_async.FakeStreamUnaryCall", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.api_core.grpc_helpers_async", "mro": ["google.api_core.grpc_helpers_async.FakeStreamUnaryCall", "google.api_core.grpc_helpers_async._WrappedStreamUnaryCall", "google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin", "google.api_core.grpc_helpers_async._WrappedStreamRequestMixin", "google.api_core.grpc_helpers_async._WrappedCall", "builtins.object"], "names": {".class": "SymbolTable", "__await__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.grpc_helpers_async.FakeStreamUnaryCall.__await__", "name": "__await__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.grpc_helpers_async.FakeStreamUnaryCall.__init__", "name": "__init__", "type": null}}, "_future": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.grpc_helpers_async.FakeStreamUnaryCall._future", "name": "_future", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.grpc_helpers_async.FakeStreamUnaryCall.response", "name": "response", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "wait_for_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "google.api_core.grpc_helpers_async.FakeStreamUnaryCall.wait_for_connection", "name": "wait_for_connection", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.FakeStreamUnaryCall.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.api_core.grpc_helpers_async.FakeStreamUnaryCall", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeUnaryUnaryCall": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedUnaryUnaryCall"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.api_core.grpc_helpers_async.FakeUnaryUnaryCall", "name": "FakeUnaryUnaryCall", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.api_core.grpc_helpers_async.FakeUnaryUnaryCall", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.api_core.grpc_helpers_async", "mro": ["google.api_core.grpc_helpers_async.FakeUnaryUnaryCall", "google.api_core.grpc_helpers_async._WrappedUnaryUnaryCall", "google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin", "google.api_core.grpc_helpers_async._WrappedCall", "builtins.object"], "names": {".class": "SymbolTable", "__await__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.grpc_helpers_async.FakeUnaryUnaryCall.__await__", "name": "__await__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.grpc_helpers_async.FakeUnaryUnaryCall.__init__", "name": "__init__", "type": null}}, "_future": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.grpc_helpers_async.FakeUnaryUnaryCall._future", "name": "_future", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.grpc_helpers_async.FakeUnaryUnaryCall.response", "name": "response", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.FakeUnaryUnaryCall.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.api_core.grpc_helpers_async.FakeUnaryUnaryCall", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "GrpcAsyncStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.api_core.grpc_helpers_async.GrpcAsyncStream", "line": 155, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin"}}}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "P": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "name": "P", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "_WrappedCall": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.api_core.grpc_helpers_async._WrappedCall", "name": "_WrappedCall", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.api_core.grpc_helpers_async._WrappedCall", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.api_core.grpc_helpers_async", "mro": ["google.api_core.grpc_helpers_async._WrappedCall", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.grpc_helpers_async._WrappedCall.__init__", "name": "__init__", "type": null}}, "_call": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.grpc_helpers_async._WrappedCall._call", "name": "_call", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "add_done_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.grpc_helpers_async._WrappedCall.add_done_callback", "name": "add_done_callback", "type": null}}, "cancel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.grpc_helpers_async._WrappedCall.cancel", "name": "cancel", "type": null}}, "cancelled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.grpc_helpers_async._WrappedCall.cancelled", "name": "cancelled", "type": null}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "google.api_core.grpc_helpers_async._WrappedCall.code", "name": "code", "type": null}}, "details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "google.api_core.grpc_helpers_async._WrappedCall.details", "name": "details", "type": null}}, "done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.grpc_helpers_async._WrappedCall.done", "name": "done", "type": null}}, "initial_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "google.api_core.grpc_helpers_async._WrappedCall.initial_metadata", "name": "initial_metadata", "type": null}}, "time_remaining": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.grpc_helpers_async._WrappedCall.time_remaining", "name": "time_remaining", "type": null}}, "trailing_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "google.api_core.grpc_helpers_async._WrappedCall.trailing_metadata", "name": "trailing_metadata", "type": null}}, "wait_for_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "google.api_core.grpc_helpers_async._WrappedCall.wait_for_connection", "name": "wait_for_connection", "type": null}}, "with_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "call"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.grpc_helpers_async._WrappedCall.with_call", "name": "with_call", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async._WrappedCall.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.api_core.grpc_helpers_async._WrappedCall", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WrappedStreamRequestMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.api_core.grpc_helpers_async._WrappedCall"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.api_core.grpc_helpers_async._WrappedStreamRequestMixin", "name": "_WrappedStreamRequestMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.api_core.grpc_helpers_async._WrappedStreamRequestMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.api_core.grpc_helpers_async", "mro": ["google.api_core.grpc_helpers_async._WrappedStreamRequestMixin", "google.api_core.grpc_helpers_async._WrappedCall", "builtins.object"], "names": {".class": "SymbolTable", "done_writing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "google.api_core.grpc_helpers_async._WrappedStreamRequestMixin.done_writing", "name": "done_writing", "type": null}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "google.api_core.grpc_helpers_async._WrappedStreamRequestMixin.write", "name": "write", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async._WrappedStreamRequestMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.api_core.grpc_helpers_async._WrappedStreamRequestMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WrappedStreamResponseMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.api_core.grpc_helpers_async._WrappedCall"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin", "name": "_WrappedStreamResponseMixin", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.api_core.grpc_helpers_async", "mro": ["google.api_core.grpc_helpers_async._WrappedStreamResponseMixin", "google.api_core.grpc_helpers_async._WrappedCall", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aiter__ of _WrappedStreamResponseMixin", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.AsyncGenerator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin.__init__", "name": "__init__", "type": null}}, "_wrapped_aiter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator", "is_trivial_self"], "fullname": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin._wrapped_aiter", "name": "_wrapped_aiter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_wrapped_aiter of _WrappedStreamResponseMixin", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.AsyncGenerator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_wrapped_async_generator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin._wrapped_async_generator", "name": "_wrapped_async_generator", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read of _WrappedStreamResponseMixin", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["P"], "typeddict_type": null}}, "_WrappedStreamStreamCall": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.api_core.grpc_helpers_async._WrappedStreamRequestMixin", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedStreamStreamCall", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.api_core.grpc_helpers_async._WrappedStreamStreamCall", "name": "_WrappedStreamStreamCall", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedStreamStreamCall", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.api_core.grpc_helpers_async._WrappedStreamStreamCall", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.api_core.grpc_helpers_async", "mro": ["google.api_core.grpc_helpers_async._WrappedStreamStreamCall", "google.api_core.grpc_helpers_async._WrappedStreamRequestMixin", "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin", "google.api_core.grpc_helpers_async._WrappedCall", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async._WrappedStreamStreamCall.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedStreamStreamCall", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedStreamStreamCall"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["P"], "typeddict_type": null}}, "_WrappedStreamUnaryCall": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedStreamUnaryCall", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin"}, "google.api_core.grpc_helpers_async._WrappedStreamRequestMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.api_core.grpc_helpers_async._WrappedStreamUnaryCall", "name": "_WrappedStreamUnaryCall", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedStreamUnaryCall", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.api_core.grpc_helpers_async._WrappedStreamUnaryCall", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.api_core.grpc_helpers_async", "mro": ["google.api_core.grpc_helpers_async._WrappedStreamUnaryCall", "google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin", "google.api_core.grpc_helpers_async._WrappedStreamRequestMixin", "google.api_core.grpc_helpers_async._WrappedCall", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async._WrappedStreamUnaryCall.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedStreamUnaryCall", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedStreamUnaryCall"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["P"], "typeddict_type": null}}, "_WrappedUnaryResponseMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.api_core.grpc_helpers_async._WrappedCall"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin", "name": "_WrappedUnaryResponseMixin", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.api_core.grpc_helpers_async", "mro": ["google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin", "google.api_core.grpc_helpers_async._WrappedCall", "builtins.object"], "names": {".class": "SymbolTable", "__await__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin.__await__", "name": "__await__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__await__ of _WrappedUnaryResponseMixin", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["P"], "typeddict_type": null}}, "_WrappedUnaryStreamCall": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedUnaryStreamCall", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.api_core.grpc_helpers_async._WrappedUnaryStreamCall", "name": "_WrappedUnaryStreamCall", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedUnaryStreamCall", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.api_core.grpc_helpers_async._WrappedUnaryStreamCall", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.api_core.grpc_helpers_async", "mro": ["google.api_core.grpc_helpers_async._WrappedUnaryStreamCall", "google.api_core.grpc_helpers_async._WrappedStreamResponseMixin", "google.api_core.grpc_helpers_async._WrappedCall", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async._WrappedUnaryStreamCall.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedUnaryStreamCall", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedUnaryStreamCall"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["P"], "typeddict_type": null}}, "_WrappedUnaryUnaryCall": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedUnaryUnaryCall", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.api_core.grpc_helpers_async._WrappedUnaryUnaryCall", "name": "_WrappedUnaryUnaryCall", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedUnaryUnaryCall", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.api_core.grpc_helpers_async._WrappedUnaryUnaryCall", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.api_core.grpc_helpers_async", "mro": ["google.api_core.grpc_helpers_async._WrappedUnaryUnaryCall", "google.api_core.grpc_helpers_async._WrappedUnaryResponseMixin", "google.api_core.grpc_helpers_async._WrappedCall", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async._WrappedUnaryUnaryCall.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.grpc_helpers_async.P", "id": 1, "name": "P", "namespace": "google.api_core.grpc_helpers_async._WrappedUnaryUnaryCall", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "google.api_core.grpc_helpers_async._WrappedUnaryUnaryCall"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["P"], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.grpc_helpers_async.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.grpc_helpers_async.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.grpc_helpers_async.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.grpc_helpers_async.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.grpc_helpers_async.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.grpc_helpers_async.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_wrap_stream_errors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["callable_", "wrapper_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.grpc_helpers_async._wrap_stream_errors", "name": "_wrap_stream_errors", "type": null}}, "_wrap_unary_errors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["callable_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.grpc_helpers_async._wrap_unary_errors", "name": "_wrap_unary_errors", "type": null}}, "aio": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.api_core.grpc_helpers_async.aio", "name": "aio", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.api_core.grpc_helpers_async.aio", "source_any": null, "type_of_any": 3}}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "create_channel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["target", "credentials", "scopes", "ssl_credentials", "credentials_file", "quota_project_id", "default_scopes", "default_host", "compression", "attempt_direct_path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.grpc_helpers_async.create_channel", "name": "create_channel", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["target", "credentials", "scopes", "ssl_credentials", "credentials_file", "quota_project_id", "default_scopes", "default_host", "compression", "attempt_direct_path", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_channel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exceptions": {".class": "SymbolTableNode", "cross_ref": "google.api_core.exceptions", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "grpc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.api_core.grpc_helpers_async.grpc", "name": "grpc", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.api_core.grpc_helpers_async.grpc", "source_any": null, "type_of_any": 3}}}, "grpc_helpers": {".class": "SymbolTableNode", "cross_ref": "google.api_core.grpc_helpers", "kind": "Gdef"}, "wrap_errors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["callable_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.grpc_helpers_async.wrap_errors", "name": "wrap_errors", "type": null}}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/google/api_core/grpc_helpers_async.py"}