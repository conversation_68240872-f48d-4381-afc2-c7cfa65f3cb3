{"data_mtime": 1752049734, "dep_lines": [21, 20, 21, 20, 1, 1, 1, 1], "dep_prios": [5, 10, 20, 20, 5, 30, 30, 30], "dependencies": ["google.api_core.gapic_v1.config", "google.api_core.retry_async", "google.api_core.gapic_v1", "google.api_core", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "22f444a286d06774f2af8927ba799d85bdfe0173", "id": "google.api_core.gapic_v1.config_async", "ignore_all": true, "interface_hash": "0e524a34d886693db2d6d3ed09f59fbf994356d5", "mtime": 1751921823, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/google/api_core/gapic_v1/config_async.py", "plugin_data": null, "size": 1728, "suppressed": [], "version_id": "1.16.1"}