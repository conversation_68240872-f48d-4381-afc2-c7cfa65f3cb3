{"data_mtime": 1752049734, "dep_lines": [25, 24, 25, 26, 24, 21, 22, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 5, 20, 10, 10, 5, 30, 30, 30, 30], "dependencies": ["google.api_core.gapic_v1.client_info", "google.api_core.grpc_helpers", "google.api_core.gapic_v1", "google.api_core.timeout", "google.api_core", "enum", "functools", "builtins", "_frozen_importlib", "abc", "google.api_core.client_info", "typing"], "hash": "6584434fd5cd41532166a419f69fdf0d87335c50", "id": "google.api_core.gapic_v1.method", "ignore_all": true, "interface_hash": "52b31b746365c36e6ad66f3eaffbcc08346fc4dc", "mtime": 1751921823, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/google/api_core/gapic_v1/method.py", "plugin_data": null, "size": 9494, "suppressed": [], "version_id": "1.16.1"}