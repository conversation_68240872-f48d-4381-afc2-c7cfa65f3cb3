{".class": "MypyFile", "_fullname": "google.api_core.gapic_v1.method_async", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DEFAULT": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1.method.DEFAULT", "kind": "Gdef"}, "USE_DEFAULT_METADATA": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1.method.USE_DEFAULT_METADATA", "kind": "Gdef"}, "_DEFAULT_ASYNC_TRANSPORT_KIND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.api_core.gapic_v1.method_async._DEFAULT_ASYNC_TRANSPORT_KIND", "name": "_DEFAULT_ASYNC_TRANSPORT_KIND", "setter_type": null, "type": "builtins.str"}}, "_GapicCallable": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1.method._GapicCallable", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.gapic_v1.method_async.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.gapic_v1.method_async.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.gapic_v1.method_async.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.gapic_v1.method_async.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.gapic_v1.method_async.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.gapic_v1.method_async.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "client_info": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1.client_info", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "grpc_helpers_async": {".class": "SymbolTableNode", "cross_ref": "google.api_core.grpc_helpers_async", "kind": "Gdef"}, "wrap_method": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["func", "default_retry", "default_timeout", "default_compression", "client_info", "kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.gapic_v1.method_async.wrap_method", "name": "wrap_method", "type": null}}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/google/api_core/gapic_v1/method_async.py"}