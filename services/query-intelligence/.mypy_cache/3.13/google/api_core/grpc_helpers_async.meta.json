{"data_mtime": 1752049734, "dep_lines": [29, 29, 29, 21, 22, 24, 1, 1, 1, 1, 1, 26], "dep_prios": [10, 10, 20, 10, 10, 5, 5, 30, 30, 30, 30, 5], "dependencies": ["google.api_core.exceptions", "google.api_core.grpc_helpers", "google.api_core", "asyncio", "functools", "typing", "builtins", "_asyncio", "_frozen_importlib", "abc", "asyncio.events"], "hash": "bed6f3399fba43cfe3082735a004c769dd1522fd", "id": "google.api_core.grpc_helpers_async", "ignore_all": true, "interface_hash": "86f6a5106d88ee232fade2df61cfa1cacedf54bd", "mtime": 1751921823, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/google/api_core/grpc_helpers_async.py", "plugin_data": null, "size": 12966, "suppressed": ["grpc"], "version_id": "1.16.1"}