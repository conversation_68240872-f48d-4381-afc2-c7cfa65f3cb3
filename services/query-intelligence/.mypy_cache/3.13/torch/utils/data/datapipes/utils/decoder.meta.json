{"data_mtime": 1752049734, "dep_lines": [12, 7, 190, 5, 6, 7, 8, 9, 11, 182, 190, 1, 1, 1, 1, 286, 286], "dep_prios": [5, 10, 20, 10, 10, 20, 10, 10, 10, 20, 20, 5, 30, 30, 30, 20, 20], "dependencies": ["torch.utils.data.datapipes.utils.common", "os.path", "PIL.Image", "io", "json", "os", "pickle", "tempfile", "torch", "numpy", "PIL", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "03c1095f641c8b6b0e1f791d656ae146e709ea9c", "id": "torch.utils.data.datapipes.utils.decoder", "ignore_all": true, "interface_hash": "fcbe83948a6f85dc670f2c91682a1923f0ebcf83", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/torch/utils/data/datapipes/utils/decoder.py", "plugin_data": null, "size": 11995, "suppressed": ["scipy.io", "scipy"], "version_id": "1.16.1"}