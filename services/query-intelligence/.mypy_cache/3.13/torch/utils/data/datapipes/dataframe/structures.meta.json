{"data_mtime": 1752049734, "dep_lines": [4, 4, 5, 1, 2, 1, 1, 1], "dep_prios": [10, 20, 5, 5, 5, 5, 30, 30], "dependencies": ["torch.utils.data.datapipes.dataframe.dataframe_wrapper", "torch.utils.data.datapipes.dataframe", "torch.utils.data.datapipes.datapipe", "collections.abc", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "d636c53c9e85c91796386ac595ad79c0e52bc6f8", "id": "torch.utils.data.datapipes.dataframe.structures", "ignore_all": true, "interface_hash": "60d91afa3934c58728cbf8e9bebf4d1df0f70826", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/torch/utils/data/datapipes/dataframe/structures.py", "plugin_data": null, "size": 662, "suppressed": [], "version_id": "1.16.1"}