{"data_mtime": 1752049734, "dep_lines": [19, 12, 13, 17, 20, 22, 11, 15, 16, 18, 21, 10, 14, 21, 2, 3, 4, 5, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 9], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 10, 5, 20, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["torch.fx.passes.infra.pass_base", "torch._export.pass_infra.node_metadata", "torch._export.pass_infra.proxy_value", "torch.fx.experimental.proxy_tensor", "torch.fx.passes.shape_prop", "torch.fx.experimental.symbolic_shapes", "torch._dispatch.python", "torch._subclasses.fake_tensor", "torch.fx.traceback", "torch.fx.graph", "torch.utils._pytree", "torch.fx", "torch._subclasses", "torch.utils", "operator", "traceback", "typing", "contextlib", "torch", "builtins", "_frozen_importlib", "_typeshed", "abc", "torch._C", "torch._export.pass_infra", "torch._tensor", "torch.fx._symbolic_trace", "torch.fx.experimental", "torch.fx.graph_module", "torch.fx.interpreter", "torch.fx.node", "torch.fx.passes", "torch.fx.passes.infra", "torch.fx.proxy", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.utils._python_dispatch", "types"], "hash": "f13a92a22974b72dd7e45242777f8276278120c7", "id": "torch._export.pass_base", "ignore_all": true, "interface_hash": "bf5386d352a95ffe8e8cbaaefde82adc3d6b8dd9", "mtime": 1751921840, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/torch/_export/pass_base.py", "plugin_data": null, "size": 17672, "suppressed": ["functorch.experimental.control_flow"], "version_id": "1.16.1"}