{".class": "MypyFile", "_fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "DynamicRendezvousHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.elastic.rendezvous.api.RendezvousHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler", "name": "DynamicRendezvousHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "mro": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler", "torch.distributed.elastic.rendezvous.api.RendezvousHandler", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "node", "settings", "backend_name", "store", "state_holder"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "node", "settings", "backend_name", "store", "state_holder"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler", "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc", "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings", "builtins.str", "torch._C._distributed_c10d.Store", "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of DynamicRendezvousHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_backend_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._backend_name", "name": "_backend_name", "setter_type": null, "type": "builtins.str"}}, "_bootstrap_store_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._bootstrap_store_info", "name": "_bootstrap_store_info", "setter_type": null, "type": {".class": "UnionType", "items": ["torch.distributed.elastic.rendezvous.api.RendezvousStoreInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._close", "name": "_close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_close of DynamicRendezvousHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_tcp_store_server": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "master_addr", "master_port"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._create_tcp_store_server", "name": "_create_tcp_store_server", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "master_addr", "master_port"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_create_tcp_store_server of DynamicRendezvousHandler", "ret_type": "torch._C._distributed_c10d.TCPStore", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_deadline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._get_deadline", "name": "_get_deadline", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "timeout"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler", "datetime.<PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_deadline of DynamicRendezvousHandler", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_store": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._get_store", "name": "_get_store", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_store of DynamicRendezvousHandler", "ret_type": "torch._C._distributed_c10d.Store", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_world": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._get_world", "name": "_get_world", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_world of DynamicRendezvousHandler", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_heartbeat_lock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._heartbeat_lock", "name": "_heartbeat_lock", "setter_type": null, "type": "_thread.lock"}}, "_keep_alive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._keep_alive", "name": "_keep_alive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_keep_alive of DynamicRendezvousHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_keep_alive_timer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._keep_alive_timer", "name": "_keep_alive_timer", "setter_type": null, "type": {".class": "UnionType", "items": ["torch.distributed.elastic.rendezvous.utils._PeriodicTimer", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_keep_alive_weak": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["weak_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._keep_alive_weak", "name": "_keep_alive_weak", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["weak_self"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_keep_alive_weak of DynamicRendezvousHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._keep_alive_weak", "name": "_keep_alive_weak", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["weak_self"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_keep_alive_weak of DynamicRendezvousHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_node_desc_generator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._node_desc_generator", "name": "_node_desc_generator", "setter_type": null, "type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDescGenerator"}}, "_op_executor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._op_executor", "name": "_op_executor", "setter_type": null, "type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousOpExecutor"}}, "_record": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "message", "node_state", "rank"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._record", "name": "_record", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "message", "node_state", "rank"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler", "builtins.str", "torch.distributed.elastic.events.api.NodeState", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_record of DynamicRendezvousHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._settings", "name": "_settings", "setter_type": null, "type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings"}}, "_shared_tcp_store_server": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._shared_tcp_store_server", "name": "_shared_tcp_store_server", "setter_type": null, "type": {".class": "UnionType", "items": ["torch._C._distributed_c10d.Store", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_start_heartbeats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._start_heartbeats", "name": "_start_heartbeats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_start_heartbeats of DynamicRendezvousHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_state_holder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._state_holder", "name": "_state_holder", "setter_type": null, "type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder"}}, "_stop_heartbeats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._stop_heartbeats", "name": "_stop_heartbeats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_stop_heartbeats of DynamicRendezvousHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_store": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._store", "name": "_store", "setter_type": null, "type": "torch._C._distributed_c10d.Store"}}, "_this_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._this_node", "name": "_this_node", "setter_type": null, "type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc"}}, "_wrap_store": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "store"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler._wrap_store", "name": "_wrap_store", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "store"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler", "torch._C._distributed_c10d.Store"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_wrap_store of DynamicRendezvousHandler", "ret_type": "torch._C._distributed_c10d.Store", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "run_id", "store", "backend", "min_nodes", "max_nodes", "local_addr", "timeout", "keep_alive_interval", "keep_alive_max_attempt"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler.from_backend", "name": "from_backend", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "run_id", "store", "backend", "min_nodes", "max_nodes", "local_addr", "timeout", "keep_alive_interval", "keep_alive_max_attempt"], "arg_types": [{".class": "TypeType", "item": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"}, "builtins.str", "torch._C._distributed_c10d.Store", "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_backend of DynamicRendezvousHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler.from_backend", "name": "from_backend", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1], "arg_names": ["cls", "run_id", "store", "backend", "min_nodes", "max_nodes", "local_addr", "timeout", "keep_alive_interval", "keep_alive_max_attempt"], "arg_types": [{".class": "TypeType", "item": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"}, "builtins.str", "torch._C._distributed_c10d.Store", "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_backend of DynamicRendezvousHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler.get_backend", "name": "get_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_backend of DynamicRendezvousHandler", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_run_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler.get_run_id", "name": "get_run_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_run_id of DynamicRendezvousHandler", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler.is_closed", "name": "is_closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_closed of DynamicRendezvousHandler", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "next_rendezvous": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler.next_rendezvous", "name": "next_rendezvous", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "next_rendezvous of DynamicRendezvousHandler", "ret_type": "torch.distributed.elastic.rendezvous.api.RendezvousInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "num_nodes_waiting": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler.num_nodes_waiting", "name": "num_nodes_waiting", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "num_nodes_waiting of DynamicRendezvousHandler", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler.set_closed", "name": "set_closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_closed of DynamicRendezvousHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler.settings", "name": "settings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "settings of DynamicRendezvousHandler", "ret_type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler.settings", "name": "settings", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "settings of DynamicRendezvousHandler", "ret_type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "shutdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler.shutdown", "name": "shutdown", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "shutdown of DynamicRendezvousHandler", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "use_agent_store": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler.use_agent_store", "name": "use_agent_store", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "use_agent_store of DynamicRendezvousHandler", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler.use_agent_store", "name": "use_agent_store", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "use_agent_store of DynamicRendezvousHandler", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef", "module_public": false}, "NodeState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.events.api.NodeState", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "RendezvousBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_state", 1], ["name", 1], ["set_state", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend", "name": "RendezvousBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "mro": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "get_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend.get_state", "name": "get_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_state of RendezvousBackend", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend.get_state", "name": "get_state", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_state of RendezvousBackend", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "name of RendezvousBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend.name", "name": "name", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "name of RendezvousBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 1], "arg_names": ["self", "state", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend.set_state", "name": "set_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "state", "token"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend", "builtins.bytes", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.Token"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_state of RendezvousBackend", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.Token"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend.set_state", "name": "set_state", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "state", "token"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend", "builtins.bytes", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.Token"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_state of RendezvousBackend", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.Token"}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RendezvousClosedError": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.rendezvous.api.RendezvousClosedError", "kind": "Gdef", "module_public": false}, "RendezvousError": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.rendezvous.api.RendezvousError", "kind": "Gdef", "module_public": false}, "RendezvousGracefulExitError": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.rendezvous.api.RendezvousGracefulExitError", "kind": "Gdef", "module_public": false}, "RendezvousHandler": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.rendezvous.api.RendezvousHandler", "kind": "Gdef", "module_public": false}, "RendezvousInfo": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.rendezvous.api.RendezvousInfo", "kind": "Gdef", "module_public": false}, "RendezvousParameters": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.rendezvous.api.RendezvousParameters", "kind": "Gdef", "module_public": false}, "RendezvousSettings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings", "name": "RendezvousSettings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 215, "name": "run_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 216, "name": "min_nodes", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 217, "name": "max_nodes", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 218, "name": "timeout", "type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 219, "name": "keep_alive_interval", "type": "datetime.<PERSON><PERSON><PERSON>"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 220, "name": "keep_alive_max_attempt", "type": "builtins.int"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "mro": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "run_id", "min_nodes", "max_nodes", "timeout", "keep_alive_interval", "keep_alive_max_attempt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "run_id", "min_nodes", "max_nodes", "timeout", "keep_alive_interval", "keep_alive_max_attempt"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings", "builtins.str", "builtins.int", "builtins.int", "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout", "datetime.<PERSON><PERSON><PERSON>", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RendezvousSettings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "run_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "min_nodes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max_nodes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timeout"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keep_alive_interval"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keep_alive_max_attempt"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["run_id", "min_nodes", "max_nodes", "timeout", "keep_alive_interval", "keep_alive_max_attempt"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["run_id", "min_nodes", "max_nodes", "timeout", "keep_alive_interval", "keep_alive_max_attempt"], "arg_types": ["builtins.str", "builtins.int", "builtins.int", "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout", "datetime.<PERSON><PERSON><PERSON>", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of RendezvousSettings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["run_id", "min_nodes", "max_nodes", "timeout", "keep_alive_interval", "keep_alive_max_attempt"], "arg_types": ["builtins.str", "builtins.int", "builtins.int", "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout", "datetime.<PERSON><PERSON><PERSON>", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of RendezvousSettings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__replace__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "run_id", "min_nodes", "max_nodes", "timeout", "keep_alive_interval", "keep_alive_max_attempt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings.__replace__", "name": "__replace__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "run_id", "min_nodes", "max_nodes", "timeout", "keep_alive_interval", "keep_alive_max_attempt"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings", "builtins.str", "builtins.int", "builtins.int", "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout", "datetime.<PERSON><PERSON><PERSON>", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__replace__ of RendezvousSettings", "ret_type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "keep_alive_interval": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings.keep_alive_interval", "name": "keep_alive_interval", "setter_type": null, "type": "datetime.<PERSON><PERSON><PERSON>"}}, "keep_alive_max_attempt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings.keep_alive_max_attempt", "name": "keep_alive_max_attempt", "setter_type": null, "type": "builtins.int"}}, "max_nodes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings.max_nodes", "name": "max_nodes", "setter_type": null, "type": "builtins.int"}}, "min_nodes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings.min_nodes", "name": "min_nodes", "setter_type": null, "type": "builtins.int"}}, "run_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings.run_id", "name": "run_id", "setter_type": null, "type": "builtins.str"}}, "timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings.timeout", "name": "timeout", "setter_type": null, "type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RendezvousStateError": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.rendezvous.api.RendezvousStateError", "kind": "Gdef", "module_public": false}, "RendezvousStoreInfo": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.rendezvous.api.RendezvousStoreInfo", "kind": "Gdef", "module_public": false}, "RendezvousTimeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout", "name": "RendezvousTimeout", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "mro": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout", "builtins.object"], "names": {".class": "SymbolTable", "_DEFAULT_TIMEOUTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout._DEFAULT_TIMEOUTS", "name": "_DEFAULT_TIMEOUTS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "datetime.<PERSON><PERSON><PERSON>"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_ZERO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout._ZERO", "name": "_ZERO", "setter_type": null, "type": "datetime.<PERSON><PERSON><PERSON>"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "join", "last_call", "close", "heartbeat"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "join", "last_call", "close", "heartbeat"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout", {".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RendezvousTimeout", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout._close", "name": "_close", "setter_type": null, "type": "datetime.<PERSON><PERSON><PERSON>"}}, "_heartbeat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout._heartbeat", "name": "_heartbeat", "setter_type": null, "type": "datetime.<PERSON><PERSON><PERSON>"}}, "_join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout._join", "name": "_join", "setter_type": null, "type": "datetime.<PERSON><PERSON><PERSON>"}}, "_last_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout._last_call", "name": "_last_call", "setter_type": null, "type": "datetime.<PERSON><PERSON><PERSON>"}}, "_set_timeouts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "timeouts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout._set_timeouts", "name": "_set_timeouts", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "timeouts"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout", {".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_set_timeouts of RendezvousTimeout", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of RendezvousTimeout", "ret_type": "datetime.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout.close", "name": "close", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of RendezvousTimeout", "ret_type": "datetime.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "heartbeat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout.heartbeat", "name": "heartbeat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "heartbeat of RendezvousTimeout", "ret_type": "datetime.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout.heartbeat", "name": "heartbeat", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "heartbeat of RendezvousTimeout", "ret_type": "datetime.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout.join", "name": "join", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "join of RendezvousTimeout", "ret_type": "datetime.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout.join", "name": "join", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "join of RendezvousTimeout", "ret_type": "datetime.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "last_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout.last_call", "name": "last_call", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "last_call of RendezvousTimeout", "ret_type": "datetime.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout.last_call", "name": "last_call", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "last_call of RendezvousTimeout", "ret_type": "datetime.<PERSON><PERSON><PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousTimeout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RendezvousTimeoutError": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.rendezvous.api.RendezvousTimeoutError", "kind": "Gdef", "module_public": false}, "Store": {".class": "SymbolTableNode", "cross_ref": "torch._C._distributed_c10d.Store", "kind": "Gdef", "module_public": false}, "Token": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.Token", "line": 57, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "_Action": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action", "name": "_Action", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "mro": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "ADD_TO_PARTICIPANTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action.ADD_TO_PARTICIPANTS", "name": "ADD_TO_PARTICIPANTS", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "ADD_TO_REDUNDANCY_LIST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action.ADD_TO_REDUNDANCY_LIST", "name": "ADD_TO_REDUNDANCY_LIST", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "ADD_TO_WAIT_LIST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action.ADD_TO_WAIT_LIST", "name": "ADD_TO_WAIT_LIST", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "ERROR_CLOSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action.ERROR_CLOSED", "name": "ERROR_CLOSED", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 11}, "type_ref": "builtins.int"}}}, "ERROR_TIMEOUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action.ERROR_TIMEOUT", "name": "ERROR_TIMEOUT", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 12}, "type_ref": "builtins.int"}}}, "FINISH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action.FINISH", "name": "FINISH", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 13}, "type_ref": "builtins.int"}}}, "KEEP_ALIVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action.KEEP_ALIVE", "name": "KEEP_ALIVE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "MARK_RENDEZVOUS_CLOSED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action.MARK_RENDEZVOUS_CLOSED", "name": "MARK_RENDEZVOUS_CLOSED", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 9}, "type_ref": "builtins.int"}}}, "MARK_RENDEZVOUS_COMPLETE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action.MARK_RENDEZVOUS_COMPLETE", "name": "MARK_RENDEZVOUS_COMPLETE", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "REMOVE_FROM_PARTICIPANTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action.REMOVE_FROM_PARTICIPANTS", "name": "REMOVE_FROM_PARTICIPANTS", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "REMOVE_FROM_REDUNDANCY_LIST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action.REMOVE_FROM_REDUNDANCY_LIST", "name": "REMOVE_FROM_REDUNDANCY_LIST", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 7}, "type_ref": "builtins.int"}}}, "REMOVE_FROM_WAIT_LIST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action.REMOVE_FROM_WAIT_LIST", "name": "REMOVE_FROM_WAIT_LIST", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, "type_ref": "builtins.int"}}}, "SYNC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action.SYNC", "name": "SYNC", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 10}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BackendRendezvousStateHolder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder", "name": "_BackendRendezvousStateHolder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "mro": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder", "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "backend", "settings", "cache_duration"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "backend", "settings", "cache_duration"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder", "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend", "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _BackendRendezvousStateHolder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder._backend", "name": "_backend", "setter_type": null, "type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend"}}, "_cache_duration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder._cache_duration", "name": "_cache_duration", "setter_type": null, "type": "builtins.int"}}, "_dead_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder._dead_nodes", "name": "_dead_nodes", "setter_type": null, "type": {".class": "Instance", "args": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_dirty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder._dirty", "name": "_dirty", "setter_type": null, "type": "builtins.bool"}}, "_last_sync_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder._last_sync_time", "name": "_last_sync_time", "setter_type": null, "type": "builtins.float"}}, "_record": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "message", "node_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder._record", "name": "_record", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "message", "node_state"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder", "builtins.str", "torch.distributed.elastic.events.api.NodeState"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_record of _BackendRendezvousStateHolder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sanitize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder._sanitize", "name": "_sanitize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_sanitize of _BackendRendezvousStateHolder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder._settings", "name": "_settings", "setter_type": null, "type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings"}}, "_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder._state", "name": "_state", "setter_type": null, "type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState"}}, "_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder._token", "name": "_token", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.Token"}}}, "mark_dirty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder.mark_dirty", "name": "mark_dirty", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mark_dirty of _BackendRendezvousStateHolder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder.state", "name": "state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "state of _BackendRendezvousStateHolder", "ret_type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder.state", "name": "state", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "state of _BackendRendezvousStateHolder", "ret_type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sync": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder.sync", "name": "sync", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sync of _BackendRendezvousStateHolder", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._BackendRendezvousStateHolder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DistributedRendezvousOpExecutor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousOpExecutor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor", "name": "_DistributedRendezvousOpExecutor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "mro": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor", "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousOpExecutor", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "state_holder", "settings"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "state_holder", "settings"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor", "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc", "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder", "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _DistributedRendezvousOpExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_to_participants": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor._add_to_participants", "name": "_add_to_participants", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_add_to_participants of _DistributedRendezvousOpExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_to_redundancy_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor._add_to_redundancy_list", "name": "_add_to_redundancy_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_add_to_redundancy_list of _DistributedRendezvousOpExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_to_wait_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor._add_to_wait_list", "name": "_add_to_wait_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_add_to_wait_list of _DistributedRendezvousOpExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_keep_alive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor._keep_alive", "name": "_keep_alive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_keep_alive of _DistributedRendezvousOpExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mark_rendezvous_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor._mark_rendezvous_closed", "name": "_mark_rendezvous_closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_mark_rendezvous_closed of _DistributedRendezvousOpExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mark_rendezvous_complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor._mark_rendezvous_complete", "name": "_mark_rendezvous_complete", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_mark_rendezvous_complete of _DistributedRendezvousOpExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor._node", "name": "_node", "setter_type": null, "type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc"}}, "_record": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "message", "node_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor._record", "name": "_record", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "message", "node_state"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor", "builtins.str", "torch.distributed.elastic.events.api.NodeState"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_record of _DistributedRendezvousOpExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_remove_from_participants": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor._remove_from_participants", "name": "_remove_from_participants", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_remove_from_participants of _DistributedRendezvousOpExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_remove_from_redundancy_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor._remove_from_redundancy_list", "name": "_remove_from_redundancy_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_remove_from_redundancy_list of _DistributedRendezvousOpExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_remove_from_wait_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor._remove_from_wait_list", "name": "_remove_from_wait_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_remove_from_wait_list of _DistributedRendezvousOpExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor._settings", "name": "_settings", "setter_type": null, "type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings"}}, "_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor._state", "name": "_state", "setter_type": null, "type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState"}}, "_state_holder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor._state_holder", "name": "_state_holder", "setter_type": null, "type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder"}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "state_handler", "deadline", "update_deadline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "state_handler", "deadline", "update_deadline"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.float", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["datetime.<PERSON><PERSON><PERSON>"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "run of _DistributedRendezvousOpExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._DistributedRendezvousOpExecutor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_NodeDesc": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc", "name": "_NodeDesc", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 236, "name": "addr", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 237, "name": "pid", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 238, "name": "local_id", "type": "builtins.int"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "mro": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc", "builtins.object"], "names": {".class": "SymbolTable", "_DT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc._DT", "name": "_DT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc._DT", "id": -1, "name": "_DT", "namespace": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc._DT", "id": -1, "name": "_DT", "namespace": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__ge__ of _NodeDesc", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc._DT", "id": -1, "name": "_DT", "namespace": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc._DT", "id": -1, "name": "_DT", "namespace": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc._DT", "id": -1, "name": "_DT", "namespace": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__gt__ of _NodeDesc", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc._DT", "id": -1, "name": "_DT", "namespace": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "addr", "pid", "local_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "addr", "pid", "local_id"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc", "builtins.str", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _NodeDesc", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc._DT", "id": -1, "name": "_DT", "namespace": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc._DT", "id": -1, "name": "_DT", "namespace": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__le__ of _NodeDesc", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc._DT", "id": -1, "name": "_DT", "namespace": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc._DT", "id": -1, "name": "_DT", "namespace": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc._DT", "id": -1, "name": "_DT", "namespace": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__lt__ of _NodeDesc", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc._DT", "id": -1, "name": "_DT", "namespace": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "addr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "local_id"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["addr", "pid", "local_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["addr", "pid", "local_id"], "arg_types": ["builtins.str", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of _NodeDesc", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["addr", "pid", "local_id"], "arg_types": ["builtins.str", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of _NodeDesc", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__replace__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "addr", "pid", "local_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__replace__", "name": "__replace__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["self", "addr", "pid", "local_id"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc", "builtins.str", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__replace__ of _NodeDesc", "ret_type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of _NodeDesc", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "addr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.addr", "name": "addr", "setter_type": null, "type": "builtins.str"}}, "local_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.local_id", "name": "local_id", "setter_type": null, "type": "builtins.int"}}, "pid": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.pid", "name": "pid", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_NodeDescGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDescGenerator", "name": "_NodeDescGenerator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDescGenerator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "mro": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDescGenerator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDescGenerator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDescGenerator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _NodeDescGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_local_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDescGenerator._local_id", "name": "_local_id", "setter_type": null, "type": "builtins.int"}}, "_lock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDescGenerator._lock", "name": "_lock", "setter_type": null, "type": "_thread.lock"}}, "generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "local_addr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDescGenerator.generate", "name": "generate", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "local_addr"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDescGenerator", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate of _NodeDescGenerator", "ret_type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDescGenerator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDescGenerator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_PeriodicTimer": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.rendezvous.utils._PeriodicTimer", "kind": "Gdef", "module_public": false}, "_RendezvousCloseOp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousCloseOp", "name": "_RendezvousCloseOp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousCloseOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "mro": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousCloseOp", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ctx", "deadline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousCloseOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ctx", "deadline"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousCloseOp", "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext", "builtins.float"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _RendezvousCloseOp", "ret_type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousCloseOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousCloseOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_RendezvousContext": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext", "name": "_RendezvousContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "mro": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "state", "settings"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "state", "settings"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext", "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc", "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState", "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _RendezvousContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "node": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext.node", "name": "node", "setter_type": null, "type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc"}}, "settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext.settings", "name": "settings", "setter_type": null, "type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings"}}, "state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext.state", "name": "state", "setter_type": null, "type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_RendezvousExitOp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousExitOp", "name": "_RendezvousExitOp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousExitOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "mro": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousExitOp", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ctx", "deadline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousExitOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ctx", "deadline"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousExitOp", "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext", "builtins.float"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _RendezvousExitOp", "ret_type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousExitOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousExitOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_RendezvousJoinOp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousJoinOp", "name": "_RendezvousJoinOp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousJoinOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "mro": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousJoinOp", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ctx", "deadline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousJoinOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ctx", "deadline"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousJoinOp", "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext", "builtins.float"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _RendezvousJoinOp", "ret_type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousJoinOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousJoinOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_RendezvousKeepAliveOp": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousKeepAliveOp", "name": "_RendezvousKeepAliveOp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousKeepAliveOp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "mro": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousKeepAliveOp", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ctx", "deadline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousKeepAliveOp.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ctx", "deadline"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousKeepAliveOp", "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext", "builtins.float"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _RendezvousKeepAliveOp", "ret_type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousKeepAliveOp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousKeepAliveOp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_RendezvousOpExecutor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["run", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousOpExecutor", "name": "_RendezvousOpExecutor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousOpExecutor", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "mro": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousOpExecutor", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "state_handler", "deadline", "update_deadline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousOpExecutor.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "state_handler", "deadline", "update_deadline"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousOpExecutor", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.float", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["datetime.<PERSON><PERSON><PERSON>"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "run of _RendezvousOpExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousOpExecutor.run", "name": "run", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "state_handler", "deadline", "update_deadline"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousOpExecutor", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._Action", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.float", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["datetime.<PERSON><PERSON><PERSON>"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "run of _RendezvousOpExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousOpExecutor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousOpExecutor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_RendezvousState": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState", "name": "_RendezvousState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "mro": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _RendezvousState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState.closed", "name": "closed", "setter_type": null, "type": "builtins.bool"}}, "complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState.complete", "name": "complete", "setter_type": null, "type": "builtins.bool"}}, "deadline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState.deadline", "name": "deadline", "setter_type": null, "type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "last_heartbeats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState.last_heartbeats", "name": "last_heartbeats", "setter_type": null, "type": {".class": "Instance", "args": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc", "datetime.datetime"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "participants": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState.participants", "name": "participants", "setter_type": null, "type": {".class": "Instance", "args": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "redundancy_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState.redundancy_list", "name": "redundancy_list", "setter_type": null, "type": {".class": "Instance", "args": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "round": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState.round", "name": "round", "setter_type": null, "type": "builtins.int"}}, "wait_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState.wait_list", "name": "wait_list", "setter_type": null, "type": {".class": "Instance", "args": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._NodeDesc"], "extra_attrs": null, "type_ref": "builtins.set"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_RendezvousStateHolder": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["mark_dirty", 1], ["state", 1], ["sync", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder", "name": "_RendezvousStateHolder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.elastic.rendezvous.dynamic_rendezvous", "mro": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "mark_dirty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder.mark_dirty", "name": "mark_dirty", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mark_dirty of _RendezvousStateHolder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder.mark_dirty", "name": "mark_dirty", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "mark_dirty of _RendezvousStateHolder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder.state", "name": "state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "state of _RendezvousStateHolder", "ret_type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder.state", "name": "state", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "state of _RendezvousStateHolder", "ret_type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sync": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body", "is_trivial_self"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder.sync", "name": "sync", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sync of _RendezvousStateHolder", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder.sync", "name": "sync", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sync of _RendezvousStateHolder", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousStateHolder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_delay": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.rendezvous.utils._delay", "kind": "Gdef", "module_public": false}, "_get_timeout": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["params", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._get_timeout", "name": "_get_timeout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["params", "key"], "arg_types": ["torch.distributed.elastic.rendezvous.api.RendezvousParameters", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_timeout", "ret_type": {".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_remove_participant_epilogue": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "settings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._remove_participant_epilogue", "name": "_remove_participant_epilogue", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state", "settings"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousState", "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousSettings"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_remove_participant_epilogue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_should_keep_alive": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ctx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous._should_keep_alive", "name": "_should_keep_alive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ctx"], "arg_types": ["torch.distributed.elastic.rendezvous.dynamic_rendezvous._RendezvousContext"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_should_keep_alive", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef", "module_public": false}, "construct_and_record_rdzv_event": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.events.construct_and_record_rdzv_event", "kind": "Gdef", "module_public": false}, "create_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["store", "backend", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.create_handler", "name": "create_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["store", "backend", "params"], "arg_types": ["torch._C._distributed_c10d.Store", "torch.distributed.elastic.rendezvous.dynamic_rendezvous.RendezvousBackend", "torch.distributed.elastic.rendezvous.api.RendezvousParameters"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_handler", "ret_type": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.DynamicRendezvousHandler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef", "module_public": false}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_public": false}, "dist": {".class": "SymbolTableNode", "cross_ref": "torch.distributed", "kind": "Gdef", "module_public": false}, "get_method_name": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["depth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.get_method_name", "name": "get_method_name", "type": null}}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.rendezvous.dynamic_rendezvous.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "pickle": {".class": "SymbolTableNode", "cross_ref": "pickle", "kind": "Gdef", "module_public": false}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef", "module_public": false}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef", "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_public": false}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef", "module_public": false}, "timezone": {".class": "SymbolTableNode", "cross_ref": "datetime.timezone", "kind": "Gdef", "module_public": false}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/torch/distributed/elastic/rendezvous/dynamic_rendezvous.py"}