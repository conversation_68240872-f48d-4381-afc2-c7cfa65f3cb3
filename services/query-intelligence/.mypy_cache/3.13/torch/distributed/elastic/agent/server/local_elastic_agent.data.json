{".class": "MypyFile", "_fullname": "torch.distributed.elastic.agent.server.local_elastic_agent", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "EventMetadataValue": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.events.api.EventMetadataValue", "kind": "Gdef", "module_public": false}, "HealthCheckServer": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.agent.server.health_check_server.HealthCheckServer", "kind": "Gdef", "module_public": false}, "LocalElasticAgent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.elastic.agent.server.api.SimpleElasticAgent"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent", "name": "LocalElasticAgent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.elastic.agent.server.local_elastic_agent", "mro": ["torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent", "torch.distributed.elastic.agent.server.api.SimpleElasticAgent", "torch.distributed.elastic.agent.server.api.ElasticAgent", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "spec", "logs_specs", "start_method", "exit_barrier_timeout", "log_line_prefix_template"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "spec", "logs_specs", "start_method", "exit_barrier_timeout", "log_line_prefix_template"], "arg_types": ["torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent", "torch.distributed.elastic.agent.server.api.WorkerSpec", "torch.distributed.elastic.multiprocessing.api.LogsSpecs", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.float", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of LocalElasticAgent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_current_time_secs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._get_current_time_secs", "name": "_get_current_time_secs", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_current_time_secs of LocalElasticAgent", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._get_current_time_secs", "name": "_get_current_time_secs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_current_time_secs of LocalElasticAgent", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_fq_hostname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._get_fq_hostname", "name": "_get_fq_hostname", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_fq_hostname of LocalElasticAgent", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_health_check_server": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._health_check_server", "name": "_health_check_server", "setter_type": null, "type": {".class": "UnionType", "items": ["torch.distributed.elastic.agent.server.health_check_server.HealthCheckServer", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_log_line_prefix_template": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._log_line_prefix_template", "name": "_log_line_prefix_template", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_log_watchdog_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._log_watchdog_event", "name": "_log_watchdog_event", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "request"], "arg_types": ["torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent", "builtins.str", {".class": "UnionType", "items": ["torch.distributed.elastic.timer.file_based_local_timer.FileTimerRequest", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_log_watchdog_event of LocalElasticAgent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_logs_specs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._logs_specs", "name": "_logs_specs", "setter_type": null, "type": "torch.distributed.elastic.multiprocessing.api.LogsSpecs"}}, "_monitor_workers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "worker_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._monitor_workers", "name": "_monitor_workers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "worker_group"], "arg_types": ["torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent", "torch.distributed.elastic.agent.server.api.WorkerGroup"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_monitor_workers of LocalElasticAgent", "ret_type": "torch.distributed.elastic.agent.server.api.RunResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._monitor_workers", "name": "_monitor_workers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "_pcontext": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._pcontext", "name": "_pcontext", "setter_type": null, "type": {".class": "UnionType", "items": ["torch.distributed.elastic.multiprocessing.api.PContext", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_rdzv_handler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._rdzv_handler", "name": "_rdzv_handler", "setter_type": null, "type": "torch.distributed.elastic.rendezvous.api.RendezvousHandler"}}, "_setup_healthcheck": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._setup_healthcheck", "name": "_setup_healthcheck", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_setup_healthcheck of LocalElasticAgent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_setup_local_watchdog": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "envs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._setup_local_watchdog", "name": "_setup_local_watchdog", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "envs"], "arg_types": ["torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent", {".class": "Instance", "args": ["builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_setup_local_watchdog of LocalElasticAgent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_shutdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "death_sig", "is_restart"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._shutdown", "name": "_shutdown", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "death_sig", "is_restart"], "arg_types": ["torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent", "signal.Signals", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_shutdown of LocalElasticAgent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_start_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._start_method", "name": "_start_method", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_start_workers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "worker_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._start_workers", "name": "_start_workers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "worker_group"], "arg_types": ["torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent", "torch.distributed.elastic.agent.server.api.WorkerGroup"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_start_workers of LocalElasticAgent", "ret_type": {".class": "Instance", "args": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._start_workers", "name": "_start_workers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "_stop_workers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "worker_group", "is_restart"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._stop_workers", "name": "_stop_workers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "worker_group", "is_restart"], "arg_types": ["torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent", "torch.distributed.elastic.agent.server.api.WorkerGroup", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_stop_workers of LocalElasticAgent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._stop_workers", "name": "_stop_workers", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "_worker_watchdog": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent._worker_watchdog", "name": "_worker_watchdog", "setter_type": null, "type": {".class": "UnionType", "items": ["torch.distributed.elastic.timer.file_based_local_timer.FileTimerServer", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.elastic.agent.server.local_elastic_agent.LocalElasticAgent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LogsSpecs": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.api.LogsSpecs", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PContext": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.api.PContext", "kind": "Gdef", "module_public": false}, "RunResult": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.agent.server.api.RunResult", "kind": "Gdef", "module_public": false}, "SimpleElasticAgent": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.agent.server.api.SimpleElasticAgent", "kind": "Gdef", "module_public": false}, "TORCHELASTIC_ENABLE_FILE_TIMER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.TORCHELASTIC_ENABLE_FILE_TIMER", "name": "TORCHELASTIC_ENABLE_FILE_TIMER", "setter_type": null, "type": "builtins.str"}}, "TORCHELASTIC_HEALTH_CHECK_PORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.TORCHELASTIC_HEALTH_CHECK_PORT", "name": "TORCHELASTIC_HEALTH_CHECK_PORT", "setter_type": null, "type": "builtins.str"}}, "TORCHELASTIC_TIMER_FILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.TORCHELASTIC_TIMER_FILE", "name": "TORCHELASTIC_TIMER_FILE", "setter_type": null, "type": "builtins.str"}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Template": {".class": "SymbolTableNode", "cross_ref": "string.Template", "kind": "Gdef", "module_public": false}, "WorkerGroup": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.agent.server.api.WorkerGroup", "kind": "Gdef", "module_public": false}, "WorkerSpec": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.agent.server.api.WorkerSpec", "kind": "Gdef", "module_public": false}, "WorkerState": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.agent.server.api.WorkerState", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "create_healthcheck_server": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.agent.server.health_check_server.create_healthcheck_server", "kind": "Gdef", "module_public": false}, "events": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.events", "kind": "Gdef", "module_public": false}, "get_logger": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.utils.logging.get_logger", "kind": "Gdef", "module_public": false}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.elastic.agent.server.local_elastic_agent.logger", "name": "logger", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "macros": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.utils.api.macros", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "prof": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.metrics.api.prof", "kind": "Gdef", "module_public": false}, "signal": {".class": "SymbolTableNode", "cross_ref": "signal", "kind": "Gdef", "module_public": false}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef", "module_public": false}, "start_processes": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.multiprocessing.start_processes", "kind": "Gdef", "module_public": false}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef", "module_public": false}, "timer": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.elastic.timer", "kind": "Gdef", "module_public": false}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/torch/distributed/elastic/agent/server/local_elastic_agent.py"}