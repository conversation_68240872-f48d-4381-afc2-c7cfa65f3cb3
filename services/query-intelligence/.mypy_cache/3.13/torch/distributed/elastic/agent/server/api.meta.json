{"data_mtime": 1752049735, "dep_lines": [24, 29, 23, 24, 25, 26, 27, 23, 23, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 20, 5, 5, 5, 20, 20, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.elastic.utils.store", "torch.distributed.elastic.utils.logging", "torch.distributed.elastic.rendezvous", "torch.distributed.elastic.utils", "torch.distributed.elastic.events", "torch.distributed.elastic.metrics", "torch.distributed.elastic.multiprocessing", "torch.distributed.elastic", "torch.distributed", "abc", "json", "os", "signal", "socket", "time", "traceback", "warnings", "collections", "contextlib", "dataclasses", "enum", "typing", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "_warnings", "torch._C", "torch._C._distributed_c10d", "torch.distributed.elastic.events.api", "torch.distributed.elastic.metrics.api", "torch.distributed.elastic.multiprocessing.api", "torch.distributed.elastic.multiprocessing.errors", "torch.distributed.elastic.rendezvous.api"], "hash": "809b8acec6426ef7186c1283ea3a2dacede690d5", "id": "torch.distributed.elastic.agent.server.api", "ignore_all": true, "interface_hash": "70a17620732ea98a56af9f10893b46ccf6f3a8a3", "mtime": 1751921840, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/torch/distributed/elastic/agent/server/api.py", "plugin_data": null, "size": 36945, "suppressed": [], "version_id": "1.16.1"}