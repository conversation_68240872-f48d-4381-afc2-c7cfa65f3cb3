{"data_mtime": 1752049735, "dep_lines": [10, 11, 12, 14, 16, 17, 18, 9, 3, 4, 5, 6, 8, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["torch.distributed.checkpoint.default_planner", "torch.distributed.checkpoint.logger", "torch.distributed.checkpoint.stateful", "torch.distributed.checkpoint._storage_utils", "torch.distributed.checkpoint.planner", "torch.distributed.checkpoint.storage", "torch.distributed.checkpoint.utils", "torch.distributed", "os", "warnings", "typing", "typing_extensions", "torch", "builtins", "_frozen_importlib", "abc", "torch._C", "torch._C._distributed_c10d"], "hash": "585f541b2aeb099896bc296a73cd9335af69f068", "id": "torch.distributed.checkpoint.state_dict_loader", "ignore_all": true, "interface_hash": "985ac9313c23d3daec0edc72bafdc644ee2e14a2", "mtime": 1751921840, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/torch/distributed/checkpoint/state_dict_loader.py", "plugin_data": null, "size": 12438, "suppressed": [], "version_id": "1.16.1"}