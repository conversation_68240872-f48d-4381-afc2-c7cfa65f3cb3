{".class": "MypyFile", "_fullname": "torch.distributed.checkpoint._traverse", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CONTAINER_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.distributed.checkpoint._traverse.CONTAINER_TYPE", "line": 16, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.checkpoint._traverse.PATH_ITEM"}, "builtins.object"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef", "module_public": false}, "DTensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor._api.DTensor", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef", "module_public": false}, "OBJ_PATH": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.distributed.checkpoint._traverse.OBJ_PATH", "line": 12, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.checkpoint._traverse.PATH_ITEM"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "PATH_ITEM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.distributed.checkpoint._traverse.PATH_ITEM", "line": 11, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}}}, "STATE_DICT_ITEM": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.distributed.checkpoint._traverse.STATE_DICT_ITEM", "line": 15, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.object"}}, "STATE_DICT_TYPE": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE", "kind": "Gdef", "module_public": false}, "ShardedTensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._shard.sharded_tensor.api.ShardedTensor", "kind": "Gdef", "module_public": false}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint._traverse.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.checkpoint._traverse.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._traverse.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._traverse.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._traverse.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._traverse.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._traverse.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint._traverse.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_keep_visiting_tensors": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._traverse._keep_visiting_tensors", "name": "_keep_visiting_tensors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.object"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_keep_visiting_tensors", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_print_nested": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["value", "prefix", "print_fun"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._traverse._print_nested", "name": "_print_nested", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["value", "prefix", "print_fun"], "arg_types": ["builtins.object", "builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_print_nested", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "get_element": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["root_dict", "path", "default_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._traverse.get_element", "name": "get_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["root_dict", "path", "default_value"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.checkpoint._traverse.OBJ_PATH"}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint._traverse.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint._traverse.get_element", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_element", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint._traverse.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint._traverse.get_element", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint._traverse.T", "id": -1, "name": "T", "namespace": "torch.distributed.checkpoint._traverse.get_element", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "print_tensor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["path", "value", "print_fun"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._traverse.print_tensor", "name": "print_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["path", "value", "print_fun"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.checkpoint._traverse.OBJ_PATH"}, "builtins.object", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "print_tensor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_element": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["root_dict", "path", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._traverse.set_element", "name": "set_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["root_dict", "path", "value"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.checkpoint._traverse.OBJ_PATH"}, "builtins.object"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_element", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "traverse_state_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["state_dict", "visitor", "keep_traversing"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._traverse.traverse_state_dict", "name": "traverse_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["state_dict", "visitor", "keep_traversing"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.checkpoint._traverse.OBJ_PATH"}, "builtins.object"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.object"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "traverse_state_dict", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "traverse_state_dict_v_2_3": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["state_dict", "visitor", "keep_traversing"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint._traverse.traverse_state_dict_v_2_3", "name": "traverse_state_dict_v_2_3", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["state_dict", "visitor", "keep_traversing"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.checkpoint._traverse.OBJ_PATH"}, "builtins.object"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.object"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "traverse_state_dict_v_2_3", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/torch/distributed/checkpoint/_traverse.py"}