{"data_mtime": 1752049734, "dep_lines": [26, 27, 31, 32, 42, 43, 48, 13, 25, 49, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 17, 18, 21, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed._shard._utils", "torch.distributed.checkpoint._extension", "torch.distributed.checkpoint.metadata", "torch.distributed.checkpoint.planner", "torch.distributed.checkpoint.staging", "torch.distributed.checkpoint.storage", "torch.distributed.checkpoint.utils", "collections.abc", "torch._utils", "torch.futures", "collections", "dataclasses", "io", "operator", "os", "pickle", "queue", "threading", "uuid", "warnings", "abc", "contextlib", "pathlib", "typing", "typing_extensions", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_pickle", "_typeshed", "functools", "torch._C", "torch._tensor", "torch.cuda", "torch.cuda.streams", "types"], "hash": "4a4a0ae62998960bfdad0ecbb95dfdb523edbe34", "id": "torch.distributed.checkpoint.filesystem", "ignore_all": true, "interface_hash": "e468ce2efd2919bb3b44fae6b5153484937c3d17", "mtime": 1751921840, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/torch/distributed/checkpoint/filesystem.py", "plugin_data": null, "size": 32526, "suppressed": [], "version_id": "1.16.1"}