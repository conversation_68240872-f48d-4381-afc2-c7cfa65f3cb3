{"data_mtime": 1752049734, "dep_lines": [9, 10, 11, 5, 8, 3, 4, 6, 8, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.checkpoint.metadata", "torch.distributed.checkpoint.planner", "torch.distributed.checkpoint.storage", "concurrent.futures", "torch.distributed", "abc", "os", "typing", "torch", "builtins", "_frozen_importlib", "concurrent", "concurrent.futures._base", "torch._C", "torch._C._distributed_c10d"], "hash": "eb0a631e5ca6285a8e8f5b5ee605e52f1cf5981a", "id": "torch.distributed.checkpoint._async_executor", "ignore_all": true, "interface_hash": "5c4921f538039450c30da8a60f51a4dfa50730bc", "mtime": 1751921840, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/torch/distributed/checkpoint/_async_executor.py", "plugin_data": null, "size": 1097, "suppressed": [], "version_id": "1.16.1"}