{"data_mtime": 1752049734, "dep_lines": [16, 7, 8, 11, 12, 9, 6, 3, 4, 6, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 5, 5, 10, 10, 5, 20, 5, 30, 30], "dependencies": ["torch.distributed._shard.sharded_tensor.metadata", "torch.distributed._shard.sharded_tensor", "torch.distributed.checkpoint.metadata", "torch.distributed.checkpoint._traverse", "torch.distributed.checkpoint.utils", "torch.distributed.remote_device", "torch.distributed", "copy", "typing", "torch", "builtins", "_frozen_importlib", "abc"], "hash": "4f608ec22438a60dbe699825e5b2da96a44cc347", "id": "torch.distributed.checkpoint._sharded_tensor_utils", "ignore_all": true, "interface_hash": "1287a0d90108791201344998b9caa7b8831736b1", "mtime": 1751921840, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/torch/distributed/checkpoint/_sharded_tensor_utils.py", "plugin_data": null, "size": 4144, "suppressed": [], "version_id": "1.16.1"}