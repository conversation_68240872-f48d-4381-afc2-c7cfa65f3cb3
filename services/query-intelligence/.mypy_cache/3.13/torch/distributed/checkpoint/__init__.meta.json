{"data_mtime": 1752049735, "dep_lines": [1, 2, 3, 4, 5, 6, 12, 13, 14, 15, 16, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["torch.distributed.checkpoint._extension", "torch.distributed.checkpoint._hf_storage", "torch.distributed.checkpoint.api", "torch.distributed.checkpoint.default_planner", "torch.distributed.checkpoint.filesystem", "torch.distributed.checkpoint.metadata", "torch.distributed.checkpoint.optimizer", "torch.distributed.checkpoint.planner", "torch.distributed.checkpoint.state_dict_loader", "torch.distributed.checkpoint.state_dict_saver", "torch.distributed.checkpoint.storage", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "b74ab7daf714171fa14dfd3e914b476ae4fedfae", "id": "torch.distributed.checkpoint", "ignore_all": true, "interface_hash": "f3ac5ba183f95a334965256abcb7909e8192f041", "mtime": 1751921840, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/torch/distributed/checkpoint/__init__.py", "plugin_data": null, "size": 697, "suppressed": [], "version_id": "1.16.1"}