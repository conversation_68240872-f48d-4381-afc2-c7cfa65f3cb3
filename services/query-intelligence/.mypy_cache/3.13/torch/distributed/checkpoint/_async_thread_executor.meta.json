{"data_mtime": 1752049735, "dep_lines": [8, 9, 10, 11, 27, 4, 7, 3, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.distributed.checkpoint._async_executor", "torch.distributed.checkpoint.metadata", "torch.distributed.checkpoint.planner", "torch.distributed.checkpoint.storage", "torch.distributed.checkpoint.state_dict_saver", "concurrent.futures", "torch.distributed", "os", "typing", "torch", "builtins", "_frozen_importlib", "abc", "concurrent", "concurrent.futures._base", "concurrent.futures.thread", "torch._C", "torch._C._distributed_c10d"], "hash": "dc75c7aceac4e719b757b2aa3a9c1177489753bb", "id": "torch.distributed.checkpoint._async_thread_executor", "ignore_all": true, "interface_hash": "01d1c035f448b944dcb0e6e59a3af1e0a3a78ec6", "mtime": 1751921840, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/torch/distributed/checkpoint/_async_thread_executor.py", "plugin_data": null, "size": 1365, "suppressed": [], "version_id": "1.16.1"}