{".class": "MypyFile", "_fullname": "torch.distributed.checkpoint.default_planner", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BytesStorageMetadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.BytesStorageMetadata", "kind": "Gdef", "module_public": false}, "ChainMap": {".class": "SymbolTableNode", "cross_ref": "collections.ChainMap", "kind": "Gdef", "module_public": false}, "ChunkStorageMetadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.ChunkStorageMetadata", "kind": "Gdef", "module_public": false}, "DTensor": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.tensor._api.DTensor", "kind": "Gdef", "module_public": false}, "DefaultLoadPlanner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.checkpoint.planner.LoadPlanner"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner", "name": "DefaultLoadP<PERSON>ner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.checkpoint.default_planner", "mro": ["torch.distributed.checkpoint.default_planner.DefaultLoadPlanner", "torch.distributed.checkpoint.planner.LoadPlanner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "flatten_state_dict", "flatten_sharded_tensors", "allow_partial_load"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "flatten_state_dict", "flatten_sharded_tensors", "allow_partial_load"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultLoadPlanner", "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of DefaultLoadPlanner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allow_partial_load": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.allow_partial_load", "name": "allow_partial_load", "setter_type": null, "type": "builtins.bool"}}, "commit_tensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "read_item", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.commit_tensor", "name": "commit_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "read_item", "tensor"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultLoadPlanner", "torch.distributed.checkpoint.planner.ReadItem", "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "commit_tensor of DefaultLoadPlanner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_global_plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "global_plan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.create_global_plan", "name": "create_global_plan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "global_plan"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultLoadPlanner", {".class": "Instance", "args": ["torch.distributed.checkpoint.planner.LoadPlan"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_global_plan of DefaultLoadPlanner", "ret_type": {".class": "Instance", "args": ["torch.distributed.checkpoint.planner.LoadPlan"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_local_plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.create_local_plan", "name": "create_local_plan", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultLoadPlanner"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_local_plan of DefaultLoadPlanner", "ret_type": "torch.distributed.checkpoint.planner.LoadPlan", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "finish_plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_plan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.finish_plan", "name": "finish_plan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_plan"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultLoadPlanner", "torch.distributed.checkpoint.planner.LoadPlan"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "finish_plan of DefaultLoadPlanner", "ret_type": "torch.distributed.checkpoint.planner.LoadPlan", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flatten_sharded_tensors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.flatten_sharded_tensors", "name": "flatten_sharded_tensors", "setter_type": null, "type": "builtins.bool"}}, "flatten_state_dict": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.flatten_state_dict", "name": "flatten_state_dict", "setter_type": null, "type": "builtins.bool"}}, "is_coordinator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.is_coordinator", "name": "is_coordinator", "setter_type": null, "type": "builtins.bool"}}, "load_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "read_item", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.load_bytes", "name": "load_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "read_item", "value"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultLoadPlanner", "torch.distributed.checkpoint.planner.ReadItem", "_io.BytesIO"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "load_bytes of DefaultLoadPlanner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lookup_tensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.lookup_tensor", "name": "lookup_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultLoadPlanner", "torch.distributed.checkpoint.metadata.MetadataIndex"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "lookup_tensor of DefaultLoadPlanner", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mappings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.mappings", "name": "mappings", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.checkpoint._nested_dict.FLATTEN_MAPPING"}}}, "metadata": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.metadata", "name": "metadata", "setter_type": null, "type": {".class": "UnionType", "items": ["torch.distributed.checkpoint.metadata.Metadata", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "original_state_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.original_state_dict", "name": "original_state_dict", "setter_type": null, "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}}}, "resolve_tensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "read_item"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.resolve_tensor", "name": "resolve_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "read_item"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultLoadPlanner", "torch.distributed.checkpoint.planner.ReadItem"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "resolve_tensor of DefaultLoadPlanner", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_up_planner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "state_dict", "metadata", "is_coordinator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.set_up_planner", "name": "set_up_planner", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "state_dict", "metadata", "is_coordinator"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultLoadPlanner", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.metadata.Metadata", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_up_planner of DefaultLoadPlanner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "state_dict": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.state_dict", "name": "state_dict", "setter_type": null, "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}}}, "transform_tensor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "read_item", "tensor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.transform_tensor", "name": "transform_tensor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "read_item", "tensor"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultLoadPlanner", "torch.distributed.checkpoint.planner.ReadItem", "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transform_tensor of DefaultLoadPlanner", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DefaultSavePlanner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.checkpoint.planner.SavePlanner"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner", "name": "DefaultSavePlanner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "torch.distributed.checkpoint.default_planner", "mro": ["torch.distributed.checkpoint.default_planner.DefaultSavePlanner", "torch.distributed.checkpoint.planner.SavePlanner", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "flatten_state_dict", "flatten_sharded_tensors", "dedup_replicated_tensors", "dedup_save_to_lowest_rank", "enable_plan_caching"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "flatten_state_dict", "flatten_sharded_tensors", "dedup_replicated_tensors", "dedup_save_to_lowest_rank", "enable_plan_caching"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultSavePlanner", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of DefaultSavePlanner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cached_plans_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner._cached_plans_key", "name": "_cached_plans_key", "setter_type": null, "type": "builtins.str"}}, "_create_global_plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "all_plans"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner._create_global_plan", "name": "_create_global_plan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "all_plans"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultSavePlanner", {".class": "Instance", "args": ["torch.distributed.checkpoint.planner.SavePlan"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_create_global_plan of DefaultSavePlanner", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["torch.distributed.checkpoint.planner.SavePlan"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch.distributed.checkpoint.metadata.Metadata"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_global_plan_with_caching": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "all_plans"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner._create_global_plan_with_caching", "name": "_create_global_plan_with_caching", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "all_plans"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultSavePlanner", {".class": "Instance", "args": ["torch.distributed.checkpoint.planner.SavePlan"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_create_global_plan_with_caching of DefaultSavePlanner", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["torch.distributed.checkpoint.planner.SavePlan"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch.distributed.checkpoint.planner.SavePlan"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch.distributed.checkpoint.metadata.Metadata"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_enable_plan_caching": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner._enable_plan_caching", "name": "_enable_plan_caching", "setter_type": null, "type": "builtins.bool"}}, "_finish_plan_with_caching": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_plan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner._finish_plan_with_caching", "name": "_finish_plan_with_caching", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_plan"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultSavePlanner", "torch.distributed.checkpoint.planner.SavePlan"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_finish_plan_with_caching of DefaultSavePlanner", "ret_type": "torch.distributed.checkpoint.planner.SavePlan", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_global_plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "all_plans"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.create_global_plan", "name": "create_global_plan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "all_plans"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultSavePlanner", {".class": "Instance", "args": ["torch.distributed.checkpoint.planner.SavePlan"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_global_plan of DefaultSavePlanner", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["torch.distributed.checkpoint.planner.SavePlan"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch.distributed.checkpoint.metadata.Metadata"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_local_plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.create_local_plan", "name": "create_local_plan", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultSavePlanner"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_local_plan of DefaultSavePlanner", "ret_type": "torch.distributed.checkpoint.planner.SavePlan", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dedup_save_to_lowest_rank": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.dedup_save_to_lowest_rank", "name": "dedup_save_to_lowest_rank", "setter_type": null, "type": "builtins.bool"}}, "finish_plan": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_plan"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.finish_plan", "name": "finish_plan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_plan"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultSavePlanner", "torch.distributed.checkpoint.planner.SavePlan"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "finish_plan of DefaultSavePlanner", "ret_type": "torch.distributed.checkpoint.planner.SavePlan", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flatten_sharded_tensors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.flatten_sharded_tensors", "name": "flatten_sharded_tensors", "setter_type": null, "type": "builtins.bool"}}, "flatten_state_dict": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.flatten_state_dict", "name": "flatten_state_dict", "setter_type": null, "type": "builtins.bool"}}, "global_plan": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.global_plan", "name": "global_plan", "setter_type": null, "type": {".class": "Instance", "args": ["torch.distributed.checkpoint.planner.SavePlan"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "is_coordinator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.is_coordinator", "name": "is_coordinator", "setter_type": null, "type": "builtins.bool"}}, "lookup_object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.lookup_object", "name": "lookup_object", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "index"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultSavePlanner", "torch.distributed.checkpoint.metadata.MetadataIndex"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "lookup_object of DefaultSavePlanner", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mappings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.mappings", "name": "mappings", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.checkpoint._nested_dict.FLATTEN_MAPPING"}}}, "metadata": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.metadata", "name": "metadata", "setter_type": null, "type": "torch.distributed.checkpoint.metadata.Metadata"}}, "plan": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.plan", "name": "plan", "setter_type": null, "type": "torch.distributed.checkpoint.planner.SavePlan"}}, "resolve_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "write_item"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.resolve_data", "name": "resolve_data", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "write_item"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultSavePlanner", "torch.distributed.checkpoint.planner.WriteItem"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "resolve_data of DefaultSavePlanner", "ret_type": {".class": "UnionType", "items": ["torch._tensor.Tensor", "_io.BytesIO"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_up_planner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "state_dict", "storage_meta", "is_coordinator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.set_up_planner", "name": "set_up_planner", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "state_dict", "storage_meta", "is_coordinator"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultSavePlanner", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.metadata.StorageMeta", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_up_planner of DefaultSavePlanner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "state_dict": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.state_dict", "name": "state_dict", "setter_type": null, "type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}}}, "transform_object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "write_item", "object"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.transform_object", "name": "transform_object", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "write_item", "object"], "arg_types": ["torch.distributed.checkpoint.default_planner.DefaultSavePlanner", "torch.distributed.checkpoint.planner.WriteItem", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transform_object of DefaultSavePlanner", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.checkpoint.default_planner.DefaultSavePlanner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FLATTEN_MAPPING": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._nested_dict.FLATTEN_MAPPING", "kind": "Gdef", "module_public": false}, "LoadPlan": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.LoadPlan", "kind": "Gdef", "module_public": false}, "LoadPlanner": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.LoadPlanner", "kind": "Gdef", "module_public": false}, "Metadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.Metadata", "kind": "Gdef", "module_public": false}, "MetadataIndex": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.MetadataIndex", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "ReadItem": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.ReadItem", "kind": "Gdef", "module_public": false}, "STATE_DICT_TYPE": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE", "kind": "Gdef", "module_public": false}, "STORAGE_TYPES": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.STORAGE_TYPES", "kind": "Gdef", "module_public": false}, "SavePlan": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.SavePlan", "kind": "Gdef", "module_public": false}, "SavePlanner": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.SavePlanner", "kind": "Gdef", "module_public": false}, "StorageMeta": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.StorageMeta", "kind": "Gdef", "module_public": false}, "TensorStorageMetadata": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.metadata.TensorStorageMetadata", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "WriteItem": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.WriteItem", "kind": "Gdef", "module_public": false}, "WriteItemType": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner.WriteItemType", "kind": "Gdef", "module_public": false}, "_EmptyStateDictLoadPlanner": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.checkpoint.default_planner.DefaultLoadPlanner"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.checkpoint.default_planner._EmptyStateDictLoadPlanner", "name": "_EmptyStateDictLoadPlanner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.default_planner._EmptyStateDictLoadPlanner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.checkpoint.default_planner", "mro": ["torch.distributed.checkpoint.default_planner._EmptyStateDictLoadPlanner", "torch.distributed.checkpoint.default_planner.DefaultLoadPlanner", "torch.distributed.checkpoint.planner.LoadPlanner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 2, 4], "arg_names": ["self", "keys", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.default_planner._EmptyStateDictLoadPlanner.__init__", "name": "__init__", "type": null}}, "_should_include_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner._EmptyStateDictLoadPlanner._should_include_key", "name": "_should_include_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "metadata"], "arg_types": ["torch.distributed.checkpoint.default_planner._EmptyStateDictLoadPlanner", "builtins.str", "torch.distributed.checkpoint.metadata.Metadata"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_should_include_key of _EmptyStateDictLoadPlanner", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keys": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.checkpoint.default_planner._EmptyStateDictLoadPlanner.keys", "name": "keys", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "set_up_planner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "state_dict", "metadata", "is_coordinator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.checkpoint.default_planner._EmptyStateDictLoadPlanner.set_up_planner", "name": "set_up_planner", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "state_dict", "metadata", "is_coordinator"], "arg_types": ["torch.distributed.checkpoint.default_planner._EmptyStateDictLoadPlanner", {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}, {".class": "UnionType", "items": ["torch.distributed.checkpoint.metadata.Metadata", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_up_planner of _EmptyStateDictLoadPlanner", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.checkpoint.default_planner._EmptyStateDictLoadPlanner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.checkpoint.default_planner._EmptyStateDictLoadPlanner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.checkpoint.default_planner.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.default_planner.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.default_planner.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.default_planner.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.default_planner.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.default_planner.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.checkpoint.default_planner.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_check_box_bounds": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["outer_box_size", "inner_box"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.default_planner._check_box_bounds", "name": "_check_box_bounds", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["outer_box_size", "inner_box"], "arg_types": ["torch._<PERSON><PERSON>", "torch.distributed.checkpoint.metadata.ChunkStorageMetadata"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_check_box_bounds", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_box_overlap": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["box0", "box1"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.default_planner._check_box_overlap", "name": "_check_box_overlap", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["box0", "box1"], "arg_types": ["torch.distributed.checkpoint.metadata.ChunkStorageMetadata", "torch.distributed.checkpoint.metadata.ChunkStorageMetadata"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_check_box_overlap", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compare_save_plans": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner_helpers._compare_save_plans", "kind": "Gdef", "module_public": false}, "_create_default_local_metadata": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.default_planner._create_default_local_metadata", "name": "_create_default_local_metadata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["state_dict"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "torch.distributed.checkpoint.metadata.STATE_DICT_TYPE"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_create_default_local_metadata", "ret_type": "torch.distributed.checkpoint.metadata.Metadata", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_default_metadata_only_plan": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner_helpers._create_default_metadata_only_plan", "kind": "Gdef", "module_public": false}, "_create_read_items": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner_helpers._create_read_items", "kind": "Gdef", "module_public": false}, "_create_write_items": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner_helpers._create_write_items", "kind": "Gdef", "module_public": false}, "_flatten_sharded_tensors": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._sharded_tensor_utils._flatten_sharded_tensors", "kind": "Gdef", "module_public": false}, "_init_state_dict": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner_helpers._init_state_dict", "kind": "Gdef", "module_public": false}, "_merge_delta_local_plans": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.planner_helpers._merge_delta_local_plans", "kind": "Gdef", "module_public": false}, "_validate_global_plan": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["global_plan", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.default_planner._validate_global_plan", "name": "_validate_global_plan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["global_plan", "metadata"], "arg_types": [{".class": "Instance", "args": ["torch.distributed.checkpoint.planner.SavePlan"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch.distributed.checkpoint.metadata.Metadata"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_global_plan", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_version": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._version", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef", "module_public": false}, "create_default_global_load_plan": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["all_plans"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.default_planner.create_default_global_load_plan", "name": "create_default_global_load_plan", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["all_plans"], "arg_types": [{".class": "Instance", "args": ["torch.distributed.checkpoint.planner.LoadPlan"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_default_global_load_plan", "ret_type": {".class": "Instance", "args": ["torch.distributed.checkpoint.planner.LoadPlan"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_default_global_save_plan": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["all_plans", "rewrite_index_hints"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.default_planner.create_default_global_save_plan", "name": "create_default_global_save_plan", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["all_plans", "rewrite_index_hints"], "arg_types": [{".class": "Instance", "args": ["torch.distributed.checkpoint.planner.SavePlan"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_default_global_save_plan", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["torch.distributed.checkpoint.planner.SavePlan"], "extra_attrs": null, "type_ref": "builtins.list"}, "torch.distributed.checkpoint.metadata.Metadata"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_default_local_load_plan": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["state_dict", "metadata", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.default_planner.create_default_local_load_plan", "name": "create_default_local_load_plan", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["state_dict", "metadata", "strict"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.distributed.checkpoint.metadata.Metadata", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_default_local_load_plan", "ret_type": "torch.distributed.checkpoint.planner.LoadPlan", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_default_local_save_plan": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state_dict", "is_coordinator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.checkpoint.default_planner.create_default_local_save_plan", "name": "create_default_local_save_plan", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state_dict", "is_coordinator"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_default_local_save_plan", "ret_type": "torch.distributed.checkpoint.planner.SavePlan", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef", "module_public": false}, "dedup_save_plans": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._dedup_save_plans.dedup_save_plans", "kind": "Gdef", "module_public": false}, "find_state_dict_object": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint.utils.find_state_dict_object", "kind": "Gdef", "module_public": false}, "flatten_state_dict": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._nested_dict.flatten_state_dict", "kind": "Gdef", "module_public": false}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef", "module_public": false}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "torch.distributed.checkpoint.default_planner.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "narrow_tensor_by_index": {".class": "SymbolTableNode", "cross_ref": "torch.distributed._shard._utils.narrow_tensor_by_index", "kind": "Gdef", "module_public": false}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef", "module_public": false}, "reduce": {".class": "SymbolTableNode", "cross_ref": "functools.reduce", "kind": "Gdef", "module_public": false}, "set_element": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.checkpoint._traverse.set_element", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/torch/distributed/checkpoint/default_planner.py"}