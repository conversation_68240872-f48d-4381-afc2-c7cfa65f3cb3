{".class": "MypyFile", "_fullname": "torch.distributed.nn.jit.instantiator", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "INSTANTIATED_TEMPLATE_DIR_PATH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.nn.jit.instantiator.INSTANTIATED_TEMPLATE_DIR_PATH", "name": "INSTANTIATED_TEMPLATE_DIR_PATH", "setter_type": null, "type": "builtins.str"}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "_FILE_PREFIX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "torch.distributed.nn.jit.instantiator._FILE_PREFIX", "name": "_FILE_PREFIX", "setter_type": null, "type": "builtins.str"}}, "_TEMP_DIR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.nn.jit.instantiator._TEMP_DIR", "name": "_TEMP_DIR", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "tempfile.TemporaryDirectory"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.jit.instantiator.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.jit.instantiator.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.jit.instantiator.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.jit.instantiator.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.jit.instantiator.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.jit.instantiator.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_do_instantiate_remote_module_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["generated_module_name", "str_dict", "enable_moving_cpu_tensors_to_cuda"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.jit.instantiator._do_instantiate_remote_module_template", "name": "_do_instantiate_remote_module_template", "type": null}}, "_write": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["out_path", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.jit.instantiator._write", "name": "_write", "type": null}}, "atexit": {".class": "SymbolTableNode", "cross_ref": "atexit", "kind": "Gdef"}, "get_arg_return_types_from_interface": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["module_interface"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.jit.instantiator.get_arg_return_types_from_interface", "name": "get_arg_return_types_from_interface", "type": null}}, "get_remote_module_template": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.nn.jit.templates.remote_module_template.get_remote_module_template", "kind": "Gdef"}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef"}, "instantiate_non_scriptable_remote_module_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.jit.instantiator.instantiate_non_scriptable_remote_module_template", "name": "instantiate_non_scriptable_remote_module_template", "type": null}}, "instantiate_scriptable_remote_module_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["module_interface_cls", "enable_moving_cpu_tensors_to_cuda"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.jit.instantiator.instantiate_scriptable_remote_module_template", "name": "instantiate_scriptable_remote_module_template", "type": null}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.nn.jit.instantiator.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/torch/distributed/nn/jit/instantiator.py"}