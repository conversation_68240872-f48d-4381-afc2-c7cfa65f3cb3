{".class": "MypyFile", "_fullname": "torch.distributed.nn.api.remote_module", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "Module": {".class": "SymbolTableNode", "cross_ref": "torch.nn.modules.module.Module", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Parameter": {".class": "SymbolTableNode", "cross_ref": "torch.nn.parameter.Parameter", "kind": "Gdef", "module_public": false}, "RemoteModule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.distributed.nn.api.remote_module._RemoteModule"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.nn.api.remote_module.RemoteModule", "name": "RemoteModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module.RemoteModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.nn.api.remote_module", "mro": ["torch.distributed.nn.api.remote_module.RemoteModule", "torch.distributed.nn.api.remote_module._RemoteModule", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "remote_device", "module_cls", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module.RemoteModule.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "remote_device", "module_cls", "args", "kwargs"], "arg_types": ["torch.distributed.nn.api.remote_module.RemoteModule", "builtins.str", {".class": "TypeType", "item": "torch.nn.modules.module.Module"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RemoteModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.RemoteModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.nn.api.remote_module.RemoteModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RemovableHandle": {".class": "SymbolTableNode", "cross_ref": "torch.utils.hooks.RemovableHandle", "kind": "Gdef", "module_public": false}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "name": "T", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}}, "Tensor": {".class": "SymbolTableNode", "cross_ref": "torch._tensor.Tensor", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_NON_SCRIPTABLE_REMOTE_MODULE_MODULE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.nn.api.remote_module._NON_SCRIPTABLE_REMOTE_MODULE_MODULE", "name": "_NON_SCRIPTABLE_REMOTE_MODULE_MODULE", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_REMOTE_MODULE_ATTRIBUTES_IGNORE_FOR_PICKLING": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.nn.api.remote_module._REMOTE_MODULE_ATTRIBUTES_IGNORE_FOR_PICKLING", "name": "_REMOTE_MODULE_ATTRIBUTES_IGNORE_FOR_PICKLING", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_REMOTE_MODULE_PICKLED_ATTRIBUTES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.nn.api.remote_module._REMOTE_MODULE_PICKLED_ATTRIBUTES", "name": "_REMOTE_MODULE_PICKLED_ATTRIBUTES", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_RemoteModule": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.nn.api.remote_module._RemoteModule", "name": "_RemoteModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "torch.distributed.nn.api.remote_module", "mro": ["torch.distributed.nn.api.remote_module._RemoteModule", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.__getstate__", "name": "__getstate__", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.__getstate__", "name": "__getstate__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "remote_device", "module_cls", "args", "kwargs", "_module_interface_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "remote_device", "module_cls", "args", "kwargs", "_module_interface_cls"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule", "builtins.str", {".class": "TypeType", "item": "torch.nn.modules.module.Module"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of _RemoteModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.__new__", "name": "__new__", "type": null}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.__setstate__", "name": "__setstate__", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.__setstate__", "name": "__setstate__", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "_check_attribute_picklability": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule._check_attribute_picklability", "name": "_check_attribute_picklability", "type": null}}, "_init_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "module_interface_cls", "enable_moving_cpu_tensors_to_cuda"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule._init_template", "name": "_init_template", "type": null}}, "_install_generated_methods": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule._install_generated_methods", "name": "_install_generated_methods", "type": null}}, "_prepare_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "remote_device_str"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule._prepare_init", "name": "_prepare_init", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "remote_device_str"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_prepare_init of _RemoteModule", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "module"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.add_module", "name": "add_module", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "module"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule", "builtins.str", {".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_module of _RemoteModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.apply", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["torch.nn.modules.module.Module"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "apply of _RemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.apply", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.apply", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}]}}}, "bfloat16": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.bfloat16", "name": "bfloat16", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.bfloat16", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "bfloat16 of _RemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.bfloat16", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.bfloat16", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}]}}}, "buffers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "recurse"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.buffers", "name": "buffers", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recurse"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "buffers of _RemoteModule", "ret_type": {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.children", "name": "children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "children of _RemoteModule", "ret_type": {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.cpu", "name": "cpu", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.cpu", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cpu of _RemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.cpu", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.cpu", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}]}}}, "cuda": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.cuda", "name": "cuda", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "device"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.cuda", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.int", "torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cuda of _RemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.cuda", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.cuda", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}]}}}, "device": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.device", "name": "device", "setter_type": null, "type": "builtins.str"}}, "double": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.double", "name": "double", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.double", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "double of _RemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.double", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.double", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}]}}}, "eval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.eval", "name": "eval", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.eval", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "eval of _RemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.eval", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.eval", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}]}}}, "extra_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.extra_repr", "name": "extra_repr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extra_repr of _RemoteModule", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "float": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.float", "name": "float", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.float", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "float of _RemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.float", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.float", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}]}}}, "generated_methods": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.generated_methods", "name": "generated_methods", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "get_module_rref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.get_module_rref", "name": "get_module_rref", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_module_rref of _RemoteModule", "ret_type": {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "torch.distributed.rpc.api.RRef"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "half": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.half", "name": "half", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.half", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "half of _RemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.half", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.half", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}]}}}, "init_from_module_rref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["remote_device", "module_rref", "_module_interface_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.init_from_module_rref", "name": "init_from_module_rref", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["remote_device", "module_rref", "_module_interface_cls"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "torch.distributed.rpc.api.RRef"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "init_from_module_rref of _RemoteModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.init_from_module_rref", "name": "init_from_module_rref", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["remote_device", "module_rref", "_module_interface_cls"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "torch.distributed.rpc.api.RRef"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "init_from_module_rref of _RemoteModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ipu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.ipu", "name": "ipu", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "device"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.ipu", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.int", "torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ipu of _RemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.ipu", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.ipu", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}]}}}, "is_device_map_set": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.is_device_map_set", "name": "is_device_map_set", "setter_type": null, "type": "builtins.bool"}}, "is_scriptable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.is_scriptable", "name": "is_scriptable", "setter_type": null, "type": "builtins.bool"}}, "load_state_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "state_dict", "strict", "assign"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.load_state_dict", "name": "load_state_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "state_dict", "strict", "assign"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "load_state_dict of _RemoteModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "module_rref": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.module_rref", "name": "module_rref", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.modules", "name": "modules", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "modules of _RemoteModule", "ret_type": {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "named_buffers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "prefix", "recurse", "remove_duplicate"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.named_buffers", "name": "named_buffers", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "prefix", "recurse", "remove_duplicate"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule", "builtins.str", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "named_buffers of _RemoteModule", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "named_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.named_children", "name": "named_children", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "named_children of _RemoteModule", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "torch.nn.modules.module.Module"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "named_modules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "memo", "prefix", "remove_duplicate"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.named_modules", "name": "named_modules", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "memo", "prefix", "remove_duplicate"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule", {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "named_modules of _RemoteModule", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "named_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "prefix", "recurse", "remove_duplicate"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.named_parameters", "name": "named_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "prefix", "recurse", "remove_duplicate"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule", "builtins.str", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "named_parameters of _RemoteModule", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "torch.nn.parameter.Parameter"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.on", "name": "on", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, "builtins.int"], "uses_pep604_syntax": false}}}, "parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "recurse"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.parameters", "name": "parameters", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recurse"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parameters of _RemoteModule", "ret_type": {".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_backward_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hook"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.register_backward_hook", "name": "register_backward_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hook"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule", {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["torch.nn.modules.module.Module", {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.nn.api.remote_module._grad_t"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.nn.api.remote_module._grad_t"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "torch.distributed.nn.api.remote_module._grad_t"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register_backward_hook of _RemoteModule", "ret_type": "torch.utils.hooks.RemovableHandle", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "tensor", "persistent"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.register_buffer", "name": "register_buffer", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "tensor", "persistent"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule", "builtins.str", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register_buffer of _RemoteModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_forward_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hook", "prepend", "with_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.register_forward_hook", "name": "register_forward_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hook", "prepend", "with_kwargs"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.register_forward_hook", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.register_forward_hook", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register_forward_hook of _RemoteModule", "ret_type": "torch.utils.hooks.RemovableHandle", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.register_forward_hook", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}]}}}, "register_forward_pre_hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hook", "prepend", "with_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.register_forward_pre_hook", "name": "register_forward_pre_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "hook", "prepend", "with_kwargs"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.register_forward_pre_hook", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.register_forward_pre_hook", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register_forward_pre_hook of _RemoteModule", "ret_type": "torch.utils.hooks.RemovableHandle", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.register_forward_pre_hook", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}]}}}, "register_parameter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "param"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.register_parameter", "name": "register_parameter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "param"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule", "builtins.str", {".class": "UnionType", "items": ["torch.nn.parameter.Parameter", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register_parameter of _RemoteModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remote_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "recurse"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.remote_parameters", "name": "remote_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "recurse"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "remote_parameters of _RemoteModule", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "torch.distributed.rpc.api.RRef"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "requires_grad_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "requires_grad"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.requires_grad_", "name": "requires_grad_", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "requires_grad"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.requires_grad_", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "requires_grad_ of _RemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.requires_grad_", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.requires_grad_", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}]}}}, "share_memory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.share_memory", "name": "share_memory", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.share_memory", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "share_memory of _RemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.share_memory", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.share_memory", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}]}}}, "state_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.state_dict", "name": "state_dict", "type": null}}, "to": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.to", "name": "to", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to of _RemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.to", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.to", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}]}}}, "train": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.train", "name": "train", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "mode"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.train", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "train of _RemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.train", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.train", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}]}}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dst_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.type", "name": "type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dst_type"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.type", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, {".class": "UnionType", "items": ["torch._C.dtype", "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "type of _RemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.type", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.type", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}]}}}, "xpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.xpu", "name": "xpu", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "device"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.xpu", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.int", "torch._C.device", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "xpu of _RemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.xpu", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module.T", "id": -1, "name": "T", "namespace": "torch.distributed.nn.api.remote_module._RemoteModule.xpu", "upper_bound": "torch.nn.modules.module.Module", "values": [], "variance": 0}]}}}, "zero_grad": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "set_to_none"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.zero_grad", "name": "zero_grad", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "set_to_none"], "arg_types": ["torch.distributed.nn.api.remote_module._RemoteModule", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "zero_grad of _RemoteModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._RemoteModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "torch.distributed.nn.api.remote_module._RemoteModule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SerializedRemoteModule": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule", "name": "_SerializedRemoteModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": []}}, "module_name": "torch.distributed.nn.api.remote_module", "mro": ["torch.distributed.nn.api.remote_module._SerializedRemoteModule", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_cls"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.nn.api.remote_module._SerializedRemoteModule.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__new__ of _SerializedRemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.nn.api.remote_module._SerializedRemoteModule.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.nn.api.remote_module._SerializedRemoteModule.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "__replace__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule.__replace__", "name": "__replace__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.nn.api.remote_module._SerializedRemoteModule.__replace__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__replace__ of _SerializedRemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.nn.api.remote_module._SerializedRemoteModule.__replace__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.nn.api.remote_module._SerializedRemoteModule.__replace__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_asdict of _SerializedRemoteModule", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._field_defaults", "name": "_field_defaults", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._field_types", "name": "_field_types", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._fields", "name": "_fields", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make of _SerializedRemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._make", "name": "_make", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make of _SerializedRemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_replace of _SerializedRemoteModule", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._NT", "id": -1, "name": "_NT", "namespace": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "torch.distributed.nn.api.remote_module._SerializedRemoteModule._source", "name": "_source", "setter_type": null, "type": "builtins.str"}}}, "self_type": null, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "UninhabitedType"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "torch.distributed.nn.api.remote_module.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.api.remote_module.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.api.remote_module.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.api.remote_module.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.api.remote_module.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.api.remote_module.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "torch.distributed.nn.api.remote_module.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_create_module": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["module_cls", "args", "kwargs", "device"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._create_module", "name": "_create_module", "type": null}}, "_create_module_with_interface": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["module_cls", "args", "kwargs", "device", "module_interface_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._create_module_with_interface", "name": "_create_module_with_interface", "type": null}}, "_grad_t": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "torch.distributed.nn.api.remote_module._grad_t", "line": 23, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "torch._tensor.Tensor"], "uses_pep604_syntax": false}}}, "_instantiate_template": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["module_interface_cls", "enable_moving_cpu_tensors_to_cuda"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._instantiate_template", "name": "_instantiate_template", "type": null}}, "_internal_rpc_pickler": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.rpc.internal._internal_rpc_pickler", "kind": "Gdef", "module_public": false}, "_param_rrefs": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["module_rref", "recurse"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._param_rrefs", "name": "_param_rrefs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["module_rref", "recurse"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_param_rrefs", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["torch.nn.parameter.Parameter"], "extra_attrs": null, "type_ref": "torch.distributed.rpc.api.RRef"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_raise_not_supported": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._raise_not_supported", "name": "_raise_not_supported", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_raise_not_supported", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_recursive_script_module_receiver": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["recursive_script_module_serialized"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._recursive_script_module_receiver", "name": "_recursive_script_module_receiver", "type": null}}, "_recursive_script_module_reducer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["recursive_script_module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._recursive_script_module_reducer", "name": "_recursive_script_module_reducer", "type": null}}, "_remote_device": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.remote_device._remote_device", "kind": "Gdef", "module_public": false}, "_remote_module_receiver": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["remote_module_pickled_attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._remote_module_receiver", "name": "_remote_module_receiver", "type": null}}, "_remote_module_reducer": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["remote_module"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "torch.distributed.nn.api.remote_module._remote_module_reducer", "name": "_remote_module_reducer", "type": null}}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef", "module_public": false}, "device": {".class": "SymbolTableNode", "cross_ref": "torch._C.device", "kind": "Gdef", "module_public": false}, "dtype": {".class": "SymbolTableNode", "cross_ref": "torch._C.dtype", "kind": "Gdef", "module_public": false}, "instantiator": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.nn.jit.instantiator", "kind": "Gdef", "module_public": false}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef", "module_public": false}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef", "module_public": false}, "rpc": {".class": "SymbolTableNode", "cross_ref": "torch.distributed.rpc", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef", "module_public": false}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/torch/distributed/nn/api/remote_module.py"}