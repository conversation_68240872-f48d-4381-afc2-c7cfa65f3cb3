{"data_mtime": 1752049734, "dep_lines": [19, 13, 14, 15, 16, 12, 13, 17, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 10, 20, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["torch._inductor.package.pt2_archive_constants", "torch.utils._pytree", "torch._inductor.config", "torch._inductor.cpp_builder", "torch.export._tree_utils", "torch._inductor", "torch.utils", "torch.types", "io", "json", "logging", "os", "tempfile", "zipfile", "pathlib", "typing", "typing_extensions", "torch", "builtins", "_collections_abc", "_frozen_importlib", "abc", "torch._C", "torch._<PERSON><PERSON>_aoti", "torch._tensor"], "hash": "ac1c227de0d8c3496fa2d538879cbb8efeabb03b", "id": "torch._inductor.package.package", "ignore_all": true, "interface_hash": "846ff2efd8a0eccf216593aa733130d6e6bf405d", "mtime": 1751921840, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/torch/_inductor/package/package.py", "plugin_data": null, "size": 11410, "suppressed": [], "version_id": "1.16.1"}