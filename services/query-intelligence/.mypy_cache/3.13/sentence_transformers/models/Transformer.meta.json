{"data_mtime": 1752049734, "dep_lines": [18, 19, 21, 1, 3, 4, 5, 6, 7, 8, 13, 15, 16, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 204, 203, 258, 26, 257], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 20, 5, 20], "dependencies": ["transformers.utils.import_utils", "transformers.utils.peft_utils", "sentence_transformers.models.InputModule", "__future__", "json", "logging", "os", "fnmatch", "pathlib", "typing", "typing_extensions", "huggingface_hub", "torch", "transformers", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "json.decoder", "sentence_transformers.models.Module", "torch._C", "torch._tensor", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "transformers.configuration_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.modeling_utils", "transformers.models", "transformers.models.auto", "transformers.models.auto.auto_factory", "transformers.models.auto.modeling_auto", "transformers.models.auto.tokenization_auto", "transformers.models.mt5", "transformers.models.mt5.configuration_mt5", "transformers.models.mt5.modeling_mt5", "transformers.models.t5", "transformers.models.t5.configuration_t5", "transformers.models.t5.modeling_t5", "transformers.tokenization_utils_base", "transformers.utils", "transformers.utils.hub", "types"], "hash": "e6ac7d6479ed3e5c948d2512a47b72cf9fa0da22", "id": "sentence_transformers.models.Transformer", "ignore_all": true, "interface_hash": "e3d25bc0dbc0ac84b9749afe19cd6d054ade41d3", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/models/Transformer.py", "plugin_data": null, "size": 28805, "suppressed": ["optimum.intel.openvino", "optimum.intel", "optimum.onnxruntime", "peft", "onnxruntime"], "version_id": "1.16.1"}