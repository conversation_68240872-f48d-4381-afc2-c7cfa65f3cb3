{"data_mtime": 1752049734, "dep_lines": [5, 3, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sentence_transformers.models.Module", "torch.nn", "__future__", "torch", "builtins", "_frozen_importlib", "abc", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.dropout", "torch.nn.modules.module", "typing"], "hash": "33a9a7b93dd89a2e063af566ed14645d2aed08a5", "id": "sentence_transformers.models.Dropout", "ignore_all": true, "interface_hash": "9a27406528c7391df5cc8a3ef0e01c31b219f696", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/models/Dropout.py", "plugin_data": null, "size": 782, "suppressed": [], "version_id": "1.16.1"}