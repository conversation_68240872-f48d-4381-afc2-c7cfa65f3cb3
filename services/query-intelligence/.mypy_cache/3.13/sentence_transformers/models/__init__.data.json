{".class": "MypyFile", "_fullname": "sentence_transformers.models", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Asym": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.Router.Asym", "kind": "Gdef"}, "BoW": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.BoW.BoW", "kind": "Gdef"}, "CLIPModel": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.CLIPModel.CLIPModel", "kind": "Gdef"}, "CNN": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.CNN.CNN", "kind": "Gdef"}, "Dense": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.Dense.Dense", "kind": "Gdef"}, "Dropout": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.Dropout.Dropout", "kind": "Gdef"}, "InputModule": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.InputModule.InputModule", "kind": "Gdef"}, "LSTM": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.LSTM.LSTM", "kind": "Gdef"}, "LayerNorm": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.LayerNorm.LayerNorm", "kind": "Gdef"}, "Module": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.Module.Module", "kind": "Gdef"}, "Normalize": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.Normalize.Normalize", "kind": "Gdef"}, "Pooling": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.Pooling.Pooling", "kind": "Gdef"}, "Router": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.Router.Router", "kind": "Gdef"}, "StaticEmbedding": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.StaticEmbedding.StaticEmbedding", "kind": "Gdef"}, "Transformer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.Transformer.Transformer", "kind": "Gdef"}, "WeightedLayerPooling": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.WeightedLayerPooling.WeightedLayerPooling", "kind": "Gdef"}, "WordEmbeddings": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.WordEmbeddings.WordEmbeddings", "kind": "Gdef"}, "WordWeights": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.WordWeights.WordWeights", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.models.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/models/__init__.py"}