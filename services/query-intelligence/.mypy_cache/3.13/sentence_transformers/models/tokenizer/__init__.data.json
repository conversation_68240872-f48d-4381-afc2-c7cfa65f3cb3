{".class": "MypyFile", "_fullname": "sentence_transformers.models.tokenizer", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ENGLISH_STOP_WORDS": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.tokenizer.WordTokenizer.ENGLISH_STOP_WORDS", "kind": "Gdef"}, "PhraseTokenizer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.tokenizer.PhraseTokenizer.PhraseTokenizer", "kind": "Gdef"}, "TransformersTokenizerWrapper": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.tokenizer.WordTokenizer.TransformersTokenizerWrapper", "kind": "Gdef"}, "WhitespaceTokenizer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.tokenizer.WhitespaceTokenizer.WhitespaceTokenizer", "kind": "Gdef"}, "WordTokenizer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.tokenizer.WordTokenizer.WordTokenizer", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.models.tokenizer.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.tokenizer.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.tokenizer.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.tokenizer.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.tokenizer.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.tokenizer.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.tokenizer.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.tokenizer.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/models/tokenizer/__init__.py"}