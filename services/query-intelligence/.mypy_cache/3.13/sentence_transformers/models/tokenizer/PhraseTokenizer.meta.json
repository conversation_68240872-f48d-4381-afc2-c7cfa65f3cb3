{"data_mtime": 1752049734, "dep_lines": [12, 10, 8, 1, 3, 4, 5, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1, 64], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["sentence_transformers.models.tokenizer.WordTokenizer", "transformers.utils.import_utils", "collections.abc", "__future__", "collections", "json", "logging", "os", "string", "builtins", "_frozen_importlib", "_typeshed", "abc", "transformers", "transformers.utils", "types", "typing"], "hash": "5fd9d639a78f0bb11fcca1388fa606b9e69c622c", "id": "sentence_transformers.models.tokenizer.PhraseTokenizer", "ignore_all": true, "interface_hash": "2fcc980445fcfd8cdb703d1ce6cf69fbd12d1ec1", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/models/tokenizer/PhraseTokenizer.py", "plugin_data": null, "size": 4708, "suppressed": ["nltk"], "version_id": "1.16.1"}