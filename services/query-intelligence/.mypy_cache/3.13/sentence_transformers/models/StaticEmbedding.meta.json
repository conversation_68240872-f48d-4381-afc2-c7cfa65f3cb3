{"data_mtime": 1752049734, "dep_lines": [22, 17, 19, 23, 1, 3, 4, 5, 6, 7, 8, 13, 15, 16, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 203, 18, 257], "dep_prios": [5, 5, 10, 5, 5, 10, 10, 10, 10, 5, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 5, 20], "dependencies": ["sentence_transformers.models.InputModule", "safetensors.torch", "torch.nn", "sentence_transformers.util", "__future__", "inspect", "logging", "math", "os", "pathlib", "typing", "typing_extensions", "numpy", "torch", "transformers", "builtins", "_collections_abc", "_frozen_importlib", "abc", "numpy._typing", "numpy._typing._dtype_like", "sentence_transformers.models.Module", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.modules", "torch.nn.modules.module", "torch.nn.modules.sparse", "transformers.utils", "transformers.utils.dummy_tokenizers_objects", "transformers.utils.import_utils", "types"], "hash": "4435bbb93a867dc7f2a60f5e0aa91a6d3edc56c4", "id": "sentence_transformers.models.StaticEmbedding", "ignore_all": true, "interface_hash": "504f1f359eb2c7bb47eb59c0e7a2e2c5b5e62642", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/models/StaticEmbedding.py", "plugin_data": null, "size": 11891, "suppressed": ["model2vec.distill", "tokenizers", "model2vec"], "version_id": "1.16.1"}