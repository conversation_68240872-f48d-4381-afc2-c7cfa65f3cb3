{"data_mtime": 1752049734, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 1, 3, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["sentence_transformers.models.BoW", "sentence_transformers.models.CLIPModel", "sentence_transformers.models.CNN", "sentence_transformers.models.Dense", "sentence_transformers.models.Dropout", "sentence_transformers.models.InputModule", "sentence_transformers.models.LayerNorm", "sentence_transformers.models.LSTM", "sentence_transformers.models.Module", "sentence_transformers.models.Normalize", "sentence_transformers.models.Pooling", "sentence_transformers.models.Router", "sentence_transformers.models.StaticEmbedding", "sentence_transformers.models.Transformer", "sentence_transformers.models.WeightedLayerPooling", "sentence_transformers.models.WordEmbeddings", "sentence_transformers.models.WordWeights", "__future__", "sys", "builtins", "_frozen_importlib", "abc", "types", "typing"], "hash": "94e153293bf0cef9708f4dfc72db798956679ca5", "id": "sentence_transformers.models", "ignore_all": true, "interface_hash": "6335638c30620c1433bd4d625957273f8c9edccd", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/models/__init__.py", "plugin_data": null, "size": 1069, "suppressed": [], "version_id": "1.16.1"}