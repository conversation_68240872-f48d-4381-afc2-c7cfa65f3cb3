{"data_mtime": 1752049734, "dep_lines": [14, 16, 17, 13, 14, 18, 1, 3, 4, 5, 6, 11, 9, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 20, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "sentence_transformers.models.InputModule", "sentence_transformers.models.Module", "torch.nn", "transformers.utils", "sentence_transformers.util", "__future__", "json", "os", "collections", "pathlib", "typing_extensions", "typing", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "logging", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "transformers", "types"], "hash": "c86142ebb44b4f0c329d3d64579021a4ef746d82", "id": "sentence_transformers.models.Router", "ignore_all": true, "interface_hash": "a6d3ab4fa1d1c47a82e8dc9d7b11a4c736b408b6", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/models/Router.py", "plugin_data": null, "size": 19908, "suppressed": [], "version_id": "1.16.1"}