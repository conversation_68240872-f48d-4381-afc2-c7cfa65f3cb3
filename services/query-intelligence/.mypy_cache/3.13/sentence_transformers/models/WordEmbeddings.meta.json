{"data_mtime": 1752049734, "dep_lines": [22, 22, 19, 22, 16, 20, 1, 3, 4, 5, 7, 12, 10, 14, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 17], "dep_prios": [10, 10, 5, 5, 10, 5, 5, 10, 10, 10, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["sentence_transformers.models.tokenizer.WhitespaceTokenizer", "sentence_transformers.models.tokenizer.WordTokenizer", "sentence_transformers.models.Module", "sentence_transformers.models.tokenizer", "torch.nn", "sentence_transformers.util", "__future__", "gzip", "logging", "os", "transformers", "typing_extensions", "typing", "numpy", "torch", "builtins", "_collections_abc", "_frozen_importlib", "abc", "numpy._core", "numpy._core.multiarray", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nested_sequence", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.modules", "torch.nn.modules.module", "torch.nn.modules.sparse", "transformers.tokenization_utils_base", "transformers.utils", "transformers.utils.hub", "types"], "hash": "371256fd4daf5a061be661503b82e57fab9ea8f0", "id": "sentence_transformers.models.WordEmbeddings", "ignore_all": true, "interface_hash": "15b9af797368061d1001cfe6780c917679fec69a", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/models/WordEmbeddings.py", "plugin_data": null, "size": 7407, "suppressed": ["tqdm"], "version_id": "1.16.1"}