{".class": "MypyFile", "_fullname": "sentence_transformers.models.WordEmbeddings", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Module": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.Module.Module", "kind": "Gdef"}, "PreTrainedTokenizerBase": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.PreTrainedTokenizerBase", "kind": "Gdef"}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef"}, "TransformersTokenizerWrapper": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.tokenizer.WordTokenizer.TransformersTokenizerWrapper", "kind": "Gdef"}, "WhitespaceTokenizer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.tokenizer.WhitespaceTokenizer.WhitespaceTokenizer", "kind": "Gdef"}, "WordEmbeddings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sentence_transformers.models.Module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings", "name": "WordEmbeddings", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sentence_transformers.models.WordEmbeddings", "mro": ["sentence_transformers.models.WordEmbeddings.WordEmbeddings", "sentence_transformers.models.Module.Module", "abc.ABC", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "tokenizer", "embedding_weights", "update_embeddings", "max_seq_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "tokenizer", "embedding_weights", "update_embeddings", "max_seq_length"], "arg_types": ["sentence_transformers.models.WordEmbeddings.WordEmbeddings", {".class": "UnionType", "items": ["sentence_transformers.models.tokenizer.WordTokenizer.WordTokenizer", "transformers.tokenization_utils_base.PreTrainedTokenizerBase"], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of WordEmbeddings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config_file_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.config_file_name", "name": "config_file_name", "setter_type": null, "type": "builtins.str"}}, "config_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.config_keys", "name": "config_keys", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "emb_layer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.emb_layer", "name": "emb_layer", "setter_type": null, "type": "torch.nn.modules.sparse.Embedding"}}, "embeddings_dimension": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.embeddings_dimension", "name": "embeddings_dimension", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "features"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.forward", "name": "forward", "type": null}}, "from_text_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["cls", "embeddings_file_path", "update_embeddings", "item_separator", "tokenizer", "max_vocab_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.from_text_file", "name": "from_text_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["cls", "embeddings_file_path", "update_embeddings", "item_separator", "tokenizer", "max_vocab_size"], "arg_types": [{".class": "TypeType", "item": "sentence_transformers.models.WordEmbeddings.WordEmbeddings"}, "builtins.str", "builtins.bool", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_text_file of WordEmbeddings", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.from_text_file", "name": "from_text_file", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["cls", "embeddings_file_path", "update_embeddings", "item_separator", "tokenizer", "max_vocab_size"], "arg_types": [{".class": "TypeType", "item": "sentence_transformers.models.WordEmbeddings.WordEmbeddings"}, "builtins.str", "builtins.bool", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_text_file of WordEmbeddings", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_config_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.get_config_dict", "name": "get_config_dict", "type": null}}, "get_word_embedding_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.get_word_embedding_dimension", "name": "get_word_embedding_dimension", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.models.WordEmbeddings.WordEmbeddings"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_word_embedding_dimension of WordEmbeddings", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "model_name_or_path", "subfolder", "token", "cache_folder", "revision", "local_files_only", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "model_name_or_path", "subfolder", "token", "cache_folder", "revision", "local_files_only", "kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.models.WordEmbeddings.WordEmbeddings", "values": [], "variance": 0}}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "load of WordEmbeddings", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.models.WordEmbeddings.WordEmbeddings", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.models.WordEmbeddings.WordEmbeddings", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.load", "name": "load", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "model_name_or_path", "subfolder", "token", "cache_folder", "revision", "local_files_only", "kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.models.WordEmbeddings.WordEmbeddings", "values": [], "variance": 0}}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "load of WordEmbeddings", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.models.WordEmbeddings.WordEmbeddings", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.models.WordEmbeddings.WordEmbeddings", "values": [], "variance": 0}]}}}}, "max_seq_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.max_seq_length", "name": "max_seq_length", "setter_type": null, "type": "builtins.int"}}, "save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "output_path", "safe_serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.save", "name": "save", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "output_path", "safe_serialization"], "arg_types": ["sentence_transformers.models.WordEmbeddings.WordEmbeddings", "builtins.str", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "save of WordEmbeddings", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tokenize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "texts", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.tokenize", "name": "tokenize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "texts", "kwargs"], "arg_types": ["sentence_transformers.models.WordEmbeddings.WordEmbeddings", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tokenize of WordEmbeddings", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tokenizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.tokenizer", "name": "tokenizer", "setter_type": null, "type": "sentence_transformers.models.tokenizer.WordTokenizer.WordTokenizer"}}, "update_embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.update_embeddings", "name": "update_embeddings", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.models.WordEmbeddings.WordEmbeddings.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.models.WordEmbeddings.WordEmbeddings", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WordTokenizer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.tokenizer.WordTokenizer.WordTokenizer", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.WordEmbeddings.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.WordEmbeddings.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.WordEmbeddings.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.WordEmbeddings.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.WordEmbeddings.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.models.WordEmbeddings.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "fullname": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.fullname", "kind": "Gdef"}, "gzip": {".class": "SymbolTableNode", "cross_ref": "gzip", "kind": "Gdef"}, "http_get": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.http_get", "kind": "Gdef"}, "import_from_string": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.import_from_string", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.models.WordEmbeddings.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "tqdm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.models.WordEmbeddings.tqdm", "name": "tqdm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.models.WordEmbeddings.tqdm", "source_any": null, "type_of_any": 3}}}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/models/WordEmbeddings.py"}