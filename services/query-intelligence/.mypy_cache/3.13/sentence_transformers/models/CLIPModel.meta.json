{"data_mtime": 1752049734, "dep_lines": [12, 10, 1, 6, 4, 8, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sentence_transformers.models.Router", "PIL.Image", "__future__", "typing_extensions", "typing", "torch", "transformers", "PIL", "builtins", "_frozen_importlib", "abc", "os", "sentence_transformers.models.InputModule", "sentence_transformers.models.Module", "torch._C", "torch._tensor", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "transformers.configuration_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.modeling_utils", "transformers.models", "transformers.models.clip", "transformers.models.clip.configuration_clip", "transformers.models.clip.modeling_clip", "transformers.models.clip.processing_clip", "transformers.processing_utils", "transformers.utils", "transformers.utils.hub"], "hash": "0a4d9833fd2027c20d35f26bf914d9d2880d9139", "id": "sentence_transformers.models.CLIPModel", "ignore_all": true, "interface_hash": "1a7dec2084d1b51dae3fa4c4bb44a4e56d205514", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/models/CLIPModel.py", "plugin_data": null, "size": 4263, "suppressed": [], "version_id": "1.16.1"}