{"data_mtime": 1752049734, "dep_lines": [11, 9, 11, 1, 3, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sentence_transformers.models.tokenizer.WhitespaceTokenizer", "sentence_transformers.models.InputModule", "sentence_transformers.models.tokenizer", "__future__", "logging", "typing", "torch", "builtins", "_frozen_importlib", "_typeshed", "abc", "sentence_transformers.models.Module", "sentence_transformers.models.tokenizer.WordTokenizer", "torch._C", "torch._tensor", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "transformers", "transformers.tokenization_utils_base", "transformers.utils", "transformers.utils.hub", "types"], "hash": "944348c6459128f27ba0b402444b84cfd4a94bb6", "id": "sentence_transformers.models.BoW", "ignore_all": true, "interface_hash": "dac5dc08d246aa08d0ab11d8cccff517bb7cd0d4", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/models/BoW.py", "plugin_data": null, "size": 3160, "suppressed": [], "version_id": "1.16.1"}