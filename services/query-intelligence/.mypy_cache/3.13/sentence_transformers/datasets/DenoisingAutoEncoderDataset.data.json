{".class": "MypyFile", "_fullname": "sentence_transformers.datasets.DenoisingAutoEncoderDataset", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Dataset": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.dataset.Dataset", "kind": "Gdef"}, "DenoisingAutoEncoderDataset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.Dataset"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.datasets.DenoisingAutoEncoderDataset.DenoisingAutoEncoderDataset", "name": "DenoisingAutoEncoderDataset", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.datasets.DenoisingAutoEncoderDataset.DenoisingAutoEncoderDataset", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.datasets.DenoisingAutoEncoderDataset", "mro": ["sentence_transformers.datasets.DenoisingAutoEncoderDataset.DenoisingAutoEncoderDataset", "torch.utils.data.dataset.Dataset", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.datasets.DenoisingAutoEncoderDataset.DenoisingAutoEncoderDataset.__getitem__", "name": "__getitem__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "sentences", "noise_fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.datasets.DenoisingAutoEncoderDataset.DenoisingAutoEncoderDataset.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "sentences", "noise_fn"], "arg_types": ["sentence_transformers.datasets.DenoisingAutoEncoderDataset.DenoisingAutoEncoderDataset", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of DenoisingAutoEncoderDataset", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.datasets.DenoisingAutoEncoderDataset.DenoisingAutoEncoderDataset.__len__", "name": "__len__", "type": null}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["text", "del_ratio"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "sentence_transformers.datasets.DenoisingAutoEncoderDataset.DenoisingAutoEncoderDataset.delete", "name": "delete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "sentence_transformers.datasets.DenoisingAutoEncoderDataset.DenoisingAutoEncoderDataset.delete", "name": "delete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["text", "del_ratio"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of DenoisingAutoEncoderDataset", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "noise_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.datasets.DenoisingAutoEncoderDataset.DenoisingAutoEncoderDataset.noise_fn", "name": "noise_fn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "sentences": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.datasets.DenoisingAutoEncoderDataset.DenoisingAutoEncoderDataset.sentences", "name": "sentences", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.datasets.DenoisingAutoEncoderDataset.DenoisingAutoEncoderDataset.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.datasets.DenoisingAutoEncoderDataset.DenoisingAutoEncoderDataset", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InputExample": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.readers.InputExample.InputExample", "kind": "Gdef"}, "NLTK_IMPORT_ERROR": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.NLTK_IMPORT_ERROR", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.datasets.DenoisingAutoEncoderDataset.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.datasets.DenoisingAutoEncoderDataset.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.datasets.DenoisingAutoEncoderDataset.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.datasets.DenoisingAutoEncoderDataset.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.datasets.DenoisingAutoEncoderDataset.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.datasets.DenoisingAutoEncoderDataset.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "is_nltk_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_nltk_available", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/datasets/DenoisingAutoEncoderDataset.py"}