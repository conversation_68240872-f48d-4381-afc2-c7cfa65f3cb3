{"data_mtime": 1752049734, "dep_lines": [15, 16, 18, 12, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 51, 50], "dep_prios": [5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20], "dependencies": ["torch.utils.data", "transformers.utils.import_utils", "sentence_transformers.readers.InputExample", "__future__", "numpy", "builtins", "_frozen_importlib", "abc", "torch", "torch.utils", "torch.utils.data.dataset", "transformers", "transformers.utils", "typing"], "hash": "80995d44f1d1a2aa94ed3435f4e69411e29f466b", "id": "sentence_transformers.datasets.DenoisingAutoEncoderDataset", "ignore_all": true, "interface_hash": "1c8885a8de7135b513eaedf3b6a79599043f0b26", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/datasets/DenoisingAutoEncoderDataset.py", "plugin_data": null, "size": 2528, "suppressed": ["nltk.tokenize.treebank", "nltk"], "version_id": "1.16.1"}