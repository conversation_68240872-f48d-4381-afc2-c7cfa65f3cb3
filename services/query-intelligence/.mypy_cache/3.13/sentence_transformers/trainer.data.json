{".class": "MypyFile", "_fullname": "sentence_transformers.trainer", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BatchSampler": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.sampler.BatchSampler", "kind": "Gdef"}, "BatchSamplers": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.training_args.BatchSamplers", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CoSENTLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.CoSENTLoss.CoSENTLoss", "kind": "Gdef"}, "ConcatDataset": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.dataset.ConcatDataset", "kind": "Gdef"}, "DataCollator": {".class": "SymbolTableNode", "cross_ref": "transformers.data.data_collator.DataCollator", "kind": "Gdef"}, "DataLoader": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.dataloader.DataLoader", "kind": "Gdef"}, "Dataset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.trainer.Dataset", "name": "Dataset", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Dataset", "source_any": null, "type_of_any": 3}}}, "DatasetDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.trainer.DatasetDict", "name": "DatasetDict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.DatasetDict", "source_any": null, "type_of_any": 3}}}, "DefaultBatchSampler": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sampler.DefaultBatchSampler", "kind": "Gdef"}, "EvalLoopOutput": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.EvalLoopOutput", "kind": "Gdef"}, "EvalPrediction": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.EvalPrediction", "kind": "Gdef"}, "GroupByLabelBatchSampler": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sampler.GroupByLabelBatchSampler", "kind": "Gdef"}, "IterableDataset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.trainer.IterableDataset", "name": "IterableDataset", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.IterableDataset", "source_any": null, "type_of_any": 3}}}, "IterableDatasetDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.trainer.IterableDatasetDict", "name": "IterableDatasetDict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.IterableDatasetDict", "source_any": null, "type_of_any": 3}}}, "MultiDatasetBatchSamplers": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.training_args.MultiDatasetBatchSamplers", "kind": "Gdef"}, "MultiDatasetDefaultBatchSampler": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sampler.MultiDatasetDefaultBatchSampler", "kind": "Gdef"}, "NoDuplicatesBatchSampler": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sampler.NoDuplicatesBatchSampler", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "Pooling": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.Pooling.Pooling", "kind": "Gdef"}, "PreTrainedTokenizerBase": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.PreTrainedTokenizerBase", "kind": "Gdef"}, "ProportionalBatchSampler": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sampler.ProportionalBatchSampler", "kind": "Gdef"}, "RandomSampler": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.sampler.RandomSampler", "kind": "Gdef"}, "RoundRobinBatchSampler": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sampler.RoundRobinBatchSampler", "kind": "Gdef"}, "Router": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.Router.Router", "kind": "Gdef"}, "SentenceEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.SentenceEvaluator", "kind": "Gdef"}, "SentenceTransformer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.SentenceTransformer.SentenceTransformer", "kind": "Gdef"}, "SentenceTransformerDataCollator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.data_collator.SentenceTransformerDataCollator", "kind": "Gdef"}, "SentenceTransformerModelCardCallback": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.model_card.SentenceTransformerModelCardCallback", "kind": "Gdef"}, "SentenceTransformerTrainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.dummy_pt_objects.Trainer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer", "name": "SentenceTransformerTrainer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer", "has_param_spec_type": false, "metaclass_type": "transformers.utils.import_utils.DummyObject", "metadata": {}, "module_name": "sentence_transformers.trainer", "mro": ["sentence_transformers.trainer.SentenceTransformerTrainer", "transformers.utils.dummy_pt_objects.Trainer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "model", "args", "train_dataset", "eval_dataset", "loss", "evaluator", "data_collator", "tokenizer", "model_init", "compute_metrics", "callbacks", "optimizers", "preprocess_logits_for_metrics"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "model", "args", "train_dataset", "eval_dataset", "loss", "evaluator", "data_collator", "tokenizer", "model_init", "compute_metrics", "callbacks", "optimizers", "preprocess_logits_for_metrics"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "UnionType", "items": ["sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["sentence_transformers.training_args.SentenceTransformerTrainingArguments", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Dataset", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.DatasetDict", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.IterableDataset", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Dataset", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Dataset", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.DatasetDict", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.IterableDataset", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Dataset", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "Instance", "args": ["builtins.str", "torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sentence_transformers.SentenceTransformer.SentenceTransformer"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sentence_transformers.SentenceTransformer.SentenceTransformer"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceEvaluator"}, {".class": "Instance", "args": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceEvaluator"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["transformers.data.data_collator.DataCollator", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["transformers.tokenization_utils_base.PreTrainedTokenizerBase", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "sentence_transformers.SentenceTransformer.SentenceTransformer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["transformers.trainer_utils.EvalPrediction"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["transformers.trainer_callback.TrainerCallback"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TupleType", "implicit": false, "items": ["torch.optim.optimizer.Optimizer", "torch.optim.lr_scheduler.LambdaLR"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SentenceTransformerTrainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_include_prompt_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer._include_prompt_length", "name": "_include_prompt_length", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_include_prompt_length of SentenceTransformerTrainer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_best_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer._load_best_model", "name": "_load_best_model", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_load_best_model of SentenceTransformerTrainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_from_checkpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "checkpoint_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer._load_from_checkpoint", "name": "_load_from_checkpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "checkpoint_path"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_load_from_checkpoint of SentenceTransformerTrainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "output_dir", "state_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer._save", "name": "_save", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "output_dir", "state_dict"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_save of SentenceTransformerTrainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_train_dataloader": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer._train_dataloader", "name": "_train_dataloader", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}}}, "accum_loss_components": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.accum_loss_components", "name": "accum_loss_components", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "add_dataset_name_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dataset", "dataset_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.add_dataset_name_column", "name": "add_dataset_name_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dataset", "dataset_name"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.DatasetDict", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.IterableDatasetDict", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Dataset", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.IterableDataset", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_dataset_name_column of SentenceTransformerTrainer", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.DatasetDict", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Dataset", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_dataset_name_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["batch", "dataset_name", "transform", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.add_dataset_name_transform", "name": "add_dataset_name_transform", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["batch", "dataset_name", "transform", "kwargs"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_dataset_name_transform of SentenceTransformerTrainer", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.add_dataset_name_transform", "name": "add_dataset_name_transform", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["batch", "dataset_name", "transform", "kwargs"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_dataset_name_transform of SentenceTransformerTrainer", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "add_model_card_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "default_args_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.add_model_card_callback", "name": "add_model_card_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "default_args_dict"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_model_card_callback of SentenceTransformerTrainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.args", "name": "args", "setter_type": null, "type": "sentence_transformers.training_args.SentenceTransformerTrainingArguments"}}, "call_model_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "trial"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.call_model_init", "name": "call_model_init", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "trial"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "call_model_init of SentenceTransformerTrainer", "ret_type": "sentence_transformers.SentenceTransformer.SentenceTransformer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "can_return_loss": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.can_return_loss", "name": "can_return_loss", "setter_type": null, "type": "builtins.bool"}}, "collect_features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.collect_features", "name": "collect_features", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "collect_features of SentenceTransformerTrainer", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "model", "inputs", "return_outputs", "num_items_in_batch"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.compute_loss", "name": "compute_loss", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "model", "inputs", "return_outputs", "num_items_in_batch"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", "sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_loss of SentenceTransformerTrainer", "ret_type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_model_card": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "language", "license", "tags", "model_name", "finetuned_from", "tasks", "dataset_tags", "dataset", "dataset_args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.create_model_card", "name": "create_model_card", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "language", "license", "tags", "model_name", "finetuned_from", "tasks", "dataset_tags", "dataset", "dataset_args", "kwargs"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_model_card of SentenceTransformerTrainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "data_collator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.data_collator", "name": "data_collator", "setter_type": null, "type": "sentence_transformers.data_collator.SentenceTransformerDataCollator"}}, "eval_dataset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.eval_dataset", "name": "eval_dataset", "setter_type": null, "type": {".class": "NoneType"}}}, "evaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "eval_dataset", "ignore_keys", "metric_key_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.evaluate", "name": "evaluate", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "eval_dataset", "ignore_keys", "metric_key_prefix"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Dataset", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Dataset", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluate of SentenceTransformerTrainer", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "evaluation_loop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "dataloader", "description", "prediction_loss_only", "ignore_keys", "metric_key_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.evaluation_loop", "name": "evaluation_loop", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "dataloader", "description", "prediction_loss_only", "ignore_keys", "metric_key_prefix"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataloader.DataLoader"}, "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evaluation_loop of SentenceTransformerTrainer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "transformers.trainer_utils.EvalLoopOutput"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "evaluator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.evaluator", "name": "evaluator", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceEvaluator"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "get_batch_sampler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "dataset", "batch_size", "drop_last", "valid_label_columns", "generator", "seed"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.get_batch_sampler", "name": "get_batch_sampler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "dataset", "batch_size", "drop_last", "valid_label_columns", "generator", "seed"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Dataset", "source_any": null, "type_of_any": 3}, "builtins.int", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["torch._C.Generator", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_batch_sampler of SentenceTransformerTrainer", "ret_type": {".class": "UnionType", "items": ["torch.utils.data.sampler.BatchSampler", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_eval_dataloader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "eval_dataset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.get_eval_dataloader", "name": "get_eval_dataloader", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "eval_dataset"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Dataset", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.DatasetDict", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.IterableDataset", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_eval_dataloader of SentenceTransformerTrainer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataloader.DataLoader"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_multi_dataset_batch_sampler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "dataset", "batch_samplers", "generator", "seed"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.get_multi_dataset_batch_sampler", "name": "get_multi_dataset_batch_sampler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "dataset", "batch_samplers", "generator", "seed"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataset.ConcatDataset"}, {".class": "Instance", "args": ["torch.utils.data.sampler.BatchSampler"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["torch._C.Generator", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_multi_dataset_batch_sampler of SentenceTransformerTrainer", "ret_type": "torch.utils.data.sampler.BatchSampler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_optimizer_cls_and_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "args", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.get_optimizer_cls_and_kwargs", "name": "get_optimizer_cls_and_kwargs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "args", "model"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", "sentence_transformers.training_args.SentenceTransformerTrainingArguments", {".class": "UnionType", "items": ["sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_optimizer_cls_and_kwargs of SentenceTransformerTrainer", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_test_dataloader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "test_dataset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.get_test_dataloader", "name": "get_test_dataloader", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "test_dataset"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Dataset", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.DatasetDict", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.IterableDataset", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_test_dataloader of SentenceTransformerTrainer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataloader.DataLoader"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_train_dataloader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.get_train_dataloader", "name": "get_train_dataloader", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_train_dataloader of SentenceTransformerTrainer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataloader.DataLoader"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "logs", "start_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.log", "name": "log", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "logs", "start_time"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "log of SentenceTransformerTrainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loss": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.loss", "name": "loss", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "maybe_add_dataset_name_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "dataset", "prompts", "router_mapping", "dataset_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.maybe_add_dataset_name_column", "name": "maybe_add_dataset_name_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "dataset", "prompts", "router_mapping", "dataset_name"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.DatasetDict", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Dataset", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "maybe_add_dataset_name_column of SentenceTransformerTrainer", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.DatasetDict", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Dataset", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.model", "name": "model", "setter_type": null, "type": "sentence_transformers.SentenceTransformer.SentenceTransformer"}}, "model_init": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.model_init", "name": "model_init", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "sentence_transformers.SentenceTransformer.SentenceTransformer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "override_model_in_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "loss", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.override_model_in_loss", "name": "override_model_in_loss", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "loss", "model"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", "torch.nn.modules.module.Module", "sentence_transformers.SentenceTransformer.SentenceTransformer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "override_model_in_loss of SentenceTransformerTrainer", "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prepare_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "loss", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.prepare_loss", "name": "prepare_loss", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "loss", "model"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sentence_transformers.SentenceTransformer.SentenceTransformer"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "torch.nn.modules.module.Module"], "uses_pep604_syntax": true}, "sentence_transformers.SentenceTransformer.SentenceTransformer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "prepare_loss of SentenceTransformerTrainer", "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "preprocess_dataset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "dataset", "prompts", "router_mapping", "dataset_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.preprocess_dataset", "name": "preprocess_dataset", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "dataset", "prompts", "router_mapping", "dataset_name"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.DatasetDict", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Dataset", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "preprocess_dataset of SentenceTransformerTrainer", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.DatasetDict", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Dataset", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "track_loss_components": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "loss"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.track_loss_components", "name": "track_loss_components", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "loss"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "track_loss_components of SentenceTransformerTrainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "train_dataset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.train_dataset", "name": "train_dataset", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.DatasetDict", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "validate_column_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dataset", "dataset_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.validate_column_names", "name": "validate_column_names", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dataset", "dataset_name"], "arg_types": ["sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Dataset", "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate_column_names of SentenceTransformerTrainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.trainer.SentenceTransformerTrainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.trainer.SentenceTransformerTrainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SentenceTransformerTrainingArguments": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.training_args.SentenceTransformerTrainingArguments", "kind": "Gdef"}, "SequentialEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.SequentialEvaluator", "kind": "Gdef"}, "TRAINING_ARGS_NAME": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer.TRAINING_ARGS_NAME", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Trainer": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.dummy_pt_objects.Trainer", "kind": "Gdef"}, "TrainerCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.TrainerCallback", "kind": "Gdef"}, "Value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.trainer.Value", "name": "Value", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.trainer.Value", "source_any": null, "type_of_any": 3}}}, "WandbCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.integration_utils.WandbCallback", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.trainer.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.trainer.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.trainer.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.trainer.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.trainer.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.trainer.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "disable_logging": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.disable_logging", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "is_datasets_available": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.is_datasets_available", "kind": "Gdef"}, "is_training_available": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.is_training_available", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.trainer.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "nullcontext": {".class": "SymbolTableNode", "cross_ref": "contextlib.nullcontext", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "parse_version": {".class": "SymbolTableNode", "cross_ref": "packaging.version.parse", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "transformers_version": {".class": "SymbolTableNode", "cross_ref": "transformers.__version__", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/trainer.py"}