{"data_mtime": 1752049734, "dep_lines": [31, 32, 33, 16, 17, 27, 37, 1, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 16, 19, 28, 30, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 254, 18, 253, 305, 304], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 10, 20, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 5, 20, 20, 20], "dependencies": ["sentence_transformers.cross_encoder.fit_mixin", "sentence_transformers.cross_encoder.model_card", "sentence_transformers.cross_encoder.util", "packaging.version", "torch.nn", "transformers.utils", "sentence_transformers.util", "__future__", "json", "logging", "os", "tempfile", "traceback", "fnmatch", "pathlib", "typing", "huggingface_hub", "numpy", "torch", "packaging", "transformers", "typing_extensions", "sentence_transformers", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "functools", "huggingface_hub.repocard_data", "io", "json.decoder", "sentence_transformers.model_card", "torch._C", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.nn.modules", "torch.nn.modules.module", "torch.utils", "torch.utils._contextlib", "transformers.configuration_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.modeling_utils", "transformers.models", "transformers.models.auto", "transformers.models.auto.auto_factory", "transformers.models.auto.configuration_auto", "transformers.models.auto.modeling_auto", "transformers.models.auto.tokenization_auto", "transformers.tokenization_utils", "transformers.tokenization_utils_base", "transformers.utils.hub", "types", "warnings"], "hash": "512fb2de6d01f7b85be9277d09a0c957ad159eb1", "id": "sentence_transformers.cross_encoder.CrossEncoder", "ignore_all": true, "interface_hash": "139599c0d3a0ad3f1a26855794920387b28aaed0", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/cross_encoder/CrossEncoder.py", "plugin_data": null, "size": 46765, "suppressed": ["optimum.intel.openvino", "tqdm.autonotebook", "optimum.intel", "optimum.onnxruntime", "onnxruntime"], "version_id": "1.16.1"}