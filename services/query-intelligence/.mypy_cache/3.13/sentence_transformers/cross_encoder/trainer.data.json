{".class": "MypyFile", "_fullname": "sentence_transformers.cross_encoder.trainer", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BinaryCrossEntropyLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.BinaryCrossEntropyLoss", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CrossEncoder": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.CrossEncoder", "kind": "Gdef"}, "CrossEncoderDataCollator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.data_collator.CrossEncoderDataCollator", "kind": "Gdef"}, "CrossEncoderModelCardCallback": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.model_card.CrossEncoderModelCardCallback", "kind": "Gdef"}, "CrossEncoderTrainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sentence_transformers.trainer.SentenceTransformerTrainer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer", "name": "CrossEncoderTrainer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer", "has_param_spec_type": false, "metaclass_type": "transformers.utils.import_utils.DummyObject", "metadata": {}, "module_name": "sentence_transformers.cross_encoder.trainer", "mro": ["sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer", "sentence_transformers.trainer.SentenceTransformerTrainer", "transformers.utils.dummy_pt_objects.Trainer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "model", "args", "train_dataset", "eval_dataset", "loss", "evaluator", "data_collator", "tokenizer", "model_init", "compute_metrics", "callbacks", "optimizers", "preprocess_logits_for_metrics"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "model", "args", "train_dataset", "eval_dataset", "loss", "evaluator", "data_collator", "tokenizer", "model_init", "compute_metrics", "callbacks", "optimizers", "preprocess_logits_for_metrics"], "arg_types": ["sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer", {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "CrossEncoder"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["sentence_transformers.cross_encoder.training_args.CrossEncoderTrainingArguments", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.cross_encoder.trainer.Dataset", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.cross_encoder.trainer.DatasetDict", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "sentence_transformers.cross_encoder.trainer.Dataset", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.cross_encoder.trainer.Dataset", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.cross_encoder.trainer.DatasetDict", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "sentence_transformers.cross_encoder.trainer.Dataset", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "Instance", "args": ["builtins.str", "torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "CrossEncoder"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "CrossEncoder"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceEvaluator"}, {".class": "Instance", "args": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceEvaluator"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["transformers.data.data_collator.DataCollator", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["transformers.tokenization_utils_base.PreTrainedTokenizerBase", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "CrossEncoder"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["transformers.trainer_utils.EvalPrediction"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["transformers.trainer_callback.TrainerCallback"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TupleType", "implicit": false, "items": ["torch.optim.optimizer.Optimizer", "torch.optim.lr_scheduler.LambdaLR"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CrossEncoderTrainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_include_prompt_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer._include_prompt_length", "name": "_include_prompt_length", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_include_prompt_length of CrossEncoderTrainer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_best_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer._load_best_model", "name": "_load_best_model", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_load_best_model of CrossEncoderTrainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_from_checkpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "checkpoint_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer._load_from_checkpoint", "name": "_load_from_checkpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "checkpoint_path"], "arg_types": ["sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_load_from_checkpoint of CrossEncoderTrainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prompt_length_mapping": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer._prompt_length_mapping", "name": "_prompt_length_mapping", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "add_model_card_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "default_args_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer.add_model_card_callback", "name": "add_model_card_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "default_args_dict"], "arg_types": ["sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_model_card_callback of CrossEncoderTrainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer.args", "name": "args", "setter_type": null, "type": "sentence_transformers.cross_encoder.training_args.CrossEncoderTrainingArguments"}}, "collect_features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer.collect_features", "name": "collect_features", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "inputs"], "arg_types": ["sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "collect_features of CrossEncoderTrainer", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "data_collator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer.data_collator", "name": "data_collator", "setter_type": null, "type": "sentence_transformers.cross_encoder.data_collator.CrossEncoderDataCollator"}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer.model", "name": "model", "setter_type": null, "type": {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "CrossEncoder"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CrossEncoderTrainingArguments": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.training_args.CrossEncoderTrainingArguments", "kind": "Gdef"}, "CrossEntropyLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.CrossEntropyLoss", "kind": "Gdef"}, "DataCollator": {".class": "SymbolTableNode", "cross_ref": "transformers.data.data_collator.DataCollator", "kind": "Gdef"}, "Dataset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.cross_encoder.trainer.Dataset", "name": "Dataset", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.cross_encoder.trainer.Dataset", "source_any": null, "type_of_any": 3}}}, "DatasetDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.cross_encoder.trainer.DatasetDict", "name": "DatasetDict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.cross_encoder.trainer.DatasetDict", "source_any": null, "type_of_any": 3}}}, "EvalPrediction": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.EvalPrediction", "kind": "Gdef"}, "IterableDataset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.cross_encoder.trainer.IterableDataset", "name": "IterableDataset", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.cross_encoder.trainer.IterableDataset", "source_any": null, "type_of_any": 3}}}, "PreTrainedTokenizerBase": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.PreTrainedTokenizerBase", "kind": "Gdef"}, "SentenceEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.SentenceEvaluator", "kind": "Gdef"}, "SentenceTransformerTrainer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.trainer.SentenceTransformerTrainer", "kind": "Gdef"}, "SequentialEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.SequentialEvaluator", "kind": "Gdef"}, "TrainerCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.TrainerCallback", "kind": "Gdef"}, "WandbCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.integration_utils.WandbCallback", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.trainer.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.trainer.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.trainer.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.trainer.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.trainer.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.trainer.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "is_datasets_available": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.is_datasets_available", "kind": "Gdef"}, "is_training_available": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.is_training_available", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.cross_encoder.trainer.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "parse_version": {".class": "SymbolTableNode", "cross_ref": "packaging.version.parse", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "transformers_version": {".class": "SymbolTableNode", "cross_ref": "transformers.__version__", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/cross_encoder/trainer.py"}