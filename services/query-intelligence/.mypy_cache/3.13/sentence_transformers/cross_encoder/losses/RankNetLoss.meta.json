{"data_mtime": 1752049734, "dep_lines": [8, 7, 8, 5, 7, 1, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 10, 20, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sentence_transformers.cross_encoder.losses.LambdaLoss", "sentence_transformers.cross_encoder.CrossEncoder", "sentence_transformers.cross_encoder.losses", "torch.nn", "sentence_transformers.cross_encoder", "__future__", "typing", "torch", "builtins", "_frozen_importlib", "abc", "sentence_transformers.cross_encoder.fit_mixin", "torch.nn.modules", "torch.nn.modules.linear", "torch.nn.modules.module", "transformers", "transformers.utils", "transformers.utils.hub"], "hash": "ec30c57ad1eb36639836c9c973333606eb82942c", "id": "sentence_transformers.cross_encoder.losses.RankNetLoss", "ignore_all": true, "interface_hash": "bb8b9ce55f0284f365bbcdbe70314e019f72198a", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/cross_encoder/losses/RankNetLoss.py", "plugin_data": null, "size": 6034, "suppressed": [], "version_id": "1.16.1"}