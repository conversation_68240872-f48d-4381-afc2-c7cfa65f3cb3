{".class": "MypyFile", "_fullname": "sentence_transformers.cross_encoder.losses", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BinaryCrossEntropyLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.BinaryCrossEntropyLoss.BinaryCrossEntropyLoss", "kind": "Gdef"}, "CachedMultipleNegativesRankingLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.CachedMultipleNegativesRankingLoss.CachedMultipleNegativesRankingLoss", "kind": "Gdef"}, "CrossEntropyLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.CrossEntropyLoss.CrossEntropyLoss", "kind": "Gdef"}, "LambdaLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss", "kind": "Gdef"}, "LambdaRankScheme": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaRankScheme", "kind": "Gdef"}, "ListMLELoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.ListMLELoss.ListMLELoss", "kind": "Gdef"}, "ListNetLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.ListNetLoss.ListNetLoss", "kind": "Gdef"}, "MSELoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.MSELoss.MSELoss", "kind": "Gdef"}, "MarginMSELoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.MarginMSELoss.MarginMSELoss", "kind": "Gdef"}, "MultipleNegativesRankingLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.MultipleNegativesRankingLoss.MultipleNegativesRankingLoss", "kind": "Gdef"}, "NDCGLoss1Scheme": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss1Scheme", "kind": "Gdef"}, "NDCGLoss2PPScheme": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2PPScheme", "kind": "Gdef"}, "NDCGLoss2Scheme": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2Scheme", "kind": "Gdef"}, "NoWeightingScheme": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.LambdaLoss.NoWeightingScheme", "kind": "Gdef"}, "PListMLELambdaWeight": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.PListMLELoss.PListMLELambdaWeight", "kind": "Gdef"}, "PListMLELoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.PListMLELoss.PListMLELoss", "kind": "Gdef"}, "RankNetLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.losses.RankNetLoss.RankNetLoss", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.cross_encoder.losses.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.losses.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.losses.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.losses.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.losses.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.losses.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.losses.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.losses.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/cross_encoder/losses/__init__.py"}