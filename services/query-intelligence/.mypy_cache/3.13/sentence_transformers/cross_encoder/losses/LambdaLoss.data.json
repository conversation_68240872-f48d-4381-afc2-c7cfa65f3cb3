{".class": "MypyFile", "_fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseWeightingScheme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme", "name": "BaseWeightingScheme", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.cross_encoder.losses.LambdaLoss", "mro": ["sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BaseWeightingScheme", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "gain", "discount", "true_sorted"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "gain", "discount", "true_sorted"], "arg_types": ["sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme", "torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of BaseWeightingScheme", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CrossEncoder": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.CrossEncoder.CrossEncoder", "kind": "Gdef"}, "LambdaLoss": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss", "name": "LambdaLoss", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.cross_encoder.losses.LambdaLoss", "mro": ["sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "model", "weighting_scheme", "k", "sigma", "eps", "reduction_log", "activation_fn", "mini_batch_size"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "model", "weighting_scheme", "k", "sigma", "eps", "reduction_log", "activation_fn", "mini_batch_size"], "arg_types": ["sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss", "sentence_transformers.cross_encoder.CrossEncoder.CrossEncoder", {".class": "UnionType", "items": ["sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "natural"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binary"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of LambdaLoss", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "activation_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss.activation_fn", "name": "activation_fn", "setter_type": null, "type": "torch.nn.modules.module.Module"}}, "citation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss.citation", "name": "citation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "citation of LambdaLoss", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss.citation", "name": "citation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "citation of LambdaLoss", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "eps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss.eps", "name": "eps", "setter_type": null, "type": "builtins.float"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "inputs", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "inputs", "labels"], "arg_types": ["sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of LambdaLoss", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_config_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss.get_config_dict", "name": "get_config_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_config_dict of LambdaLoss", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.float", "builtins.int", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "k": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss.k", "name": "k", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "mini_batch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss.mini_batch_size", "name": "mini_batch_size", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss.model", "name": "model", "setter_type": null, "type": "sentence_transformers.cross_encoder.CrossEncoder.CrossEncoder"}}, "reduction_log": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss.reduction_log", "name": "reduction_log", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "natural"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binary"}], "uses_pep604_syntax": false}}}, "sigma": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss.sigma", "name": "sigma", "setter_type": null, "type": "builtins.float"}}, "weighting_scheme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss.weighting_scheme", "name": "weighting_scheme", "setter_type": null, "type": "sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaLoss", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LambdaRankScheme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaRankScheme", "name": "LambdaRankScheme", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaRankScheme", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.cross_encoder.losses.LambdaLoss", "mro": ["sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaRankScheme", "sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "gain", "discount", "true_sorted"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaRankScheme.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "gain", "discount", "true_sorted"], "arg_types": ["sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaRankScheme", "torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of LambdaRankScheme", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaRankScheme.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaRankScheme", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "NDCGLoss1Scheme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss1Scheme", "name": "NDCGLoss1Scheme", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss1Scheme", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.cross_encoder.losses.LambdaLoss", "mro": ["sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss1Scheme", "sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "gain", "discount", "true_sorted"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss1Scheme.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "gain", "discount", "true_sorted"], "arg_types": ["sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss1Scheme", "torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of NDCGLoss1Scheme", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss1Scheme.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss1Scheme", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NDCGLoss2PPScheme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2PPScheme", "name": "NDCGLoss2PPScheme", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2PPScheme", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.cross_encoder.losses.LambdaLoss", "mro": ["sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2PPScheme", "sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "mu"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2PPScheme.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "mu"], "arg_types": ["sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2PPScheme", "builtins.float"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of NDCGLoss2PPScheme", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "gain", "discount", "true_sorted"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2PPScheme.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "gain", "discount", "true_sorted"], "arg_types": ["sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2PPScheme", "torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of NDCGLoss2PPScheme", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lambda_rank": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2PPScheme.lambda_rank", "name": "lambda_rank", "setter_type": null, "type": "sentence_transformers.cross_encoder.losses.LambdaLoss.LambdaRankScheme"}}, "mu": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2PPScheme.mu", "name": "mu", "setter_type": null, "type": "builtins.float"}}, "ndcg_loss2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2PPScheme.ndcg_loss2", "name": "ndcg_loss2", "setter_type": null, "type": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2Scheme"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2PPScheme.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2PPScheme", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NDCGLoss2Scheme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2Scheme", "name": "NDCGLoss2Scheme", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2Scheme", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.cross_encoder.losses.LambdaLoss", "mro": ["sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2Scheme", "sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "gain", "discount", "true_sorted"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2Scheme.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "gain", "discount", "true_sorted"], "arg_types": ["sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2Scheme", "torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of NDCGLoss2Scheme", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2Scheme.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.cross_encoder.losses.LambdaLoss.NDCGLoss2Scheme", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoWeightingScheme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NoWeightingScheme", "name": "NoWeightingScheme", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NoWeightingScheme", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.cross_encoder.losses.LambdaLoss", "mro": ["sentence_transformers.cross_encoder.losses.LambdaLoss.NoWeightingScheme", "sentence_transformers.cross_encoder.losses.LambdaLoss.BaseWeightingScheme", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "gain", "discount", "true_sorted"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NoWeightingScheme.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "gain", "discount", "true_sorted"], "arg_types": ["sentence_transformers.cross_encoder.losses.LambdaLoss.NoWeightingScheme", "torch._tensor.Tensor", "torch._tensor.Tensor", "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of NoWeightingScheme", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.NoWeightingScheme.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.cross_encoder.losses.LambdaLoss.NoWeightingScheme", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tensor": {".class": "SymbolTableNode", "cross_ref": "torch._tensor.Tensor", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.cross_encoder.losses.LambdaLoss.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "fullname": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.fullname", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/cross_encoder/losses/LambdaLoss.py"}