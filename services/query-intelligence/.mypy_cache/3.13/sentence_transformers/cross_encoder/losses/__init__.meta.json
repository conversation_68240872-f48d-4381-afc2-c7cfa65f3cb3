{"data_mtime": 1752049734, "dep_lines": [3, 4, 5, 6, 14, 15, 16, 17, 18, 19, 20, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["sentence_transformers.cross_encoder.losses.BinaryCrossEntropyLoss", "sentence_transformers.cross_encoder.losses.CachedMultipleNegativesRankingLoss", "sentence_transformers.cross_encoder.losses.CrossEntropyLoss", "sentence_transformers.cross_encoder.losses.LambdaLoss", "sentence_transformers.cross_encoder.losses.ListMLELoss", "sentence_transformers.cross_encoder.losses.ListNetLoss", "sentence_transformers.cross_encoder.losses.MarginMSELoss", "sentence_transformers.cross_encoder.losses.MSELoss", "sentence_transformers.cross_encoder.losses.MultipleNegativesRankingLoss", "sentence_transformers.cross_encoder.losses.PListMLELoss", "sentence_transformers.cross_encoder.losses.RankNetLoss", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "f249f16d6e7909817deee0ec55dfe9f0272d59b5", "id": "sentence_transformers.cross_encoder.losses", "ignore_all": true, "interface_hash": "0abd60ef18051e933ae3e40a0c93cf132c0e5634", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/cross_encoder/losses/__init__.py", "plugin_data": null, "size": 1158, "suppressed": [], "version_id": "1.16.1"}