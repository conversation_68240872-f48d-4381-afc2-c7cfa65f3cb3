{"data_mtime": 1752049734, "dep_lines": [254, 255, 13, 18, 19, 20, 21, 22, 31, 256, 10, 11, 12, 16, 22, 23, 24, 25, 1, 3, 4, 5, 6, 8, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 14, 28], "dep_prios": [20, 20, 5, 5, 5, 5, 5, 10, 25, 20, 10, 10, 5, 5, 20, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["sentence_transformers.cross_encoder.losses.BinaryCrossEntropyLoss", "sentence_transformers.cross_encoder.losses.CrossEntropyLoss", "torch.utils.data", "sentence_transformers.cross_encoder.training_args", "sentence_transformers.datasets.NoDuplicatesDataLoader", "sentence_transformers.datasets.SentenceLabelDataset", "sentence_transformers.evaluation.SentenceEvaluator", "sentence_transformers.readers.InputExample", "sentence_transformers.cross_encoder.CrossEncoder", "sentence_transformers.cross_encoder.trainer", "packaging.version", "torch.nn", "torch.optim", "transformers.tokenization_utils_base", "sentence_transformers.readers", "sentence_transformers.SentenceTransformer", "sentence_transformers.training_args", "sentence_transformers.util", "__future__", "logging", "os", "pathlib", "typing", "torch", "transformers", "packaging", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "collections", "enum", "functools", "huggingface_hub", "huggingface_hub.repocard_data", "posixpath", "sentence_transformers.cross_encoder.losses", "sentence_transformers.datasets", "sentence_transformers.evaluation", "sentence_transformers.fit_mixin", "sentence_transformers.model_card", "sentence_transformers.peft_mixin", "sentence_transformers.similarity_functions", "sentence_transformers.trainer", "torch._C", "torch._tensor", "torch.amp", "torch.amp.autocast_mode", "torch.amp.grad_scaler", "torch.cuda", "torch.cuda.amp", "torch.cuda.amp.grad_scaler", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.linear", "torch.nn.modules.loss", "torch.nn.modules.module", "torch.nn.parameter", "torch.nn.utils", "torch.optim.adam", "torch.optim.adamw", "torch.optim.lr_scheduler", "torch.optim.optimizer", "torch.utils", "torch.utils.data._utils", "torch.utils.data._utils.collate", "torch.utils.data.dataloader", "torch.utils.data.dataset", "transformers.configuration_utils", "transformers.data", "transformers.data.data_collator", "transformers.debug_utils", "transformers.trainer_callback", "transformers.trainer_utils", "transformers.training_args", "transformers.utils", "transformers.utils.dummy_pt_objects", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "ea06ae68e9d326a80431da65c8127d02308ecdd3", "id": "sentence_transformers.cross_encoder.fit_mixin", "ignore_all": true, "interface_hash": "ddb3c22addd65acb60e2affeefaecd8cf929dc67", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/cross_encoder/fit_mixin.py", "plugin_data": null, "size": 25378, "suppressed": ["tqdm.autonotebook", "datasets"], "version_id": "1.16.1"}