{"data_mtime": 1752049734, "dep_lines": [18, 18, 13, 16, 17, 18, 19, 20, 21, 21, 9, 10, 14, 16, 21, 22, 23, 1, 3, 4, 5, 6, 8, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 26], "dep_prios": [10, 10, 5, 10, 5, 20, 5, 5, 10, 10, 5, 10, 5, 20, 20, 5, 5, 5, 10, 10, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["sentence_transformers.cross_encoder.losses.BinaryCrossEntropyLoss", "sentence_transformers.cross_encoder.losses.CrossEntropyLoss", "transformers.data.data_collator", "sentence_transformers.cross_encoder.CrossEncoder", "sentence_transformers.cross_encoder.data_collator", "sentence_transformers.cross_encoder.losses", "sentence_transformers.cross_encoder.model_card", "sentence_transformers.cross_encoder.training_args", "sentence_transformers.evaluation.SentenceEvaluator", "sentence_transformers.evaluation.SequentialEvaluator", "packaging.version", "torch.nn", "transformers.integrations", "sentence_transformers.cross_encoder", "sentence_transformers.evaluation", "sentence_transformers.trainer", "sentence_transformers.util", "__future__", "logging", "os", "functools", "typing", "torch", "transformers", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "huggingface_hub", "huggingface_hub.repocard_data", "packaging", "sentence_transformers.SentenceTransformer", "sentence_transformers.data_collator", "sentence_transformers.fit_mixin", "sentence_transformers.model_card", "sentence_transformers.peft_mixin", "sentence_transformers.training_args", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "torch.optim", "torch.optim.lr_scheduler", "torch.optim.optimizer", "transformers.data", "transformers.debug_utils", "transformers.integrations.integration_utils", "transformers.tokenization_utils_base", "transformers.trainer_callback", "transformers.trainer_utils", "transformers.training_args", "transformers.utils", "transformers.utils.dummy_pt_objects", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "ed605dac7e952cd7e2d7bbb63b032b74b07941c6", "id": "sentence_transformers.cross_encoder.trainer", "ignore_all": true, "interface_hash": "8e6537f1ad671913908713161d5fbc2ecbb64394", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/cross_encoder/trainer.py", "plugin_data": null, "size": 20640, "suppressed": ["datasets"], "version_id": "1.16.1"}