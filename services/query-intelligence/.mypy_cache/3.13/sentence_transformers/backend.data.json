{".class": "MypyFile", "_fullname": "sentence_transformers.backend", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CrossEncoder": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.CrossEncoder.CrossEncoder", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "OVQuantizationConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.backend.OVQuantizationConfig", "name": "OVQuantizationConfig", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.backend.OVQuantizationConfig", "source_any": null, "type_of_any": 3}}}, "OptimizationConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.backend.OptimizationConfig", "name": "OptimizationConfig", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.backend.OptimizationConfig", "source_any": null, "type_of_any": 3}}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "QuantizationConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.backend.QuantizationConfig", "name": "QuantizationConfig", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.backend.QuantizationConfig", "source_any": null, "type_of_any": 3}}}, "SentenceTransformer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.SentenceTransformer", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.backend.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.backend.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.backend.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.backend.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.backend.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.backend.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "disable_datasets_caching": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.disable_datasets_caching", "kind": "Gdef"}, "export_dynamic_quantized_onnx_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["model", "quantization_config", "model_name_or_path", "push_to_hub", "create_pr", "file_suffix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.backend.export_dynamic_quantized_onnx_model", "name": "export_dynamic_quantized_onnx_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["model", "quantization_config", "model_name_or_path", "push_to_hub", "create_pr", "file_suffix"], "arg_types": [{".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceTransformer"}, "sentence_transformers.cross_encoder.CrossEncoder.CrossEncoder"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.backend.QuantizationConfig", "source_any": null, "type_of_any": 3}, {".class": "LiteralType", "fallback": "builtins.str", "value": "arm64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "avx2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "avx512"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "avx512_vnni"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "export_dynamic_quantized_onnx_model", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "export_optimized_onnx_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["model", "optimization_config", "model_name_or_path", "push_to_hub", "create_pr", "file_suffix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.backend.export_optimized_onnx_model", "name": "export_optimized_onnx_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["model", "optimization_config", "model_name_or_path", "push_to_hub", "create_pr", "file_suffix"], "arg_types": [{".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceTransformer"}, "sentence_transformers.cross_encoder.CrossEncoder.CrossEncoder"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.backend.OptimizationConfig", "source_any": null, "type_of_any": 3}, {".class": "LiteralType", "fallback": "builtins.str", "value": "O1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "O2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "O3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "O4"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "export_optimized_onnx_model", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "export_static_quantized_openvino_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["model", "quantization_config", "model_name_or_path", "dataset_name", "dataset_config_name", "dataset_split", "column_name", "push_to_hub", "create_pr", "file_suffix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.backend.export_static_quantized_openvino_model", "name": "export_static_quantized_openvino_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["model", "quantization_config", "model_name_or_path", "dataset_name", "dataset_config_name", "dataset_split", "column_name", "push_to_hub", "create_pr", "file_suffix"], "arg_types": [{".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceTransformer"}, "sentence_transformers.cross_encoder.CrossEncoder.CrossEncoder"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.backend.OVQuantizationConfig", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "export_static_quantized_openvino_model", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "huggingface_hub": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub", "kind": "Gdef"}, "is_datasets_available": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.is_datasets_available", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.backend.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "save_or_push_to_hub_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["export_function", "export_function_name", "config", "model_name_or_path", "push_to_hub", "create_pr", "file_suffix", "backend", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.backend.save_or_push_to_hub_model", "name": "save_or_push_to_hub_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["export_function", "export_function_name", "config", "model_name_or_path", "push_to_hub", "create_pr", "file_suffix", "backend", "model"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceTransformer"}, "sentence_transformers.cross_encoder.CrossEncoder.CrossEncoder", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "save_or_push_to_hub_model", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/backend.py"}