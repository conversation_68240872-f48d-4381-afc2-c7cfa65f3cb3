{"data_mtime": 1752049734, "dep_lines": [32, 32, 42, 526, 21, 22, 23, 26, 27, 28, 32, 33, 34, 43, 44, 526, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 29, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 24, 37], "dep_prios": [10, 10, 25, 20, 5, 5, 10, 5, 5, 5, 20, 5, 5, 25, 25, 20, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["sentence_transformers.models.Router", "sentence_transformers.models.StaticEmbedding", "sentence_transformers.evaluation.SentenceEvaluator", "sentence_transformers.evaluation.SequentialEvaluator", "huggingface_hub.repocard_data", "huggingface_hub.utils", "torch.nn", "transformers.integrations", "transformers.modelcard", "transformers.trainer_callback", "sentence_transformers.models", "sentence_transformers.training_args", "sentence_transformers.util", "sentence_transformers.SentenceTransformer", "sentence_transformers.trainer", "sentence_transformers.evaluation", "__future__", "json", "logging", "random", "re", "collections", "copy", "dataclasses", "pathlib", "platform", "pprint", "textwrap", "typing", "torch", "transformers", "huggingface_hub", "typing_extensions", "sentence_transformers", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "contextlib", "enum", "fractions", "huggingface_hub.hf_api", "numbers", "numpy", "numpy._typing", "numpy._typing._nbit_base", "os", "sentence_transformers.fit_mixin", "sentence_transformers.models.InputModule", "sentence_transformers.models.Module", "sentence_transformers.peft_mixin", "torch._C", "torch._tensor", "torch._tensor_str", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "transformers.integrations.integration_utils", "transformers.training_args", "transformers.utils", "transformers.utils.dummy_pt_objects", "transformers.utils.import_utils", "types", "warnings"], "hash": "cd9bb82fb9c851c945e984cc35798b954a720d40", "id": "sentence_transformers.model_card", "ignore_all": true, "interface_hash": "44c1b77defcb472c228e0b246addeac5f8c8d01c", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/model_card.py", "plugin_data": null, "size": 53885, "suppressed": ["tqdm.autonotebook", "datasets"], "version_id": "1.16.1"}