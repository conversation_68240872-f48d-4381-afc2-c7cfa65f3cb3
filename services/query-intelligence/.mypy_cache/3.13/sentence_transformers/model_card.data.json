{".class": "MypyFile", "_fullname": "sentence_transformers.model_card", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CardData": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.repocard_data.CardData", "kind": "Gdef"}, "CodeCarbonCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.integration_utils.CodeCarbonCallback", "kind": "Gdef"}, "Counter": {".class": "SymbolTableNode", "cross_ref": "collections.Counter", "kind": "Gdef"}, "Dataset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.model_card.Dataset", "name": "Dataset", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.model_card.Dataset", "source_any": null, "type_of_any": 3}}}, "DatasetDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.model_card.DatasetDict", "name": "DatasetDict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.model_card.DatasetDict", "source_any": null, "type_of_any": 3}}}, "EvalResult": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.repocard_data.EvalResult", "kind": "Gdef"}, "IGNORED_FIELDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.model_card.IGNORED_FIELDS", "name": "IGNORED_FIELDS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "IterableDataset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.model_card.IterableDataset", "name": "IterableDataset", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.model_card.IterableDataset", "source_any": null, "type_of_any": 3}}}, "IterableDatasetDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.model_card.IterableDatasetDict", "name": "IterableDatasetDict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.model_card.IterableDatasetDict", "source_any": null, "type_of_any": 3}}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "ModelCard": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.repocard.ModelCard", "kind": "Gdef"}, "ModelCardCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sentence_transformers.model_card.SentenceTransformerModelCardCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.model_card.ModelCardCallback", "name": "ModelCardCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": "class sentence_transformers.model_card.ModelCardCallback is deprecated: The `ModelCardCallback` has been renamed to `SentenceTransformerModelCardCallback` and the former is now deprecated. Please use `SentenceTransformerModelCardCallback` instead.", "flags": [], "fullname": "sentence_transformers.model_card.ModelCardCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.model_card", "mro": ["sentence_transformers.model_card.ModelCardCallback", "sentence_transformers.model_card.SentenceTransformerModelCardCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.model_card.ModelCardCallback.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.model_card.ModelCardCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.model_card.ModelCardCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Router": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.Router.Router", "kind": "Gdef"}, "SentenceEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.SentenceEvaluator.SentenceEvaluator", "kind": "Gdef"}, "SentenceTransformer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.SentenceTransformer.SentenceTransformer", "kind": "Gdef"}, "SentenceTransformerModelCardCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_callback.TrainerCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardCallback", "name": "SentenceTransformerModelCardCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.model_card", "mro": ["sentence_transformers.model_card.SentenceTransformerModelCardCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "default_args_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardCallback.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "default_args_dict"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardCallback", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SentenceTransformerModelCardCallback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_args_dict": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardCallback.default_args_dict", "name": "default_args_dict", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "on_evaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "model", "metrics", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardCallback.on_evaluate", "name": "on_evaluate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "model", "metrics", "kwargs"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardCallback", "sentence_transformers.training_args.SentenceTransformerTrainingArguments", "transformers.trainer_callback.TrainerState", "transformers.trainer_callback.TrainerControl", "sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_evaluate of SentenceTransformerModelCardCallback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_init_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "model", "trainer", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardCallback.on_init_end", "name": "on_init_end", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "model", "trainer", "kwargs"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardCallback", "sentence_transformers.training_args.SentenceTransformerTrainingArguments", "transformers.trainer_callback.TrainerState", "transformers.trainer_callback.TrainerControl", "sentence_transformers.SentenceTransformer.SentenceTransformer", "sentence_transformers.trainer.SentenceTransformerTrainer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_init_end of SentenceTransformerModelCardCallback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "model", "logs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardCallback.on_log", "name": "on_log", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "model", "logs", "kwargs"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardCallback", "sentence_transformers.training_args.SentenceTransformerTrainingArguments", "transformers.trainer_callback.TrainerState", "transformers.trainer_callback.TrainerControl", "sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_log of SentenceTransformerModelCardCallback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_train_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardCallback.on_train_begin", "name": "on_train_begin", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "model", "kwargs"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardCallback", "sentence_transformers.training_args.SentenceTransformerTrainingArguments", "transformers.trainer_callback.TrainerState", "transformers.trainer_callback.TrainerControl", "sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_train_begin of SentenceTransformerModelCardCallback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.model_card.SentenceTransformerModelCardCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SentenceTransformerModelCardData": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["huggingface_hub.repocard_data.CardData"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData", "name": "SentenceTransformerModelCardData", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 310, "name": "language", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 311, "name": "license", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 312, "name": "model_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 313, "name": "model_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 314, "name": "train_datasets", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 315, "name": "eval_datasets", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 316, "name": "task_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 319, "name": "tags", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 327, "name": "local_files_only", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 328, "name": "generate_widget_examples", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 331, "name": "base_model", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 332, "name": "base_model_revision", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 333, "name": "non_default_hyperparameters", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 334, "name": "all_hyperparameters", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 335, "name": "eval_results_dict", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["sentence_transformers.evaluation.SentenceEvaluator.SentenceEvaluator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 336, "name": "training_logs", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 337, "name": "widget", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 338, "name": "predict_example", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 339, "name": "label_example_list", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 340, "name": "code_carbon_callback", "type": {".class": "UnionType", "items": ["transformers.integrations.integration_utils.CodeCarbonCallback", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 341, "name": "citations", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 342, "name": "best_model_step", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 343, "name": "datasets", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 344, "name": "ir_model", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 345, "name": "similarities", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 348, "name": "first_save", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 349, "name": "widget_step", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 352, "name": "pipeline_tag", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 353, "name": "library_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 354, "name": "version", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 355, "name": "template_path", "type": "pathlib.Path"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 358, "name": "model", "type": {".class": "UnionType", "items": ["sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "NoneType"}], "uses_pep604_syntax": true}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "sentence_transformers.model_card", "mro": ["sentence_transformers.model_card.SentenceTransformerModelCardData", "huggingface_hub.repocard_data.CardData", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "language", "license", "model_name", "model_id", "train_datasets", "eval_datasets", "task_name", "tags", "local_files_only", "generate_widget_examples"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "language", "license", "model_name", "model_id", "train_datasets", "eval_datasets", "task_name", "tags", "local_files_only", "generate_widget_examples"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__init__-redefinition": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.model_card.SentenceTransformerModelCardData.__init__", "kind": "<PERSON><PERSON><PERSON>", "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "language"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "license"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "train_datasets"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval_datasets"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "task_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tags"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "local_files_only"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "generate_widget_examples"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__match_args__-redefinition": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.model_card.SentenceTransformerModelCardData.__match_args__", "kind": "<PERSON><PERSON><PERSON>", "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-post_init of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-post_init-redefinition": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.model_card.SentenceTransformerModelCardData.__mypy-post_init", "kind": "<PERSON><PERSON><PERSON>", "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["language", "license", "model_name", "model_id", "train_datasets", "eval_datasets", "task_name", "tags", "local_files_only", "generate_widget_examples", "base_model", "base_model_revision", "non_default_hyperparameters", "all_hyperparameters", "eval_results_dict", "training_logs", "widget", "predict_example", "label_example_list", "code_carbon_callback", "citations", "best_model_step", "datasets", "ir_model", "similarities", "first_save", "widget_step", "pipeline_tag", "library_name", "version", "template_path", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["language", "license", "model_name", "model_id", "train_datasets", "eval_datasets", "task_name", "tags", "local_files_only", "generate_widget_examples", "base_model", "base_model_revision", "non_default_hyperparameters", "all_hyperparameters", "eval_results_dict", "training_logs", "widget", "predict_example", "label_example_list", "code_carbon_callback", "citations", "best_model_step", "datasets", "ir_model", "similarities", "first_save", "widget_step", "pipeline_tag", "library_name", "version", "template_path", "model"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["sentence_transformers.evaluation.SentenceEvaluator.SentenceEvaluator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["transformers.integrations.integration_utils.CodeCarbonCallback", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "pathlib.Path", {".class": "UnionType", "items": ["sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["language", "license", "model_name", "model_id", "train_datasets", "eval_datasets", "task_name", "tags", "local_files_only", "generate_widget_examples", "base_model", "base_model_revision", "non_default_hyperparameters", "all_hyperparameters", "eval_results_dict", "training_logs", "widget", "predict_example", "label_example_list", "code_carbon_callback", "citations", "best_model_step", "datasets", "ir_model", "similarities", "first_save", "widget_step", "pipeline_tag", "library_name", "version", "template_path", "model"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["sentence_transformers.evaluation.SentenceEvaluator.SentenceEvaluator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["transformers.integrations.integration_utils.CodeCarbonCallback", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.int", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "pathlib.Path", {".class": "UnionType", "items": ["sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__mypy-replace-redefinition": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.model_card.SentenceTransformerModelCardData.__mypy-replace", "kind": "<PERSON><PERSON><PERSON>", "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.__post_init__", "name": "__post_init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__post_init__ of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__replace__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "language", "license", "model_name", "model_id", "train_datasets", "eval_datasets", "task_name", "tags", "local_files_only", "generate_widget_examples"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.__replace__", "name": "__replace__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "language", "license", "model_name", "model_id", "train_datasets", "eval_datasets", "task_name", "tags", "local_files_only", "generate_widget_examples"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__replace__ of SentenceTransformerModelCardData", "ret_type": "sentence_transformers.model_card.SentenceTransformerModelCardData", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__replace__-redefinition": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.model_card.SentenceTransformerModelCardData.__replace__", "kind": "<PERSON><PERSON><PERSON>", "plugin_generated": true}, "add_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.add_tags", "name": "add_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_tags of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "all_hyperparameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.all_hyperparameters", "name": "all_hyperparameters", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "base_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.base_model", "name": "base_model", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "base_model_revision": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.base_model_revision", "name": "base_model_revision", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "best_model_step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.best_model_step", "name": "best_model_step", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "citations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.citations", "name": "citations", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "code_carbon_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.code_carbon_callback", "name": "code_carbon_callback", "setter_type": null, "type": {".class": "UnionType", "items": ["transformers.integrations.integration_utils.CodeCarbonCallback", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "compute_dataset_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dataset", "dataset_info", "loss"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.compute_dataset_metrics", "name": "compute_dataset_metrics", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dataset", "dataset_info", "loss"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.model_card.Dataset", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.model_card.IterableDataset", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.dict"}, "torch.nn.modules.module.Module", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_dataset_metrics of SentenceTransformerModelCardData", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "datasets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.datasets", "name": "datasets", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "eval_datasets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.eval_datasets", "name": "eval_datasets", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "eval_results_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.eval_results_dict", "name": "eval_results_dict", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["sentence_transformers.evaluation.SentenceEvaluator.SentenceEvaluator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "extract_dataset_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "dataset", "dataset_metadata", "loss", "dataset_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.extract_dataset_metadata", "name": "extract_dataset_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "dataset", "dataset_metadata", "loss", "dataset_type"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.model_card.Dataset", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.model_card.DatasetDict", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "Instance", "args": ["builtins.str", "torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "train"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "eval"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extract_dataset_metadata of SentenceTransformerModelCardData", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "first_save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.first_save", "name": "first_save", "setter_type": null, "type": "builtins.bool"}}, "format_eval_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.format_eval_metrics", "name": "format_eval_metrics", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "format_eval_metrics of SentenceTransformerModelCardData", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format_training_logs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.format_training_logs", "name": "format_training_logs", "type": null}}, "generate_widget_examples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.generate_widget_examples", "name": "generate_widget_examples", "setter_type": null, "type": "builtins.bool"}}, "get_codecarbon_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.get_codecarbon_data", "name": "get_codecarbon_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_codecarbon_data of SentenceTransformerModelCardData", "ret_type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "co2_eq_emissions"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_default_model_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.get_default_model_name", "name": "get_default_model_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_default_model_name of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_model_specific_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.get_model_specific_metadata", "name": "get_model_specific_metadata", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_model_specific_metadata of SentenceTransformerModelCardData", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "infer_datasets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dataset", "dataset_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.infer_datasets", "name": "infer_datasets", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dataset", "dataset_name"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.model_card.Dataset", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.model_card.DatasetDict", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "infer_datasets of SentenceTransformerModelCardData", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ir_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.ir_model", "name": "ir_model", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "label_example_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.label_example_list", "name": "label_example_list", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "language": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.language", "name": "language", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "library_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.library_name", "name": "library_name", "setter_type": null, "type": "builtins.str"}}, "license": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.license", "name": "license", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "local_files_only": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.local_files_only", "name": "local_files_only", "setter_type": null, "type": "builtins.bool"}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.model", "name": "model", "setter_type": null, "type": {".class": "UnionType", "items": ["sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "model_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.model_id", "name": "model_id", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "model_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.model_name", "name": "model_name", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "non_default_hyperparameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.non_default_hyperparameters", "name": "non_default_hyperparameters", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "pipeline_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.pipeline_tag", "name": "pipeline_tag", "setter_type": null, "type": "builtins.str"}}, "predict_example": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.predict_example", "name": "predict_example", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "register_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.register_model", "name": "register_model", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "model"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", "sentence_transformers.SentenceTransformer.SentenceTransformer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register_model of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "run_usage_snippet": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.run_usage_snippet", "name": "run_usage_snippet", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "run_usage_snippet of SentenceTransformerModelCardData", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_base_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "model_id", "revision"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.set_base_model", "name": "set_base_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "model_id", "revision"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_base_model of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_best_model_step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "step"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.set_best_model_step", "name": "set_best_model_step", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "step"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_best_model_step of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_evaluation_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "evaluator", "metrics", "epoch", "step"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.set_evaluation_metrics", "name": "set_evaluation_metrics", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "evaluator", "metrics", "epoch", "step"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", "sentence_transformers.evaluation.SentenceEvaluator.SentenceEvaluator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_evaluation_metrics of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_label_examples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dataset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.set_label_examples", "name": "set_label_examples", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dataset"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", {".class": "AnyType", "missing_import_name": "sentence_transformers.model_card.Dataset", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_label_examples of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_language": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "language"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.set_language", "name": "set_language", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "language"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_language of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_license": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "license"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.set_license", "name": "set_license", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "license"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_license of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_losses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "losses"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.set_losses", "name": "set_losses", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "losses"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_losses of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_model_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.set_model_id", "name": "set_model_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "model_id"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_model_id of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_widget_examples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dataset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.set_widget_examples", "name": "set_widget_examples", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dataset"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.model_card.Dataset", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.model_card.DatasetDict", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_widget_examples of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "similarities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.similarities", "name": "similarities", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.tags", "name": "tags", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "task_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.task_name", "name": "task_name", "setter_type": null, "type": "builtins.str"}}, "template_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.template_path", "name": "template_path", "setter_type": null, "type": "pathlib.Path"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_dict of SentenceTransformerModelCardData", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_yaml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "line_break"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.to_yaml", "name": "to_yaml", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "line_break"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_yaml of SentenceTransformerModelCardData", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tokenize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "text", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.tokenize", "name": "tokenize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "text", "kwargs"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tokenize of SentenceTransformerModelCardData", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "train_datasets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.train_datasets", "name": "train_datasets", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "training_logs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.training_logs", "name": "training_logs", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "try_to_set_base_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.try_to_set_base_model", "name": "try_to_set_base_model", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "try_to_set_base_model of SentenceTransformerModelCardData", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validate_datasets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dataset_list", "infer_languages"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.validate_datasets", "name": "validate_datasets", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dataset_list", "infer_languages"], "arg_types": ["sentence_transformers.model_card.SentenceTransformerModelCardData", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate_datasets of SentenceTransformerModelCardData", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.version", "name": "version", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "widget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.widget", "name": "widget", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "widget_step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.widget_step", "name": "widget_step", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.model_card.SentenceTransformerModelCardData.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.model_card.SentenceTransformerModelCardData", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SentenceTransformerTrainer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.trainer.SentenceTransformerTrainer", "kind": "Gdef"}, "SentenceTransformerTrainingArguments": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.training_args.SentenceTransformerTrainingArguments", "kind": "Gdef"}, "StaticEmbedding": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.StaticEmbedding.StaticEmbedding", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TrainerCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.TrainerCallback", "kind": "Gdef"}, "TrainerControl": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.TrainerControl", "kind": "Gdef"}, "TrainerState": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.TrainerState", "kind": "Gdef"}, "Value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.model_card.Value", "name": "Value", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.model_card.Value", "source_any": null, "type_of_any": 3}}}, "YAML_FIELDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.model_card.YAML_FIELDS", "name": "YAML_FIELDS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.model_card.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.model_card.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.model_card.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.model_card.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.model_card.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.model_card.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy.copy", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "warnings.deprecated", "kind": "Gdef"}, "eval_results_to_model_index": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.repocard_data.eval_results_to_model_index", "kind": "Gdef"}, "field": {".class": "SymbolTableNode", "cross_ref": "dataclasses.field", "kind": "Gdef"}, "fields": {".class": "SymbolTableNode", "cross_ref": "dataclasses.fields", "kind": "Gdef"}, "format_log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.model_card.format_log", "name": "format_log", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "UnionType", "items": ["builtins.float", "builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "format_log", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fullname": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.fullname", "kind": "Gdef"}, "generate_model_card": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.model_card.generate_model_card", "name": "generate_model_card", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model"], "arg_types": ["sentence_transformers.SentenceTransformer.SentenceTransformer"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate_model_card", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_dataset_info": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.hf_api.dataset_info", "kind": "Gdef"}, "get_losses": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["loss"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.model_card.get_losses", "name": "get_losses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["loss"], "arg_types": [{".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_losses", "ret_type": {".class": "Instance", "args": ["torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_model_info": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.hf_api.model_info", "kind": "Gdef"}, "get_versions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.model_card.get_versions", "name": "get_versions", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_versions", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "indent": {".class": "SymbolTableNode", "cross_ref": "textwrap.indent", "kind": "Gdef"}, "is_accelerate_available": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.is_accelerate_available", "kind": "Gdef"}, "is_datasets_available": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.is_datasets_available", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.model_card.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "make_markdown_table": {".class": "SymbolTableNode", "cross_ref": "transformers.modelcard.make_markdown_table", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "pformat": {".class": "SymbolTableNode", "cross_ref": "pprint.pformat", "kind": "Gdef"}, "python_version": {".class": "SymbolTableNode", "cross_ref": "platform.python_version", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "sentence_transformers_version": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.__version__", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "tqdm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.model_card.tqdm", "name": "tqdm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.model_card.tqdm", "source_any": null, "type_of_any": 3}}}, "transformers": {".class": "SymbolTableNode", "cross_ref": "transformers", "kind": "Gdef"}, "yaml_dump": {".class": "SymbolTableNode", "cross_ref": "huggingface_hub.utils._fixes.yaml_dump", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/model_card.py"}