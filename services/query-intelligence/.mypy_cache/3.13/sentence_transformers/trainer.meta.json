{"data_mtime": 1752049734, "dep_lines": [15, 18, 24, 24, 25, 27, 27, 13, 14, 19, 20, 21, 23, 24, 26, 27, 28, 36, 41, 49, 1, 3, 4, 5, 6, 7, 8, 9, 10, 12, 16, 362, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 44], "dep_prios": [5, 5, 10, 10, 5, 10, 10, 5, 10, 5, 5, 5, 5, 20, 5, 20, 5, 5, 5, 20, 5, 10, 10, 10, 10, 5, 5, 5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["torch.utils.data", "transformers.data.data_collator", "sentence_transformers.evaluation.SentenceEvaluator", "sentence_transformers.evaluation.SequentialEvaluator", "sentence_transformers.losses.CoSENTLoss", "sentence_transformers.models.Pooling", "sentence_transformers.models.Router", "packaging.version", "torch.nn", "transformers.integrations", "transformers.trainer", "transformers.trainer_utils", "sentence_transformers.data_collator", "sentence_transformers.evaluation", "sentence_transformers.model_card", "sentence_transformers.models", "sentence_transformers.sampler", "sentence_transformers.training_args", "sentence_transformers.util", "sentence_transformers.SentenceTransformer", "__future__", "inspect", "logging", "os", "re", "collections", "contextlib", "functools", "typing", "torch", "transformers", "sentence_transformers", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "huggingface_hub", "huggingface_hub.repocard_data", "numpy", "packaging", "sentence_transformers.fit_mixin", "sentence_transformers.losses", "sentence_transformers.models.InputModule", "sentence_transformers.models.Module", "sentence_transformers.peft_mixin", "sentence_transformers.similarity_functions", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "torch.optim", "torch.optim.lr_scheduler", "torch.optim.optimizer", "torch.utils", "torch.utils.data.dataloader", "torch.utils.data.dataset", "torch.utils.data.sampler", "transformers.data", "transformers.debug_utils", "transformers.integrations.integration_utils", "transformers.tokenization_utils_base", "transformers.trainer_callback", "transformers.training_args", "transformers.utils", "transformers.utils.dummy_pt_objects", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "fe222e15a7693fe07f9731ca680f08e961f16223", "id": "sentence_transformers.trainer", "ignore_all": true, "interface_hash": "c0dc951ce2013bc270ff2096e41e39cc9120123c", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/trainer.py", "plugin_data": null, "size": 66048, "suppressed": ["datasets"], "version_id": "1.16.1"}