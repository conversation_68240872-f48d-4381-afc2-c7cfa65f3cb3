{"data_mtime": 1752049734, "dep_lines": [12, 13, 11, 14, 15, 1, 3, 4, 5, 7, 8, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 9, 409], "dep_prios": [5, 5, 10, 5, 5, 5, 10, 10, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 20], "dependencies": ["sentence_transformers.evaluation.InformationRetrievalEvaluator", "sentence_transformers.evaluation.SentenceEvaluator", "sentence_transformers.SentenceTransformer", "sentence_transformers.similarity_functions", "sentence_transformers.util", "__future__", "logging", "os", "typing", "numpy", "torch", "sentence_transformers", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "datetime", "enum", "genericpath", "io", "numpy._core", "numpy._core.fromnumeric", "numpy._globals", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nbit_base", "numpy._typing._nested_sequence", "posixpath", "sentence_transformers.fit_mixin", "sentence_transformers.peft_mixin", "torch._C", "torch._tensor", "torch.nn", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "types"], "hash": "48825b93dedadf4fa898305ea949239933f0eada", "id": "sentence_transformers.evaluation.NanoBEIREvaluator", "ignore_all": true, "interface_hash": "79fb599ba5688f9b8e4c286bf2955735aaee14ad", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/evaluation/NanoBEIREvaluator.py", "plugin_data": null, "size": 21759, "suppressed": ["tqdm", "datasets"], "version_id": "1.16.1"}