{".class": "MypyFile", "_fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BinaryClassificationEvaluator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sentence_transformers.evaluation.SentenceEvaluator.SentenceEvaluator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator", "name": "BinaryClassificationEvaluator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.evaluation.BinaryClassificationEvaluator", "mro": ["sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator", "sentence_transformers.evaluation.SentenceEvaluator.SentenceEvaluator", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "model", "output_path", "epoch", "steps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "model", "output_path", "epoch", "steps"], "arg_types": ["sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator", "sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of BinaryClassificationEvaluator", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "sentences1", "sentences2", "labels", "name", "batch_size", "show_progress_bar", "write_csv", "truncate_dim", "similarity_fn_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "sentences1", "sentences2", "labels", "name", "batch_size", "show_progress_bar", "write_csv", "truncate_dim", "similarity_fn_names"], "arg_types": ["sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "cosine"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dot"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "euclidean"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "manhattan"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BinaryClassificationEvaluator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_append_csv_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "similarity_fn_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator._append_csv_headers", "name": "_append_csv_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "similarity_fn_names"], "arg_types": ["sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_append_csv_headers of BinaryClassificationEvaluator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.batch_size", "name": "batch_size", "setter_type": null, "type": "builtins.int"}}, "compute_metrices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.compute_metrices", "name": "compute_metrices", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "model"], "arg_types": ["sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator", "sentence_transformers.SentenceTransformer.SentenceTransformer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_metrices of BinaryClassificationEvaluator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "csv_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.csv_file", "name": "csv_file", "setter_type": null, "type": "builtins.str"}}, "csv_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.csv_headers", "name": "csv_headers", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "embed_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "model", "sentences", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.embed_inputs", "name": "embed_inputs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "model", "sentences", "kwargs"], "arg_types": ["sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator", "sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_inputs of BinaryClassificationEvaluator", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_best_acc_and_threshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["scores", "labels", "high_score_more_similar"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.find_best_acc_and_threshold", "name": "find_best_acc_and_threshold", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["scores", "labels", "high_score_more_similar"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_best_acc_and_threshold of BinaryClassificationEvaluator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.find_best_acc_and_threshold", "name": "find_best_acc_and_threshold", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["scores", "labels", "high_score_more_similar"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_best_acc_and_threshold of BinaryClassificationEvaluator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "find_best_f1_and_threshold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["scores", "labels", "high_score_more_similar"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.find_best_f1_and_threshold", "name": "find_best_f1_and_threshold", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["scores", "labels", "high_score_more_similar"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_best_f1_and_threshold of BinaryClassificationEvaluator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.find_best_f1_and_threshold", "name": "find_best_f1_and_threshold", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["scores", "labels", "high_score_more_similar"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_best_f1_and_threshold of BinaryClassificationEvaluator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_input_examples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "examples", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.from_input_examples", "name": "from_input_examples", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "examples", "kwargs"], "arg_types": [{".class": "TypeType", "item": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator"}, {".class": "Instance", "args": ["sentence_transformers.readers.InputExample.InputExample"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_input_examples of BinaryClassificationEvaluator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.from_input_examples", "name": "from_input_examples", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "examples", "kwargs"], "arg_types": [{".class": "TypeType", "item": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator"}, {".class": "Instance", "args": ["sentence_transformers.readers.InputExample.InputExample"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_input_examples of BinaryClassificationEvaluator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_config_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.get_config_dict", "name": "get_config_dict", "type": null}}, "labels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.labels", "name": "labels", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.name", "name": "name", "setter_type": null, "type": "builtins.str"}}, "sentences1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.sentences1", "name": "sentences1", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "sentences2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.sentences2", "name": "sentences2", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "show_progress_bar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.show_progress_bar", "name": "show_progress_bar", "setter_type": null, "type": "builtins.bool"}}, "similarity_fn_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.similarity_fn_names", "name": "similarity_fn_names", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "cosine"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dot"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "euclidean"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "manhattan"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "truncate_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.truncate_dim", "name": "truncate_dim", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "write_csv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.write_csv", "name": "write_csv", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InputExample": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.readers.InputExample.InputExample", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "SentenceEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.SentenceEvaluator.SentenceEvaluator", "kind": "Gdef"}, "SentenceTransformer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.SentenceTransformer.SentenceTransformer", "kind": "Gdef"}, "SimilarityFunction": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.similarity_functions.SimilarityFunction", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "average_precision_score": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.average_precision_score", "name": "average_precision_score", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.evaluation.BinaryClassificationEvaluator.average_precision_score", "source_any": null, "type_of_any": 3}}}, "csv": {".class": "SymbolTableNode", "cross_ref": "csv", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "matthews_corrcoef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.evaluation.BinaryClassificationEvaluator.matthews_corrcoef", "name": "matthews_corrcoef", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.evaluation.BinaryClassificationEvaluator.matthews_corrcoef", "source_any": null, "type_of_any": 3}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "pairwise_cos_sim": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.pairwise_cos_sim", "kind": "Gdef"}, "pairwise_dot_score": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.pairwise_dot_score", "kind": "Gdef"}, "pairwise_euclidean_sim": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.pairwise_euclidean_sim", "kind": "Gdef"}, "pairwise_manhattan_sim": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.pairwise_manhattan_sim", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/evaluation/BinaryClassificationEvaluator.py"}