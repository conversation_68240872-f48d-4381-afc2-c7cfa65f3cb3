{"data_mtime": 1752049734, "dep_lines": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["sentence_transformers.evaluation.BinaryClassificationEvaluator", "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator", "sentence_transformers.evaluation.InformationRetrievalEvaluator", "sentence_transformers.evaluation.LabelAccuracyEvaluator", "sentence_transformers.evaluation.MSEEvaluator", "sentence_transformers.evaluation.MSEEvaluatorFromDataFrame", "sentence_transformers.evaluation.NanoBEIREvaluator", "sentence_transformers.evaluation.ParaphraseMiningEvaluator", "sentence_transformers.evaluation.RerankingEvaluator", "sentence_transformers.evaluation.SentenceEvaluator", "sentence_transformers.evaluation.SequentialEvaluator", "sentence_transformers.evaluation.SimilarityFunction", "sentence_transformers.evaluation.TranslationEvaluator", "sentence_transformers.evaluation.TripletEvaluator", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "4293c380b152a3bcf93e3f959d3d97fe60131169", "id": "sentence_transformers.evaluation", "ignore_all": true, "interface_hash": "91dea54ca95118e19f50de9b3ce31ac119dc29f6", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/evaluation/__init__.py", "plugin_data": null, "size": 1291, "suppressed": [], "version_id": "1.16.1"}