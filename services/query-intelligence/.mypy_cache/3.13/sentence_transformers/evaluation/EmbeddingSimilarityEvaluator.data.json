{".class": "MypyFile", "_fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "EmbeddingSimilarityEvaluator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sentence_transformers.evaluation.SentenceEvaluator.SentenceEvaluator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator", "name": "EmbeddingSimilarityEvaluator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator", "mro": ["sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator", "sentence_transformers.evaluation.SentenceEvaluator.SentenceEvaluator", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "model", "output_path", "epoch", "steps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "model", "output_path", "epoch", "steps"], "arg_types": ["sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator", "sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of EmbeddingSimilarityEvaluator", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "sentences1", "sentences2", "scores", "batch_size", "main_similarity", "similarity_fn_names", "name", "show_progress_bar", "write_csv", "precision", "truncate_dim"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "sentences1", "sentences2", "scores", "batch_size", "main_similarity", "similarity_fn_names", "name", "show_progress_bar", "write_csv", "precision", "truncate_dim"], "arg_types": ["sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", "sentence_transformers.similarity_functions.SimilarityFunction", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "cosine"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "euclidean"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "manhattan"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dot"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "float32"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uint8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binary"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ubinary"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of EmbeddingSimilarityEvaluator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_append_csv_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "similarity_fn_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator._append_csv_headers", "name": "_append_csv_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "similarity_fn_names"], "arg_types": ["sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_append_csv_headers of EmbeddingSimilarityEvaluator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.batch_size", "name": "batch_size", "setter_type": null, "type": "builtins.int"}}, "csv_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.csv_file", "name": "csv_file", "setter_type": null, "type": "builtins.str"}}, "csv_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.csv_headers", "name": "csv_headers", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.description", "name": "description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "description of EmbeddingSimilarityEvaluator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.description", "name": "description", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "description of EmbeddingSimilarityEvaluator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "embed_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "model", "sentences", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.embed_inputs", "name": "embed_inputs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "model", "sentences", "kwargs"], "arg_types": ["sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator", "sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_inputs of EmbeddingSimilarityEvaluator", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_input_examples": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "examples", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.from_input_examples", "name": "from_input_examples", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "examples", "kwargs"], "arg_types": [{".class": "TypeType", "item": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator"}, {".class": "Instance", "args": ["sentence_transformers.readers.InputExample.InputExample"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_input_examples of EmbeddingSimilarityEvaluator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.from_input_examples", "name": "from_input_examples", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "examples", "kwargs"], "arg_types": [{".class": "TypeType", "item": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator"}, {".class": "Instance", "args": ["sentence_transformers.readers.InputExample.InputExample"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_input_examples of EmbeddingSimilarityEvaluator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_config_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.get_config_dict", "name": "get_config_dict", "type": null}}, "main_similarity": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.main_similarity", "name": "main_similarity", "setter_type": null, "type": {".class": "UnionType", "items": ["sentence_transformers.similarity_functions.SimilarityFunction", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.name", "name": "name", "setter_type": null, "type": "builtins.str"}}, "precision": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.precision", "name": "precision", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "float32"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uint8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "binary"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ubinary"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "scores": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.scores", "name": "scores", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "sentences1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.sentences1", "name": "sentences1", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "sentences2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.sentences2", "name": "sentences2", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "show_progress_bar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.show_progress_bar", "name": "show_progress_bar", "setter_type": null, "type": "builtins.bool"}}, "similarity_fn_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.similarity_fn_names", "name": "similarity_fn_names", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "cosine"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "euclidean"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "manhattan"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dot"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "truncate_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.truncate_dim", "name": "truncate_dim", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "write_csv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.write_csv", "name": "write_csv", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InputExample": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.readers.InputExample.InputExample", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "SentenceEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.SentenceEvaluator.SentenceEvaluator", "kind": "Gdef"}, "SentenceTransformer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.SentenceTransformer.SentenceTransformer", "kind": "Gdef"}, "SimilarityFunction": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.similarity_functions.SimilarityFunction", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "csv": {".class": "SymbolTableNode", "cross_ref": "csv", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "pairwise_cos_sim": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.pairwise_cos_sim", "kind": "Gdef"}, "pairwise_dot_score": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.pairwise_dot_score", "kind": "Gdef"}, "pairwise_euclidean_sim": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.pairwise_euclidean_sim", "kind": "Gdef"}, "pairwise_manhattan_sim": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.pairwise_manhattan_sim", "kind": "Gdef"}, "pearsonr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.pearsonr", "name": "pear<PERSON><PERSON>", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.pearsonr", "source_any": null, "type_of_any": 3}}}, "spearmanr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.spearmanr", "name": "spearmanr", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.spearmanr", "source_any": null, "type_of_any": 3}}}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/evaluation/EmbeddingSimilarityEvaluator.py"}