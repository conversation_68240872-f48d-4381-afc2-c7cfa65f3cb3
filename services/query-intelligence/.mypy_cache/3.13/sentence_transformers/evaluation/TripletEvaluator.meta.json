{"data_mtime": 1752049734, "dep_lines": [8, 9, 9, 10, 11, 21, 1, 3, 4, 5, 6, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 20, 5, 5, 25, 5, 10, 10, 10, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sentence_transformers.evaluation.SentenceEvaluator", "sentence_transformers.readers.InputExample", "sentence_transformers.readers", "sentence_transformers.similarity_functions", "sentence_transformers.util", "sentence_transformers.SentenceTransformer", "__future__", "csv", "logging", "os", "typing", "numpy", "builtins", "_collections_abc", "_csv", "_frozen_importlib", "_io", "_typeshed", "abc", "enum", "genericpath", "io", "posixpath", "sentence_transformers.fit_mixin", "sentence_transformers.peft_mixin", "torch", "torch._C", "torch._tensor", "torch.nn", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "types"], "hash": "1931e2998f3ed39a594046c2030199babc00eb6d", "id": "sentence_transformers.evaluation.TripletEvaluator", "ignore_all": true, "interface_hash": "1f8b343bb28bc0dc8104ba0053425e23c0432c04", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/evaluation/TripletEvaluator.py", "plugin_data": null, "size": 11758, "suppressed": [], "version_id": "1.16.1"}