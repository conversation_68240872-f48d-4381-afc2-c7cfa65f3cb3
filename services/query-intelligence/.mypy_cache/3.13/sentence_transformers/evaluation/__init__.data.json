{".class": "MypyFile", "_fullname": "sentence_transformers.evaluation", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BinaryClassificationEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.BinaryClassificationEvaluator.BinaryClassificationEvaluator", "kind": "Gdef"}, "EmbeddingSimilarityEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.EmbeddingSimilarityEvaluator.EmbeddingSimilarityEvaluator", "kind": "Gdef"}, "InformationRetrievalEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.InformationRetrievalEvaluator.InformationRetrievalEvaluator", "kind": "Gdef"}, "LabelAccuracyEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.LabelAccuracyEvaluator.LabelAccuracyEvaluator", "kind": "Gdef"}, "MSEEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.MSEEvaluator.MSEEvaluator", "kind": "Gdef"}, "MSEEvaluatorFromDataFrame": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.MSEEvaluatorFromDataFrame.MSEEvaluatorFromDataFrame", "kind": "Gdef"}, "NanoBEIREvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.NanoBEIREvaluator.NanoBEIREvaluator", "kind": "Gdef"}, "ParaphraseMiningEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.ParaphraseMiningEvaluator.ParaphraseMiningEvaluator", "kind": "Gdef"}, "RerankingEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator", "kind": "Gdef"}, "SentenceEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.SentenceEvaluator.SentenceEvaluator", "kind": "Gdef"}, "SequentialEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.SequentialEvaluator.SequentialEvaluator", "kind": "Gdef"}, "SimilarityFunction": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.similarity_functions.SimilarityFunction", "kind": "Gdef"}, "TranslationEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.TranslationEvaluator.TranslationEvaluator", "kind": "Gdef"}, "TripletEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.TripletEvaluator.TripletEvaluator", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.evaluation.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/evaluation/__init__.py"}