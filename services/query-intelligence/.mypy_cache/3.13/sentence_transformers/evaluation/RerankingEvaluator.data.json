{".class": "MypyFile", "_fullname": "sentence_transformers.evaluation.RerankingEvaluator", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "RerankingEvaluator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sentence_transformers.evaluation.SentenceEvaluator.SentenceEvaluator"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator", "name": "RerankingEvaluator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.evaluation.RerankingEvaluator", "mro": ["sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator", "sentence_transformers.evaluation.SentenceEvaluator.SentenceEvaluator", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "model", "output_path", "epoch", "steps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "model", "output_path", "epoch", "steps"], "arg_types": ["sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator", "sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of RerankingEvaluator", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "at_k", "name", "write_csv", "similarity_fct", "batch_size", "show_progress_bar", "use_batched_encoding", "truncate_dim", "mrr_at_k"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "samples", "at_k", "name", "write_csv", "similarity_fct", "batch_size", "show_progress_bar", "use_batched_encoding", "truncate_dim", "mrr_at_k"], "arg_types": ["sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.str", "builtins.bool", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RerankingEvaluator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "at_k": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.at_k", "name": "at_k", "setter_type": null, "type": "builtins.int"}}, "batch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.batch_size", "name": "batch_size", "setter_type": null, "type": "builtins.int"}}, "compute_metrices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.compute_metrices", "name": "compute_metrices", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "model"], "arg_types": ["sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator", "sentence_transformers.SentenceTransformer.SentenceTransformer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_metrices of RerankingEvaluator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_metrices_batched": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.compute_metrices_batched", "name": "compute_metrices_batched", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "model"], "arg_types": ["sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator", "sentence_transformers.SentenceTransformer.SentenceTransformer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_metrices_batched of RerankingEvaluator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_metrices_individual": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.compute_metrices_individual", "name": "compute_metrices_individual", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "model"], "arg_types": ["sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator", "sentence_transformers.SentenceTransformer.SentenceTransformer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_metrices_individual of RerankingEvaluator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "csv_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.csv_file", "name": "csv_file", "setter_type": null, "type": "builtins.str"}}, "csv_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.csv_headers", "name": "csv_headers", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "embed_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "model", "sentences", "encode_fn_name", "show_progress_bar", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.embed_inputs", "name": "embed_inputs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "model", "sentences", "encode_fn_name", "show_progress_bar", "kwargs"], "arg_types": ["sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator", "sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "numpy.dtype"}], "extra_attrs": null, "type_ref": "numpy.n<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_inputs of RerankingEvaluator", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_config_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.get_config_dict", "name": "get_config_dict", "type": null}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.name", "name": "name", "setter_type": null, "type": "builtins.str"}}, "samples": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.samples", "name": "samples", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "show_progress_bar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.show_progress_bar", "name": "show_progress_bar", "setter_type": null, "type": "builtins.bool"}}, "similarity_fct": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.similarity_fct", "name": "similarity_fct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "truncate_dim": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.truncate_dim", "name": "truncate_dim", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "use_batched_encoding": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.use_batched_encoding", "name": "use_batched_encoding", "setter_type": null, "type": "builtins.bool"}}, "write_csv": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.write_csv", "name": "write_csv", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.evaluation.RerankingEvaluator.RerankingEvaluator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SentenceEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.SentenceEvaluator.SentenceEvaluator", "kind": "Gdef"}, "SentenceTransformer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.SentenceTransformer.SentenceTransformer", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tensor": {".class": "SymbolTableNode", "cross_ref": "torch._tensor.Tensor", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "average_precision_score": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.average_precision_score", "name": "average_precision_score", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.evaluation.RerankingEvaluator.average_precision_score", "source_any": null, "type_of_any": 3}}}, "cos_sim": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.cos_sim", "kind": "Gdef"}, "csv": {".class": "SymbolTableNode", "cross_ref": "csv", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "ndcg_score": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.ndcg_score", "name": "ndcg_score", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.evaluation.RerankingEvaluator.ndcg_score", "source_any": null, "type_of_any": 3}}}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "tqdm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.evaluation.RerankingEvaluator.tqdm", "name": "tqdm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.evaluation.RerankingEvaluator.tqdm", "source_any": null, "type_of_any": 3}}}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/evaluation/RerankingEvaluator.py"}