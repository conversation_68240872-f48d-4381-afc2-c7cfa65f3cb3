{"data_mtime": 1752049734, "dep_lines": [9, 11, 12, 15, 1, 3, 4, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 5, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.utils.data", "sentence_transformers.evaluation.SentenceEvaluator", "sentence_transformers.util", "sentence_transformers.SentenceTransformer", "__future__", "csv", "logging", "os", "typing", "torch", "builtins", "_csv", "_frozen_importlib", "_io", "_typeshed", "abc", "genericpath", "io", "posixpath", "sentence_transformers.fit_mixin", "sentence_transformers.peft_mixin", "sentence_transformers.readers", "sentence_transformers.readers.InputExample", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.nn", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "torch.utils", "torch.utils._contextlib", "torch.utils.data._utils", "torch.utils.data._utils.collate", "torch.utils.data.dataloader", "types"], "hash": "c4e2c2aedde3a7444d25b8c75eda95998a5e3221", "id": "sentence_transformers.evaluation.LabelAccuracyEvaluator", "ignore_all": true, "interface_hash": "5248e90acf8ab3f9fc57c58563858e9bf50e2216", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/evaluation/LabelAccuracyEvaluator.py", "plugin_data": null, "size": 3462, "suppressed": [], "version_id": "1.16.1"}