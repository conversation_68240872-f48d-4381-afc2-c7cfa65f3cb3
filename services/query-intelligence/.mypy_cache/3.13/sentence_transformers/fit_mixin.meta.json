{"data_mtime": 1752049734, "dep_lines": [17, 18, 22, 23, 31, 40, 570, 7, 14, 15, 16, 24, 29, 31, 32, 41, 249, 1, 3, 4, 5, 6, 8, 9, 11, 12, 13, 14, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 19, 35], "dep_prios": [5, 5, 5, 5, 10, 25, 20, 5, 10, 10, 5, 5, 5, 20, 5, 25, 20, 5, 10, 10, 10, 10, 5, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["torch.optim.lr_scheduler", "torch.utils.data", "sentence_transformers.datasets.NoDuplicatesDataLoader", "sentence_transformers.datasets.SentenceLabelDataset", "sentence_transformers.evaluation.SentenceEvaluator", "sentence_transformers.readers.InputExample", "torch.cuda.amp", "collections.abc", "packaging.version", "torch.nn", "torch.optim", "sentence_transformers.training_args", "sentence_transformers.util", "sentence_transformers.evaluation", "sentence_transformers.model_card_templates", "sentence_transformers.SentenceTransformer", "sentence_transformers.trainer", "__future__", "json", "logging", "os", "shutil", "pathlib", "typing", "numpy", "torch", "transformers", "packaging", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "genericpath", "json.encoder", "posixpath", "sentence_transformers.datasets", "sentence_transformers.peft_mixin", "sentence_transformers.readers", "torch._C", "torch._tensor", "torch.amp", "torch.amp.autocast_mode", "torch.amp.grad_scaler", "torch.cuda", "torch.cuda.amp.autocast_mode", "torch.cuda.amp.grad_scaler", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "torch.nn.parameter", "torch.nn.utils", "torch.optim.adam", "torch.optim.adamw", "torch.optim.optimizer", "torch.utils", "torch.utils.data._utils", "torch.utils.data._utils.collate", "torch.utils.data.dataloader", "torch.utils.data.dataset", "transformers.data", "transformers.data.data_collator", "transformers.debug_utils", "transformers.tokenization_utils_base", "transformers.trainer_callback", "transformers.trainer_utils", "transformers.training_args", "transformers.utils", "transformers.utils.dummy_pt_objects", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "56cb4abb520244a4786e8a81fe8bc4858973af13", "id": "sentence_transformers.fit_mixin", "ignore_all": true, "interface_hash": "58a3da8931cdf2c956ddfb69bbf33aea876e83f9", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/fit_mixin.py", "plugin_data": null, "size": 32801, "suppressed": ["tqdm.autonotebook", "datasets"], "version_id": "1.16.1"}