{".class": "MypyFile", "_fullname": "sentence_transformers.fit_mixin", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BatchSamplers": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.training_args.BatchSamplers", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "DataLoader": {".class": "SymbolTableNode", "cross_ref": "torch.utils.data.dataloader.DataLoader", "kind": "Gdef"}, "Dataset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.fit_mixin.Dataset", "name": "Dataset", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.fit_mixin.Dataset", "source_any": null, "type_of_any": 3}}}, "DatasetDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.fit_mixin.DatasetDict", "name": "DatasetDict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.fit_mixin.DatasetDict", "source_any": null, "type_of_any": 3}}}, "EvaluatorCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_callback.TrainerCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.fit_mixin.EvaluatorCallback", "name": "EvaluatorCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.fit_mixin.EvaluatorCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.fit_mixin", "mro": ["sentence_transformers.fit_mixin.EvaluatorCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "evaluator", "output_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.fit_mixin.EvaluatorCallback.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "evaluator", "output_path"], "arg_types": ["sentence_transformers.fit_mixin.EvaluatorCallback", {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceEvaluator"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of EvaluatorCallback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "evaluator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.fit_mixin.EvaluatorCallback.evaluator", "name": "evaluator", "setter_type": null, "type": {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceEvaluator"}}}, "metric_key_prefix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.fit_mixin.EvaluatorCallback.metric_key_prefix", "name": "metric_key_prefix", "setter_type": null, "type": "builtins.str"}}, "on_epoch_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.fit_mixin.EvaluatorCallback.on_epoch_end", "name": "on_epoch_end", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "model", "kwargs"], "arg_types": ["sentence_transformers.fit_mixin.EvaluatorCallback", "sentence_transformers.training_args.SentenceTransformerTrainingArguments", "transformers.trainer_callback.TrainerState", "transformers.trainer_callback.TrainerControl", "sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_epoch_end of EvaluatorCallback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.fit_mixin.EvaluatorCallback.output_path", "name": "output_path", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "trainer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.fit_mixin.EvaluatorCallback.trainer", "name": "trainer", "setter_type": null, "type": {".class": "NoneType"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.fit_mixin.EvaluatorCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.fit_mixin.EvaluatorCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FitMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.fit_mixin.FitMixin", "name": "FitMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.fit_mixin.FitMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.fit_mixin", "mro": ["sentence_transformers.fit_mixin.FitMixin", "builtins.object"], "names": {".class": "SymbolTable", "_eval_during_training": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "evaluator", "output_path", "save_best_model", "epoch", "steps", "callback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.fit_mixin.FitMixin._eval_during_training", "name": "_eval_during_training", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "evaluator", "output_path", "save_best_model", "epoch", "steps", "callback"], "arg_types": ["sentence_transformers.fit_mixin.FitMixin", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_eval_during_training of FitMixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_scheduler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["optimizer", "scheduler", "warmup_steps", "t_total"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "sentence_transformers.fit_mixin.FitMixin._get_scheduler", "name": "_get_scheduler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["optimizer", "scheduler", "warmup_steps", "t_total"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.int", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_scheduler of FitMixin", "ret_type": "torch.optim.lr_scheduler.LambdaLR", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "sentence_transformers.fit_mixin.FitMixin._get_scheduler", "name": "_get_scheduler", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["optimizer", "scheduler", "warmup_steps", "t_total"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.int", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_scheduler of FitMixin", "ret_type": "torch.optim.lr_scheduler.LambdaLR", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_model_card_text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.fit_mixin.FitMixin._model_card_text", "name": "_model_card_text", "setter_type": null, "type": {".class": "NoneType"}}}, "_save_checkpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "checkpoint_path", "checkpoint_save_total_limit", "step"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.fit_mixin.FitMixin._save_checkpoint", "name": "_save_checkpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "checkpoint_path", "checkpoint_save_total_limit", "step"], "arg_types": ["sentence_transformers.fit_mixin.FitMixin", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_save_checkpoint of FitMixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "best_score": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.fit_mixin.FitMixin.best_score", "name": "best_score", "setter_type": null, "type": "builtins.int"}}, "fit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "train_objectives", "evaluator", "epochs", "steps_per_epoch", "scheduler", "warmup_steps", "optimizer_class", "optimizer_params", "weight_decay", "evaluation_steps", "output_path", "save_best_model", "max_grad_norm", "use_amp", "callback", "show_progress_bar", "checkpoint_path", "checkpoint_save_steps", "checkpoint_save_total_limit", "resume_from_checkpoint"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.fit_mixin.FitMixin.fit", "name": "fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "train_objectives", "evaluator", "epochs", "steps_per_epoch", "scheduler", "warmup_steps", "optimizer_class", "optimizer_params", "weight_decay", "evaluation_steps", "output_path", "save_best_model", "max_grad_norm", "use_amp", "callback", "show_progress_bar", "checkpoint_path", "checkpoint_save_steps", "checkpoint_save_total_limit", "resume_from_checkpoint"], "arg_types": ["sentence_transformers.fit_mixin.FitMixin", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataloader.DataLoader"}, "torch.nn.modules.module.Module"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceEvaluator"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.int", {".class": "TypeType", "item": "torch.optim.optimizer.Optimizer"}, {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.float", "builtins.bool", {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.float", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fit of FitMixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "old_fit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "train_objectives", "evaluator", "epochs", "steps_per_epoch", "scheduler", "warmup_steps", "optimizer_class", "optimizer_params", "weight_decay", "evaluation_steps", "output_path", "save_best_model", "max_grad_norm", "use_amp", "callback", "show_progress_bar", "checkpoint_path", "checkpoint_save_steps", "checkpoint_save_total_limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.fit_mixin.FitMixin.old_fit", "name": "old_fit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "train_objectives", "evaluator", "epochs", "steps_per_epoch", "scheduler", "warmup_steps", "optimizer_class", "optimizer_params", "weight_decay", "evaluation_steps", "output_path", "save_best_model", "max_grad_norm", "use_amp", "callback", "show_progress_bar", "checkpoint_path", "checkpoint_save_steps", "checkpoint_save_total_limit"], "arg_types": ["sentence_transformers.fit_mixin.FitMixin", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "torch.utils.data.dataloader.DataLoader"}, "torch.nn.modules.module.Module"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceEvaluator"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.int", {".class": "TypeType", "item": "torch.optim.optimizer.Optimizer"}, {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.float", "builtins.bool", {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.float", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "old_fit of FitMixin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "smart_batching_collate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "batch"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.fit_mixin.FitMixin.smart_batching_collate", "name": "smart_batching_collate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "batch"], "arg_types": ["sentence_transformers.fit_mixin.FitMixin", {".class": "Instance", "args": ["sentence_transformers.readers.InputExample.InputExample"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "smart_batching_collate of FitMixin", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.fit_mixin.FitMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.fit_mixin.FitMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InputExample": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.readers.InputExample.InputExample", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "LambdaLR": {".class": "SymbolTableNode", "cross_ref": "torch.optim.lr_scheduler.LambdaLR", "kind": "Gdef"}, "ModelCardTemplate": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.model_card_templates.ModelCardTemplate", "kind": "Gdef"}, "MultiDatasetBatchSamplers": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.training_args.MultiDatasetBatchSamplers", "kind": "Gdef"}, "NoDuplicatesDataLoader": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.datasets.NoDuplicatesDataLoader.NoDuplicatesDataLoader", "kind": "Gdef"}, "Optimizer": {".class": "SymbolTableNode", "cross_ref": "torch.optim.optimizer.Optimizer", "kind": "Gdef"}, "OriginalCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_callback.TrainerCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.fit_mixin.OriginalCallback", "name": "OriginalCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.fit_mixin.OriginalCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.fit_mixin", "mro": ["sentence_transformers.fit_mixin.OriginalCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "callback", "evaluator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.fit_mixin.OriginalCallback.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "callback", "evaluator"], "arg_types": ["sentence_transformers.fit_mixin.OriginalCallback", {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.float", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceEvaluator"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of OriginalCallback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "callback": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.fit_mixin.OriginalCallback.callback", "name": "callback", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.float", "builtins.int", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "evaluator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.fit_mixin.OriginalCallback.evaluator", "name": "evaluator", "setter_type": null, "type": {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceEvaluator"}}}, "on_evaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "metrics", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.fit_mixin.OriginalCallback.on_evaluate", "name": "on_evaluate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "metrics", "kwargs"], "arg_types": ["sentence_transformers.fit_mixin.OriginalCallback", "transformers.training_args.TrainingArguments", "transformers.trainer_callback.TrainerState", "transformers.trainer_callback.TrainerControl", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_evaluate of OriginalCallback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.fit_mixin.OriginalCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.fit_mixin.OriginalCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "SaveModelCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_callback.TrainerCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.fit_mixin.SaveModelCallback", "name": "SaveModelCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.fit_mixin.SaveModelCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.fit_mixin", "mro": ["sentence_transformers.fit_mixin.SaveModelCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "output_dir", "evaluator", "save_best_model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.fit_mixin.SaveModelCallback.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "output_dir", "evaluator", "save_best_model"], "arg_types": ["sentence_transformers.fit_mixin.SaveModelCallback", "builtins.str", {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceEvaluator"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SaveModelCallback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "best_metric": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.fit_mixin.SaveModelCallback.best_metric", "name": "best_metric", "setter_type": null, "type": {".class": "NoneType"}}}, "evaluator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.fit_mixin.SaveModelCallback.evaluator", "name": "evaluator", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceEvaluator"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "is_better": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_metric"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.fit_mixin.SaveModelCallback.is_better", "name": "is_better", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_metric"], "arg_types": ["sentence_transformers.fit_mixin.SaveModelCallback", "builtins.float"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_better of SaveModelCallback", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_evaluate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "metrics", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.fit_mixin.SaveModelCallback.on_evaluate", "name": "on_evaluate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "metrics", "model", "kwargs"], "arg_types": ["sentence_transformers.fit_mixin.SaveModelCallback", "sentence_transformers.training_args.SentenceTransformerTrainingArguments", "transformers.trainer_callback.TrainerState", "transformers.trainer_callback.TrainerControl", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_evaluate of SaveModelCallback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_train_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "model", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.fit_mixin.SaveModelCallback.on_train_end", "name": "on_train_end", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "model", "kwargs"], "arg_types": ["sentence_transformers.fit_mixin.SaveModelCallback", "sentence_transformers.training_args.SentenceTransformerTrainingArguments", "transformers.trainer_callback.TrainerState", "transformers.trainer_callback.TrainerControl", "sentence_transformers.SentenceTransformer.SentenceTransformer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_train_end of SaveModelCallback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.fit_mixin.SaveModelCallback.output_dir", "name": "output_dir", "setter_type": null, "type": "builtins.str"}}, "save_best_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.fit_mixin.SaveModelCallback.save_best_model", "name": "save_best_model", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.fit_mixin.SaveModelCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.fit_mixin.SaveModelCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SentenceEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.SentenceEvaluator", "kind": "Gdef"}, "SentenceLabelDataset": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.datasets.SentenceLabelDataset.SentenceLabelDataset", "kind": "Gdef"}, "SentenceTransformer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.SentenceTransformer.SentenceTransformer", "kind": "Gdef"}, "SentenceTransformerTrainingArguments": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.training_args.SentenceTransformerTrainingArguments", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tensor": {".class": "SymbolTableNode", "cross_ref": "torch._tensor.Tensor", "kind": "Gdef"}, "TrainerCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.TrainerCallback", "kind": "Gdef"}, "TrainerControl": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.TrainerControl", "kind": "Gdef"}, "TrainerState": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.TrainerState", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.fit_mixin.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.fit_mixin.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.fit_mixin.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.fit_mixin.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.fit_mixin.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.fit_mixin.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "batch_to_device": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.batch_to_device", "kind": "Gdef"}, "fullname": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.fullname", "kind": "Gdef"}, "is_datasets_available": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.is_datasets_available", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.fit_mixin.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "trange": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.fit_mixin.trange", "name": "trange", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.fit_mixin.trange", "source_any": null, "type_of_any": 3}}}, "transformers": {".class": "SymbolTableNode", "cross_ref": "transformers", "kind": "Gdef"}, "version": {".class": "SymbolTableNode", "cross_ref": "packaging.version", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/fit_mixin.py"}