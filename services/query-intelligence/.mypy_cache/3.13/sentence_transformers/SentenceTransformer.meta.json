{"data_mtime": 1752049734, "dep_lines": [37, 38, 42, 44, 44, 17, 24, 26, 29, 30, 33, 36, 37, 39, 42, 43, 45, 46, 47, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21, 23, 25, 27, 28, 29, 34, 41, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 222, 31, 926, 926], "dep_prios": [10, 5, 10, 10, 10, 5, 10, 10, 10, 10, 5, 5, 20, 5, 20, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 10, 5, 5, 5, 20, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 5, 20, 20], "dependencies": ["sentence_transformers.models.Router", "sentence_transformers.models.Module", "sentence_transformers.evaluation.SentenceEvaluator", "sentence_transformers.models.Pooling", "sentence_transformers.models.Transformer", "collections.abc", "numpy.typing", "torch.multiprocessing", "packaging.version", "torch.nn", "transformers.dynamic_module_utils", "sentence_transformers.model_card", "sentence_transformers.models", "sentence_transformers.similarity_functions", "sentence_transformers.evaluation", "sentence_transformers.fit_mixin", "sentence_transformers.peft_mixin", "sentence_transformers.quantization", "sentence_transformers.util", "__future__", "copy", "importlib", "inspect", "json", "logging", "math", "os", "queue", "shutil", "sys", "tempfile", "traceback", "warnings", "collections", "contextlib", "multiprocessing", "pathlib", "typing", "numpy", "torch", "transformers", "huggingface_hub", "packaging", "typing_extensions", "sentence_transformers", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "_warnings", "abc", "enum", "genericpath", "huggingface_hub.repocard_data", "importlib.util", "io", "json.decoder", "multiprocessing.queues", "numpy._core", "numpy._core.fromnumeric", "numpy._core.multiarray", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nbit_base", "numpy._typing._nested_sequence", "posixpath", "sentence_transformers.models.InputModule", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.nn.functional", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "torch.nn.parameter", "torch.types", "torch.utils", "torch.utils._contextlib", "transformers.integrations", "transformers.integrations.peft", "transformers.modeling_utils", "transformers.utils", "transformers.utils.hub", "types"], "hash": "46433d2a6b43565a0728436c181f4b8e1941158b", "id": "sentence_transformers.SentenceTransformer", "ignore_all": true, "interface_hash": "5bdf776ab3bff076e2641b297d6852405d3f72fd", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/SentenceTransformer.py", "plugin_data": null, "size": 124814, "suppressed": ["optimum.habana.transformers.modeling_utils", "tqdm.autonotebook", "habana_frameworks.torch", "habana_frameworks"], "version_id": "1.16.1"}