{"data_mtime": 1752049734, "dep_lines": [11, 16, 1, 3, 4, 5, 6, 7, 9, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 23, 19], "dep_prios": [5, 25, 5, 10, 10, 10, 5, 5, 10, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25, 25], "dependencies": ["sentence_transformers.util", "sentence_transformers.SentenceTransformer", "__future__", "logging", "shutil", "tempfile", "pathlib", "typing", "huggingface_hub", "sentence_transformers", "builtins", "_frozen_importlib", "abc", "sentence_transformers.cross_encoder", "sentence_transformers.cross_encoder.CrossEncoder", "sentence_transformers.cross_encoder.fit_mixin", "torch", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "transformers", "transformers.utils", "transformers.utils.hub"], "hash": "f672d9fe3a8604a2a9088a633bf46fd7bf30cdbb", "id": "sentence_transformers.backend", "ignore_all": true, "interface_hash": "d4c1191ae62a76742856b9d5168750c45c466775", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/backend.py", "plugin_data": null, "size": 21445, "suppressed": ["optimum.onnxruntime.configuration", "optimum.intel"], "version_id": "1.16.1"}