{"data_mtime": 1752049734, "dep_lines": [34, 14, 35, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 18, 20, 21, 27, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 22, 23, 26, 19, 25, 32], "dep_prios": [25, 5, 25, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 10, 5, 25], "dependencies": ["sentence_transformers.cross_encoder.CrossEncoder", "importlib.metadata", "sentence_transformers.SentenceTransformer", "__future__", "csv", "functools", "<PERSON><PERSON><PERSON>", "heapq", "importlib", "logging", "os", "queue", "random", "sys", "contextlib", "pathlib", "typing", "numpy", "torch", "huggingface_hub", "transformers", "builtins", "_frozen_importlib", "abc", "sentence_transformers.cross_encoder", "sentence_transformers.cross_encoder.fit_mixin", "sentence_transformers.fit_mixin", "sentence_transformers.peft_mixin", "torch._C", "torch._tensor", "torch.nn", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "transformers.utils", "transformers.utils.hub"], "hash": "ab61fe85e6d62aade5182b4852afbf27c2bf4413", "id": "sentence_transformers.util", "ignore_all": true, "interface_hash": "9d1d5a2bb49c586702687ac37377a61dab25835e", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/util.py", "plugin_data": null, "size": 86091, "suppressed": ["scipy.sparse", "sklearn.metrics", "tqdm.autonotebook", "requests", "tqdm", "datasets"], "version_id": "1.16.1"}