{".class": "MypyFile", "_fullname": "sentence_transformers", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CrossEncoder": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.CrossEncoder.CrossEncoder", "kind": "Gdef"}, "CrossEncoderModelCardData": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.model_card.CrossEncoderModelCardData", "kind": "Gdef"}, "CrossEncoderTrainer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.trainer.CrossEncoderTrainer", "kind": "Gdef"}, "CrossEncoderTrainingArguments": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.cross_encoder.training_args.CrossEncoderTrainingArguments", "kind": "Gdef"}, "DefaultBatchSampler": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sampler.DefaultBatchSampler", "kind": "Gdef"}, "InputExample": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.readers.InputExample.InputExample", "kind": "Gdef"}, "LoggingHandler": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.LoggingHandler.LoggingHandler", "kind": "Gdef"}, "MultiDatasetDefaultBatchSampler": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sampler.MultiDatasetDefaultBatchSampler", "kind": "Gdef"}, "ParallelSentencesDataset": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.datasets.ParallelSentencesDataset", "kind": "Gdef"}, "SentenceTransformer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.SentenceTransformer.SentenceTransformer", "kind": "Gdef"}, "SentenceTransformerModelCardData": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.model_card.SentenceTransformerModelCardData", "kind": "Gdef"}, "SentenceTransformerTrainer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.trainer.SentenceTransformerTrainer", "kind": "Gdef"}, "SentenceTransformerTrainingArguments": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.training_args.SentenceTransformerTrainingArguments", "kind": "Gdef"}, "SentencesDataset": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.datasets.SentencesDataset", "kind": "Gdef"}, "SimilarityFunction": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.similarity_functions.SimilarityFunction", "kind": "Gdef"}, "SparseEncoder": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.SparseEncoder.SparseEncoder", "kind": "Gdef"}, "SparseEncoderModelCardData": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.model_card.SparseEncoderModelCardData", "kind": "Gdef"}, "SparseEncoderTrainer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer", "kind": "Gdef"}, "SparseEncoderTrainingArguments": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.training_args.SparseEncoderTrainingArguments", "kind": "Gdef"}, "__MODEL_HUB_ORGANIZATION__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.__MODEL_HUB_ORGANIZATION__", "name": "__MODEL_HUB_ORGANIZATION__", "setter_type": null, "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "export_dynamic_quantized_onnx_model": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.backend.export_dynamic_quantized_onnx_model", "kind": "Gdef"}, "export_optimized_onnx_model": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.backend.export_optimized_onnx_model", "kind": "Gdef"}, "export_static_quantized_openvino_model": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.backend.export_static_quantized_openvino_model", "kind": "Gdef"}, "importlib": {".class": "SymbolTableNode", "cross_ref": "importlib", "kind": "Gdef", "module_public": false}, "mine_hard_negatives": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.mine_hard_negatives", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "quantize_embeddings": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.quantization.quantize_embeddings", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/__init__.py"}