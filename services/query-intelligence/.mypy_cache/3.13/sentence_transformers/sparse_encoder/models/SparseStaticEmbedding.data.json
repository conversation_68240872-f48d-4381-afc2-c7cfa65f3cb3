{".class": "MypyFile", "_fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AutoTokenizer": {".class": "SymbolTableNode", "cross_ref": "transformers.models.auto.tokenization_auto.AutoTokenizer", "kind": "Gdef"}, "InputModule": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.InputModule.InputModule", "kind": "Gdef"}, "PreTrainedTokenizer": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils.PreTrainedTokenizer", "kind": "Gdef"}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef"}, "SparseStaticEmbedding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sentence_transformers.models.InputModule.InputModule"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding", "name": "SparseStaticEmbedding", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding", "mro": ["sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding", "sentence_transformers.models.InputModule.InputModule", "sentence_transformers.models.Module.Module", "abc.ABC", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "tokenizer", "weight", "frozen"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "tokenizer", "weight", "frozen"], "arg_types": ["sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding", "transformers.tokenization_utils.PreTrainedTokenizer", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SparseStaticEmbedding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of SparseStaticEmbedding", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.config_keys", "name": "config_keys", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "features"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "features"], "arg_types": ["sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding", {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of <PERSON>rseStatic<PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["cls", "json_path", "tokenizer", "token", "cache_folder", "revision", "local_files_only", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.from_json", "name": "from_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["cls", "json_path", "tokenizer", "token", "cache_folder", "revision", "local_files_only", "config"], "arg_types": [{".class": "TypeType", "item": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding"}, "builtins.str", "transformers.tokenization_utils.PreTrainedTokenizer", {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_json of SparseStaticEmbedding", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.from_json", "name": "from_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["cls", "json_path", "tokenizer", "token", "cache_folder", "revision", "local_files_only", "config"], "arg_types": [{".class": "TypeType", "item": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding"}, "builtins.str", "transformers.tokenization_utils.PreTrainedTokenizer", {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_json of SparseStaticEmbedding", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "frozen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.frozen", "name": "frozen", "setter_type": null, "type": "builtins.bool"}}, "get_sentence_embedding_dimension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.get_sentence_embedding_dimension", "name": "get_sentence_embedding_dimension", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_sentence_embedding_dimension of SparseStaticEmbedding", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "model_name_or_path", "subfolder", "token", "cache_folder", "revision", "local_files_only", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "model_name_or_path", "subfolder", "token", "cache_folder", "revision", "local_files_only", "kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding", "values": [], "variance": 0}}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "load of SparseStaticEmbedding", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.load", "name": "load", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "model_name_or_path", "subfolder", "token", "cache_folder", "revision", "local_files_only", "kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding", "values": [], "variance": 0}}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "load of SparseStaticEmbedding", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding", "values": [], "variance": 0}]}}}}, "max_seq_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.max_seq_length", "name": "max_seq_length", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "num_dimensions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.num_dimensions", "name": "num_dimensions", "setter_type": null, "type": "builtins.int"}}, "save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 4], "arg_names": ["self", "output_path", "args", "safe_serialization", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.save", "name": "save", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 4], "arg_names": ["self", "output_path", "args", "safe_serialization", "kwargs"], "arg_types": ["sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "save of SparseStaticEmbedding", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tokenize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "texts", "padding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.tokenize", "name": "tokenize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "texts", "padding"], "arg_types": ["sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.bool"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "tokenize of SparseStaticEmbedding", "ret_type": {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.weight", "name": "weight", "setter_type": null, "type": "torch.nn.parameter.Parameter"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.SparseStaticEmbedding", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/sparse_encoder/models/SparseStaticEmbedding.py"}