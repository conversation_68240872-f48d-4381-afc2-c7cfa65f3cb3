{"data_mtime": 1752049734, "dep_lines": [16, 18, 18, 14, 14, 15, 17, 18, 19, 20, 21, 8, 9, 12, 14, 15, 22, 23, 1, 3, 4, 5, 7, 10, 332, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 26], "dep_prios": [5, 10, 10, 10, 10, 10, 5, 20, 5, 5, 5, 5, 10, 5, 20, 20, 5, 5, 5, 10, 10, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["sentence_transformers.sparse_encoder.callbacks.splade_callbacks", "sentence_transformers.sparse_encoder.losses.SparseMultipleNegativesRankingLoss", "sentence_transformers.sparse_encoder.losses.SpladeLoss", "sentence_transformers.evaluation.SentenceEvaluator", "sentence_transformers.evaluation.SequentialEvaluator", "sentence_transformers.models.Router", "sentence_transformers.sparse_encoder.data_collator", "sentence_transformers.sparse_encoder.losses", "sentence_transformers.sparse_encoder.model_card", "sentence_transformers.sparse_encoder.SparseEncoder", "sentence_transformers.sparse_encoder.training_args", "packaging.version", "torch.nn", "transformers.integrations", "sentence_transformers.evaluation", "sentence_transformers.models", "sentence_transformers.trainer", "sentence_transformers.util", "__future__", "logging", "os", "typing", "torch", "transformers", "sentence_transformers", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "huggingface_hub", "huggingface_hub.repocard_data", "packaging", "sentence_transformers.SentenceTransformer", "sentence_transformers.data_collator", "sentence_transformers.fit_mixin", "sentence_transformers.model_card", "sentence_transformers.models.InputModule", "sentence_transformers.models.Module", "sentence_transformers.peft_mixin", "sentence_transformers.similarity_functions", "sentence_transformers.training_args", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "torch.optim", "torch.optim.lr_scheduler", "torch.optim.optimizer", "transformers.data", "transformers.data.data_collator", "transformers.debug_utils", "transformers.integrations.integration_utils", "transformers.tokenization_utils_base", "transformers.trainer_callback", "transformers.trainer_utils", "transformers.training_args", "transformers.utils", "transformers.utils.dummy_pt_objects", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "bef3f38efa36618d7367c8c9b9d48aba11c28058", "id": "sentence_transformers.sparse_encoder.trainer", "ignore_all": true, "interface_hash": "1aea1f9ec71a0a414df7dd24ecf9253ff1b03ca2", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/sparse_encoder/trainer.py", "plugin_data": null, "size": 24694, "suppressed": ["datasets"], "version_id": "1.16.1"}