{".class": "MypyFile", "_fullname": "sentence_transformers.sparse_encoder.trainer", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Dataset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.sparse_encoder.trainer.Dataset", "name": "Dataset", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.sparse_encoder.trainer.Dataset", "source_any": null, "type_of_any": 3}}}, "DatasetDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.sparse_encoder.trainer.DatasetDict", "name": "DatasetDict", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.sparse_encoder.trainer.DatasetDict", "source_any": null, "type_of_any": 3}}}, "EvalPrediction": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_utils.EvalPrediction", "kind": "Gdef"}, "IterableDataset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.sparse_encoder.trainer.IterableDataset", "name": "IterableDataset", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.sparse_encoder.trainer.IterableDataset", "source_any": null, "type_of_any": 3}}}, "PreTrainedTokenizerBase": {".class": "SymbolTableNode", "cross_ref": "transformers.tokenization_utils_base.PreTrainedTokenizerBase", "kind": "Gdef"}, "Router": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.Router.Router", "kind": "Gdef"}, "SentenceEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.SentenceEvaluator", "kind": "Gdef"}, "SentenceTransformerTrainer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.trainer.SentenceTransformerTrainer", "kind": "Gdef"}, "SequentialEvaluator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.evaluation.SequentialEvaluator", "kind": "Gdef"}, "SparseEncoder": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.SparseEncoder.SparseEncoder", "kind": "Gdef"}, "SparseEncoderDataCollator": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.data_collator.SparseEncoderDataCollator", "kind": "Gdef"}, "SparseEncoderModelCardCallback": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.model_card.SparseEncoderModelCardCallback", "kind": "Gdef"}, "SparseEncoderTrainer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sentence_transformers.trainer.SentenceTransformerTrainer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer", "name": "SparseEncoderTrainer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer", "has_param_spec_type": false, "metaclass_type": "transformers.utils.import_utils.DummyObject", "metadata": {}, "module_name": "sentence_transformers.sparse_encoder.trainer", "mro": ["sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer", "sentence_transformers.trainer.SentenceTransformerTrainer", "transformers.utils.dummy_pt_objects.Trainer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "model", "args", "train_dataset", "eval_dataset", "loss", "evaluator", "data_collator", "tokenizer", "model_init", "compute_metrics", "callbacks", "optimizers", "preprocess_logits_for_metrics"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "model", "args", "train_dataset", "eval_dataset", "loss", "evaluator", "data_collator", "tokenizer", "model_init", "compute_metrics", "callbacks", "optimizers", "preprocess_logits_for_metrics"], "arg_types": ["sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer", {".class": "UnionType", "items": ["sentence_transformers.sparse_encoder.SparseEncoder.SparseEncoder", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["sentence_transformers.sparse_encoder.training_args.SparseEncoderTrainingArguments", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.sparse_encoder.trainer.Dataset", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.sparse_encoder.trainer.DatasetDict", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "sentence_transformers.sparse_encoder.trainer.Dataset", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "sentence_transformers.sparse_encoder.trainer.Dataset", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "sentence_transformers.sparse_encoder.trainer.DatasetDict", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "sentence_transformers.sparse_encoder.trainer.Dataset", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["torch.nn.modules.module.Module", {".class": "Instance", "args": ["builtins.str", "torch.nn.modules.module.Module"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sentence_transformers.sparse_encoder.SparseEncoder.SparseEncoder"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sentence_transformers.sparse_encoder.SparseEncoder.SparseEncoder"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceEvaluator"}, {".class": "Instance", "args": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "SentenceEvaluator"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["sentence_transformers.sparse_encoder.data_collator.SparseEncoderDataCollator", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["transformers.tokenization_utils_base.PreTrainedTokenizerBase", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "sentence_transformers.sparse_encoder.SparseEncoder.SparseEncoder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["transformers.trainer_utils.EvalPrediction"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["transformers.trainer_callback.TrainerCallback"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TupleType", "implicit": false, "items": ["torch.optim.optimizer.Optimizer", "torch.optim.lr_scheduler.LambdaLR"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["torch._tensor.Tensor", "torch._tensor.Tensor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SparseEncoderTrainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_model_card_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "default_args_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer.add_model_card_callback", "name": "add_model_card_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "default_args_dict"], "arg_types": ["sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "add_model_card_callback of SparseEncoderTrainer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer.args", "name": "args", "setter_type": null, "type": "sentence_transformers.sparse_encoder.training_args.SparseEncoderTrainingArguments"}}, "call_model_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "trial"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer.call_model_init", "name": "call_model_init", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "trial"], "arg_types": ["sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "call_model_init of SparseEncoderTrainer", "ret_type": "sentence_transformers.sparse_encoder.SparseEncoder.SparseEncoder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compute_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "model", "inputs", "return_outputs", "num_items_in_batch"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer.compute_loss", "name": "compute_loss", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "model", "inputs", "return_outputs", "num_items_in_batch"], "arg_types": ["sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer", "sentence_transformers.sparse_encoder.SparseEncoder.SparseEncoder", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "compute_loss of SparseEncoderTrainer", "ret_type": {".class": "UnionType", "items": ["torch._tensor.Tensor", {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "data_collator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer.data_collator", "name": "data_collator", "setter_type": null, "type": "sentence_transformers.sparse_encoder.data_collator.SparseEncoderDataCollator"}}, "get_optimizer_cls_and_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "args", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer.get_optimizer_cls_and_kwargs", "name": "get_optimizer_cls_and_kwargs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "args", "model"], "arg_types": ["sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer", "sentence_transformers.sparse_encoder.training_args.SparseEncoderTrainingArguments", {".class": "UnionType", "items": ["sentence_transformers.sparse_encoder.SparseEncoder.SparseEncoder", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_optimizer_cls_and_kwargs of SparseEncoderTrainer", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer.model", "name": "model", "setter_type": null, "type": "sentence_transformers.sparse_encoder.SparseEncoder.SparseEncoder"}}, "override_model_in_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "loss", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer.override_model_in_loss", "name": "override_model_in_loss", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "loss", "model"], "arg_types": ["sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer", "torch.nn.modules.module.Module", "sentence_transformers.sparse_encoder.SparseEncoder.SparseEncoder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "override_model_in_loss of SparseEncoderTrainer", "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "prepare_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "loss", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer.prepare_loss", "name": "prepare_loss", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "loss", "model"], "arg_types": ["sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sentence_transformers.sparse_encoder.SparseEncoder.SparseEncoder"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "torch.nn.modules.module.Module"], "uses_pep604_syntax": true}, "sentence_transformers.sparse_encoder.SparseEncoder.SparseEncoder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "prepare_loss of SparseEncoderTrainer", "ret_type": "torch.nn.modules.module.Module", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.sparse_encoder.trainer.SparseEncoderTrainer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SparseEncoderTrainingArguments": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.training_args.SparseEncoderTrainingArguments", "kind": "Gdef"}, "SparseMultipleNegativesRankingLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.losses.SparseMultipleNegativesRankingLoss", "kind": "Gdef"}, "SpladeLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.losses.SpladeLoss", "kind": "Gdef"}, "SpladeRegularizerWeightSchedulerCallback": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback", "kind": "Gdef"}, "TrainerCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.TrainerCallback", "kind": "Gdef"}, "Value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.sparse_encoder.trainer.Value", "name": "Value", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.sparse_encoder.trainer.Value", "source_any": null, "type_of_any": 3}}}, "WandbCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.integrations.integration_utils.WandbCallback", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.trainer.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.trainer.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.trainer.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.trainer.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.trainer.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.trainer.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "is_datasets_available": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.is_datasets_available", "kind": "Gdef"}, "is_training_available": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.is_training_available", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.sparse_encoder.trainer.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "parse_version": {".class": "SymbolTableNode", "cross_ref": "packaging.version.parse", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "transformers_version": {".class": "SymbolTableNode", "cross_ref": "transformers.__version__", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/sparse_encoder/trainer.py"}