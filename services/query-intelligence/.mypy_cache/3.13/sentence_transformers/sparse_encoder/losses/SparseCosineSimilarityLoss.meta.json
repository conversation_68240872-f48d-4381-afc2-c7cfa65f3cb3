{"data_mtime": 1752049734, "dep_lines": [8, 9, 3, 5, 1, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sentence_transformers.losses.CosineSimilarityLoss", "sentence_transformers.sparse_encoder.SparseEncoder", "collections.abc", "torch.nn", "__future__", "torch", "builtins", "_frozen_importlib", "abc", "enum", "sentence_transformers.SentenceTransformer", "sentence_transformers.fit_mixin", "sentence_transformers.losses", "sentence_transformers.peft_mixin", "sentence_transformers.similarity_functions", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.linear", "torch.nn.modules.loss", "torch.nn.modules.module", "typing"], "hash": "c55bc96ab75968b8e2818c0cc515ccf07c655cd8", "id": "sentence_transformers.sparse_encoder.losses.SparseCosineSimilarityLoss", "ignore_all": true, "interface_hash": "575469c40dd310c111d9de3df842e3ac35f2b0d5", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/sparse_encoder/losses/SparseCosineSimilarityLoss.py", "plugin_data": null, "size": 3597, "suppressed": [], "version_id": "1.16.1"}