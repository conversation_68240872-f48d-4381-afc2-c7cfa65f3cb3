{".class": "MypyFile", "_fullname": "sentence_transformers.sparse_encoder.losses.SparseMSELoss", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "MSELoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.MSELoss.MSELoss", "kind": "Gdef"}, "SparseEncoder": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.SparseEncoder.SparseEncoder", "kind": "Gdef"}, "SparseMSELoss": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sentence_transformers.losses.MSELoss.MSELoss"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.sparse_encoder.losses.SparseMSELoss.SparseMSELoss", "name": "SparseMSELoss", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.sparse_encoder.losses.SparseMSELoss.SparseMSELoss", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.sparse_encoder.losses.SparseMSELoss", "mro": ["sentence_transformers.sparse_encoder.losses.SparseMSELoss.SparseMSELoss", "sentence_transformers.losses.MSELoss.MSELoss", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.losses.SparseMSELoss.SparseMSELoss.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "model"], "arg_types": ["sentence_transformers.sparse_encoder.losses.SparseMSELoss.SparseMSELoss", "sentence_transformers.sparse_encoder.SparseEncoder.SparseEncoder"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SparseMSELoss", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.sparse_encoder.losses.SparseMSELoss.SparseMSELoss.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.sparse_encoder.losses.SparseMSELoss.SparseMSELoss", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.losses.SparseMSELoss.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.losses.SparseMSELoss.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.losses.SparseMSELoss.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.losses.SparseMSELoss.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.losses.SparseMSELoss.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.losses.SparseMSELoss.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/sparse_encoder/losses/SparseMSELoss.py"}