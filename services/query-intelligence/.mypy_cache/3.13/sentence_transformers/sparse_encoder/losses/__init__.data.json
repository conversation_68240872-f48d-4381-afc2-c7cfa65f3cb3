{".class": "MypyFile", "_fullname": "sentence_transformers.sparse_encoder.losses", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CSRLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.losses.CSRLoss.CSRLoss", "kind": "Gdef"}, "CSRReconstructionLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.losses.CSRLoss.CSRReconstructionLoss", "kind": "Gdef"}, "FlopsLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.losses.FlopsLoss.FlopsLoss", "kind": "Gdef"}, "SparseAnglELoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.losses.SparseAnglELoss.SparseAnglELoss", "kind": "Gdef"}, "SparseCoSENTLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.losses.SparseCoSENTLoss.SparseCoSENTLoss", "kind": "Gdef"}, "SparseCosineSimilarityLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.losses.SparseCosineSimilarityLoss.SparseCosineSimilarityLoss", "kind": "Gdef"}, "SparseDistillKLDivLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.losses.SparseDistillKLDivLoss.SparseDistillKLDivLoss", "kind": "Gdef"}, "SparseMSELoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.losses.SparseMSELoss.SparseMSELoss", "kind": "Gdef"}, "SparseMarginMSELoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.losses.SparseMarginMSELoss.SparseMarginMSELoss", "kind": "Gdef"}, "SparseMultipleNegativesRankingLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.losses.SparseMultipleNegativesRankingLoss.SparseMultipleNegativesRankingLoss", "kind": "Gdef"}, "SparseTripletLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.losses.SparseTripletLoss.SparseTripletLoss", "kind": "Gdef"}, "SpladeLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.losses.SpladeLoss.SpladeLoss", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.sparse_encoder.losses.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.losses.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.losses.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.losses.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.losses.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.losses.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.losses.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.losses.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/sparse_encoder/losses/__init__.py"}