{"data_mtime": 1752049734, "dep_lines": [10, 10, 10, 9, 9, 10, 13, 8, 9, 1, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 20, 25, 5, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sentence_transformers.sparse_encoder.models.SparseAutoEncoder", "sentence_transformers.sparse_encoder.models.SparseStaticEmbedding", "sentence_transformers.sparse_encoder.models.SpladePooling", "sentence_transformers.models.Module", "sentence_transformers.models.Router", "sentence_transformers.sparse_encoder.models", "sentence_transformers.sparse_encoder.SparseEncoder", "sentence_transformers.model_card", "sentence_transformers.models", "__future__", "logging", "dataclasses", "pathlib", "typing", "builtins", "_collections_abc", "_frozen_importlib", "abc", "enum", "huggingface_hub", "huggingface_hub.repocard_data", "os", "sentence_transformers.SentenceTransformer", "sentence_transformers.evaluation", "sentence_transformers.evaluation.SentenceEvaluator", "sentence_transformers.fit_mixin", "sentence_transformers.models.InputModule", "sentence_transformers.peft_mixin", "torch", "torch._C", "torch._tensor", "torch.nn", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "transformers", "transformers.integrations", "transformers.integrations.integration_utils", "transformers.tokenization_utils", "transformers.tokenization_utils_base", "transformers.trainer_callback", "transformers.utils", "transformers.utils.hub", "types"], "hash": "10f68d1f3c6bc237911242eced564ab7b867e934", "id": "sentence_transformers.sparse_encoder.model_card", "ignore_all": true, "interface_hash": "431565cc3c8d970838ed48eb78dc306f08342c65", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/sparse_encoder/model_card.py", "plugin_data": null, "size": 5974, "suppressed": [], "version_id": "1.16.1"}