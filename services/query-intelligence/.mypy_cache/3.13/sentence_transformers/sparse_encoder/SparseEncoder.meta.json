{"data_mtime": 1752049734, "dep_lines": [21, 21, 21, 17, 17, 20, 21, 4, 9, 11, 14, 17, 18, 19, 22, 1, 3, 5, 6, 8, 10, 13, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 12], "dep_prios": [10, 10, 10, 10, 10, 5, 20, 5, 10, 10, 5, 20, 5, 5, 5, 5, 10, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["sentence_transformers.sparse_encoder.models.MLMTransformer", "sentence_transformers.sparse_encoder.models.SparseAutoEncoder", "sentence_transformers.sparse_encoder.models.SpladePooling", "sentence_transformers.models.Pooling", "sentence_transformers.models.Transformer", "sentence_transformers.sparse_encoder.model_card", "sentence_transformers.sparse_encoder.models", "collections.abc", "numpy.typing", "torch.nn", "transformers.modeling_utils", "sentence_transformers.models", "sentence_transformers.SentenceTransformer", "sentence_transformers.similarity_functions", "sentence_transformers.util", "__future__", "logging", "contextlib", "typing", "numpy", "torch", "transformers", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "abc", "enum", "huggingface_hub", "huggingface_hub.repocard_data", "numpy._typing", "numpy._typing._nbit_base", "os", "sentence_transformers.fit_mixin", "sentence_transformers.model_card", "sentence_transformers.models.InputModule", "sentence_transformers.models.Module", "sentence_transformers.peft_mixin", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "transformers.integrations", "transformers.integrations.peft", "transformers.models", "transformers.models.auto", "transformers.models.auto.configuration_auto", "transformers.utils", "transformers.utils.hub", "types", "warnings"], "hash": "977c2489633cd5a3eb70d278c7697b17f9e59998", "id": "sentence_transformers.sparse_encoder.SparseEncoder", "ignore_all": true, "interface_hash": "b42b8700e61b4b7d69c9ff844be7fdaa079bce34", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/sparse_encoder/SparseEncoder.py", "plugin_data": null, "size": 74117, "suppressed": ["tqdm"], "version_id": "1.16.1"}