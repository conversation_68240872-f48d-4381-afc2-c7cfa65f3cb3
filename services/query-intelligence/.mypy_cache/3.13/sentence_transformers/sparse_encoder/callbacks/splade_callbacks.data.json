{".class": "MypyFile", "_fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "SchedulerType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SchedulerType", "name": "SchedulerType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SchedulerType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks", "mro": ["sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SchedulerType", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "LINEAR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SchedulerType.LINEAR", "name": "LINEAR", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "linear"}, "type_ref": "builtins.str"}}}, "QUADRATIC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SchedulerType.QUADRATIC", "name": "QUADRATIC", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "quadratic"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SchedulerType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SchedulerType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SparseEncoderTrainingArguments": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.training_args.SparseEncoderTrainingArguments", "kind": "Gdef"}, "SpladeLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.sparse_encoder.losses.SpladeLoss.SpladeLoss", "kind": "Gdef"}, "SpladeRegularizerWeightSchedulerCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.trainer_callback.TrainerCallback"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback", "name": "SpladeRegularizerWeightSchedulerCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks", "mro": ["sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback", "transformers.trainer_callback.TrainerCallback", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "loss", "scheduler_type", "warmup_ratio"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "loss", "scheduler_type", "warmup_ratio"], "arg_types": ["sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback", "sentence_transformers.sparse_encoder.losses.SpladeLoss.SpladeLoss", {".class": "UnionType", "items": ["builtins.str", "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SchedulerType"], "uses_pep604_syntax": true}, "builtins.float"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SpladeRegularizerWeightSchedulerCallback", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_weight_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "step", "max_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback._calculate_weight_value", "name": "_calculate_weight_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "step", "max_value"], "arg_types": ["sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback", "builtins.int", "builtins.float"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_calculate_weight_value of SpladeRegularizerWeightSchedulerCallback", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_current_document_regularizer_weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback._current_document_regularizer_weight", "name": "_current_document_regularizer_weight", "setter_type": null, "type": "builtins.float"}}, "_current_query_regularizer_weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback._current_query_regularizer_weight", "name": "_current_query_regularizer_weight", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "loss": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback.loss", "name": "loss", "setter_type": null, "type": "sentence_transformers.sparse_encoder.losses.SpladeLoss.SpladeLoss"}}, "max_document_regularizer_weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback.max_document_regularizer_weight", "name": "max_document_regularizer_weight", "setter_type": null, "type": "builtins.float"}}, "max_query_regularizer_weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback.max_query_regularizer_weight", "name": "max_query_regularizer_weight", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "on_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "args", "state", "control", "model", "logs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback.on_log", "name": "on_log", "type": null}}, "on_step_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback.on_step_begin", "name": "on_step_begin", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "arg_types": ["sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback", "sentence_transformers.sparse_encoder.training_args.SparseEncoderTrainingArguments", "transformers.trainer_callback.TrainerState", "transformers.trainer_callback.TrainerControl", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_step_begin of SpladeRegularizerWeightSchedulerCallback", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_train_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback.on_train_begin", "name": "on_train_begin", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "args", "state", "control", "kwargs"], "arg_types": ["sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback", "sentence_transformers.sparse_encoder.training_args.SparseEncoderTrainingArguments", "transformers.trainer_callback.TrainerState", "transformers.trainer_callback.TrainerControl", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_train_begin of SpladeRegularizerWeightSchedulerCallback", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scheduler_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback.scheduler_type", "name": "scheduler_type", "setter_type": null, "type": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SchedulerType"}}, "total_steps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback.total_steps", "name": "total_steps", "setter_type": null, "type": {".class": "NoneType"}}}, "warmup_ratio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback.warmup_ratio", "name": "warmup_ratio", "setter_type": null, "type": "builtins.float"}}, "warmup_steps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback.warmup_steps", "name": "warmup_steps", "setter_type": null, "type": {".class": "NoneType"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.SpladeRegularizerWeightSchedulerCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TrainerCallback": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.TrainerCallback", "kind": "Gdef"}, "TrainerControl": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.TrainerControl", "kind": "Gdef"}, "TrainerState": {".class": "SymbolTableNode", "cross_ref": "transformers.trainer_callback.TrainerState", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/sparse_encoder/callbacks/splade_callbacks.py"}