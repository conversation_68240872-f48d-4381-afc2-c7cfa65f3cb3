{"data_mtime": 1752049734, "dep_lines": [8, 9, 6, 1, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sentence_transformers.sparse_encoder.losses.SpladeLoss", "sentence_transformers.sparse_encoder.training_args", "transformers.trainer_callback", "__future__", "logging", "enum", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "sentence_transformers.SentenceTransformer", "sentence_transformers.fit_mixin", "sentence_transformers.peft_mixin", "sentence_transformers.sparse_encoder.SparseEncoder", "sentence_transformers.sparse_encoder.losses", "sentence_transformers.training_args", "torch", "torch.nn", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "transformers", "transformers.training_args", "types", "typing"], "hash": "4823537b2a9f92f9cd7d54cdd8520b02d9a4eafc", "id": "sentence_transformers.sparse_encoder.callbacks.splade_callbacks", "ignore_all": true, "interface_hash": "6881f1ea3d0d0738af2aa474baff7c3ce56663cb", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/sparse_encoder/callbacks/splade_callbacks.py", "plugin_data": null, "size": 6501, "suppressed": [], "version_id": "1.16.1"}