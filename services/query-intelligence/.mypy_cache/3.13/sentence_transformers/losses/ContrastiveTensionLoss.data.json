{".class": "MypyFile", "_fullname": "sentence_transformers.losses.ContrastiveTensionLoss", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ContrastiveTensionDataLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionDataLoader", "name": "ContrastiveTensionDataLoader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionDataLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.losses.ContrastiveTensionLoss", "mro": ["sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionDataLoader", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "sentences", "batch_size", "pos_neg_ratio"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionDataLoader.__init__", "name": "__init__", "type": null}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionDataLoader.__iter__", "name": "__iter__", "type": null}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionDataLoader.__len__", "name": "__len__", "type": null}}, "batch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionDataLoader.batch_size", "name": "batch_size", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "collate_fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionDataLoader.collate_fn", "name": "collate_fn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pos_neg_ratio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionDataLoader.pos_neg_ratio", "name": "pos_neg_ratio", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "sentences": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionDataLoader.sentences", "name": "sentences", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionDataLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionDataLoader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ContrastiveTensionLoss": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLoss", "name": "ContrastiveTensionLoss", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLoss", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.losses.ContrastiveTensionLoss", "mro": ["sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLoss", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLoss.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "model"], "arg_types": ["sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLoss", "sentence_transformers.SentenceTransformer.SentenceTransformer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ContrastiveTensionLoss", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "citation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLoss.citation", "name": "citation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLoss"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "citation of ContrastiveTensionLoss", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLoss.citation", "name": "citation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLoss"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "citation of ContrastiveTensionLoss", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "criterion": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLoss.criterion", "name": "criterion", "setter_type": null, "type": "torch.nn.modules.loss.BCEWithLogitsLoss"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "sentence_features", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLoss.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "sentence_features", "labels"], "arg_types": ["sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLoss", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of ContrastiveTensionLoss", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLoss.model1", "name": "model1", "setter_type": null, "type": "sentence_transformers.SentenceTransformer.SentenceTransformer"}}, "model2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLoss.model2", "name": "model2", "setter_type": null, "type": "sentence_transformers.SentenceTransformer.SentenceTransformer"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLoss.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLoss", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ContrastiveTensionLossInBatchNegatives": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives", "name": "ContrastiveTensionLossInBatchNegatives", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.losses.ContrastiveTensionLoss", "mro": ["sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "model", "scale", "similarity_fct"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "model", "scale", "similarity_fct"], "arg_types": ["sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives", "sentence_transformers.SentenceTransformer.SentenceTransformer", "builtins.float", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ContrastiveTensionLossInBatchNegatives", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "citation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives.citation", "name": "citation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "citation of ContrastiveTensionLossInBatchNegatives", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives.citation", "name": "citation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "citation of ContrastiveTensionLossInBatchNegatives", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cross_entropy_loss": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives.cross_entropy_loss", "name": "cross_entropy_loss", "setter_type": null, "type": "torch.nn.modules.loss.CrossEntropyLoss"}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "sentence_features", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "sentence_features", "labels"], "arg_types": ["sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of ContrastiveTensionLossInBatchNegatives", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logit_scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives.logit_scale", "name": "logit_scale", "setter_type": null, "type": "torch.nn.parameter.Parameter"}}, "model1": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives.model1", "name": "model1", "setter_type": null, "type": "sentence_transformers.SentenceTransformer.SentenceTransformer"}}, "model2": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives.model2", "name": "model2", "setter_type": null, "type": "sentence_transformers.SentenceTransformer.SentenceTransformer"}}, "similarity_fct": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives.similarity_fct", "name": "similarity_fct", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InputExample": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.readers.InputExample.InputExample", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "SentenceTransformer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.SentenceTransformer.SentenceTransformer", "kind": "Gdef"}, "Tensor": {".class": "SymbolTableNode", "cross_ref": "torch._tensor.Tensor", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.ContrastiveTensionLoss.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "cos_sim": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util.cos_sim", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/losses/ContrastiveTensionLoss.py"}