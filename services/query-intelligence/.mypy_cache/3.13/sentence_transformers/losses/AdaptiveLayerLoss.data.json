{".class": "MypyFile", "_fullname": "sentence_transformers.losses.AdaptiveLayerLoss", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AdaptiveLayerLoss": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss", "name": "AdaptiveLayerLoss", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.losses.AdaptiveLayerLoss", "mro": ["sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "model", "loss", "n_layers_per_step", "last_layer_weight", "prior_layers_weight", "kl_div_weight", "kl_temperature"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "model", "loss", "n_layers_per_step", "last_layer_weight", "prior_layers_weight", "kl_div_weight", "kl_temperature"], "arg_types": ["sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss", "sentence_transformers.SentenceTransformer.SentenceTransformer", "torch.nn.modules.module.Module", "builtins.int", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AdaptiveLayerLoss", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "citation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss.citation", "name": "citation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "citation of AdaptiveLayerLoss", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss.citation", "name": "citation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "citation of AdaptiveLayerLoss", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "sentence_features", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "sentence_features", "labels"], "arg_types": ["sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of <PERSON>ptiveLayerLoss", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_config_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss.get_config_dict", "name": "get_config_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_config_dict of AdaptiveLayerLoss", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kl_div_weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss.kl_div_weight", "name": "kl_div_weight", "setter_type": null, "type": "builtins.float"}}, "kl_temperature": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss.kl_temperature", "name": "kl_temperature", "setter_type": null, "type": "builtins.float"}}, "last_layer_weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss.last_layer_weight", "name": "last_layer_weight", "setter_type": null, "type": "builtins.float"}}, "loss": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss.loss", "name": "loss", "setter_type": null, "type": "torch.nn.modules.module.Module"}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss.model", "name": "model", "setter_type": null, "type": "sentence_transformers.SentenceTransformer.SentenceTransformer"}}, "n_layers_per_step": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss.n_layers_per_step", "name": "n_layers_per_step", "setter_type": null, "type": "builtins.int"}}, "prior_layers_weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss.prior_layers_weight", "name": "prior_layers_weight", "setter_type": null, "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CachedGISTEmbedLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.CachedGISTEmbedLoss.CachedGISTEmbedLoss", "kind": "Gdef"}, "CachedMultipleNegativesRankingLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.CachedMultipleNegativesRankingLoss.CachedMultipleNegativesRankingLoss", "kind": "Gdef"}, "CachedMultipleNegativesSymmetricRankingLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss", "kind": "Gdef"}, "F": {".class": "SymbolTableNode", "cross_ref": "torch.nn.functional", "kind": "Gdef"}, "ForwardDecorator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.ForwardDecorator", "name": "ForwardDecorator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.ForwardDecorator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.losses.AdaptiveLayerLoss", "mro": ["sentence_transformers.losses.AdaptiveLayerLoss.ForwardDecorator", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "features"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.ForwardDecorator.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "features"], "arg_types": ["sentence_transformers.losses.AdaptiveLayerLoss.ForwardDecorator", {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of ForwardDecorator", "ret_type": {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.ForwardDecorator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fn"], "arg_types": ["sentence_transformers.losses.AdaptiveLayerLoss.ForwardDecorator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ForwardDecorator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.ForwardDecorator.embeddings", "name": "embeddings", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.ForwardDecorator.fn", "name": "fn", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.ForwardDecorator.get_embeddings", "name": "get_embeddings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.losses.AdaptiveLayerLoss.ForwardDecorator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_embeddings of ForwardDecorator", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.ForwardDecorator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.losses.AdaptiveLayerLoss.ForwardDecorator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "SentenceTransformer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.SentenceTransformer.SentenceTransformer", "kind": "Gdef"}, "Tensor": {".class": "SymbolTableNode", "cross_ref": "torch._tensor.Tensor", "kind": "Gdef"}, "Transformer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.Transformer.Transformer", "kind": "Gdef"}, "TransformerDecorator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator", "name": "TransformerDecorator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.losses.AdaptiveLayerLoss", "mro": ["sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "features"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "features"], "arg_types": ["sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of TransformerDecorator", "ret_type": {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "transformer", "original_forward"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "transformer", "original_forward"], "arg_types": ["sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator", "sentence_transformers.models.Transformer.Transformer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TransformerDecorator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_grow_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "features"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator.call_grow_cache", "name": "call_grow_cache", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "features"], "arg_types": ["sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator", {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "call_grow_cache of TransformerDecorator", "ret_type": {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_idx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator.call_idx", "name": "call_idx", "setter_type": null, "type": "builtins.int"}}, "call_use_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "features"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator.call_use_cache", "name": "call_use_cache", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "features"], "arg_types": ["sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator", {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "call_use_cache of TransformerDecorator", "ret_type": {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator.embeddings", "name": "embeddings", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "features": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator.features", "name": "features", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "get_layer_embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator.get_layer_embeddings", "name": "get_layer_embeddings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_layer_embeddings of TransformerDecorator", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "last_embeddings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator.last_embeddings", "name": "last_embeddings", "setter_type": null, "type": {".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "layer_idx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator.layer_idx", "name": "layer_idx", "setter_type": null, "type": {".class": "NoneType"}}}, "num_layers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator.num_layers", "name": "num_layers", "setter_type": null, "type": "builtins.int"}}, "original_forward": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator.original_forward", "name": "original_forward", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "set_layer_idx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "layer_idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator.set_layer_idx", "name": "set_layer_idx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "layer_idx"], "arg_types": ["sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "set_layer_idx of TransformerDecorator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transformer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator.transformer", "name": "transformer", "setter_type": null, "type": "sentence_transformers.models.Transformer.Transformer"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.losses.AdaptiveLayerLoss.TransformerDecorator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.AdaptiveLayerLoss.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/losses/AdaptiveLayerLoss.py"}