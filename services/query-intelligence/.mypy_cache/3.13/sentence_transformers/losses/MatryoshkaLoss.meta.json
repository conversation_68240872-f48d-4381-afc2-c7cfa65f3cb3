{"data_mtime": 1752049735, "dep_lines": [8, 12, 12, 12, 4, 8, 11, 12, 1, 3, 5, 7, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 10, 10, 20, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.nn.functional", "sentence_transformers.losses.CachedGISTEmbedLoss", "sentence_transformers.losses.CachedMultipleNegativesRankingLoss", "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss", "collections.abc", "torch.nn", "sentence_transformers.SentenceTransformer", "sentence_transformers.losses", "__future__", "random", "typing", "torch", "sentence_transformers", "builtins", "_frozen_importlib", "_typeshed", "abc", "sentence_transformers.fit_mixin", "sentence_transformers.peft_mixin", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.module", "types"], "hash": "8e213c747d2dd7fd2950f21d6978729158a20e43", "id": "sentence_transformers.losses.MatryoshkaLoss", "ignore_all": true, "interface_hash": "15baceddc75231bcfa821b487c66eb4b222bf863", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/losses/MatryoshkaLoss.py", "plugin_data": null, "size": 11163, "suppressed": [], "version_id": "1.16.1"}