{".class": "MypyFile", "_fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CachedMultipleNegativesSymmetricRankingLoss": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["torch.nn.modules.module.Module"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss", "name": "CachedMultipleNegativesSymmetricRankingLoss", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss", "mro": ["sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss", "torch.nn.modules.module.Module", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "model", "scale", "similarity_fct", "mini_batch_size", "show_progress_bar"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "model", "scale", "similarity_fct", "mini_batch_size", "show_progress_bar"], "arg_types": ["sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss", "sentence_transformers.SentenceTransformer.SentenceTransformer", "builtins.float", {".class": "UnboundType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "torch._tensor.Tensor"], "expr": null, "expr_fallback": null, "name": "callable"}, "builtins.int", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CachedMultipleNegativesSymmetricRankingLoss", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss.cache", "name": "cache", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "calculate_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "reps", "with_backward"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss.calculate_loss", "name": "calculate_loss", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "reps", "with_backward"], "arg_types": ["sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss", {".class": "Instance", "args": [{".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "calculate_loss of CachedMultipleNegativesSymmetricRankingLoss", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "calculate_loss_and_cache_gradients": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "reps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss.calculate_loss_and_cache_gradients", "name": "calculate_loss_and_cache_gradients", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "reps"], "arg_types": ["sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss", {".class": "Instance", "args": [{".class": "Instance", "args": ["torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "calculate_loss_and_cache_gradients of CachedMultipleNegativesSymmetricRankingLoss", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cross_entropy_loss": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss.cross_entropy_loss", "name": "cross_entropy_loss", "setter_type": null, "type": "torch.nn.modules.loss.CrossEntropyLoss"}}, "embed_minibatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "sentence_feature", "begin", "end", "with_grad", "copy_random_state", "random_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss.embed_minibatch", "name": "embed_minibatch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "sentence_feature", "begin", "end", "with_grad", "copy_random_state", "random_state"], "arg_types": ["sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss", {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["sentence_transformers.losses.CachedMultipleNegativesRankingLoss.RandContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_minibatch of CachedMultipleNegativesSymmetricRankingLoss", "ret_type": {".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", {".class": "UnionType", "items": ["sentence_transformers.losses.CachedMultipleNegativesRankingLoss.RandContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "embed_minibatch_iter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "sentence_feature", "with_grad", "copy_random_state", "random_states"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss.embed_minibatch_iter", "name": "embed_minibatch_iter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "sentence_feature", "with_grad", "copy_random_state", "random_states"], "arg_types": ["sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss", {".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["sentence_transformers.losses.CachedMultipleNegativesRankingLoss.RandContext"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_minibatch_iter of CachedMultipleNegativesSymmetricRankingLoss", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["torch._tensor.Tensor", {".class": "UnionType", "items": ["sentence_transformers.losses.CachedMultipleNegativesRankingLoss.RandContext", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "forward": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "sentence_features", "labels"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss.forward", "name": "forward", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "sentence_features", "labels"], "arg_types": ["sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "torch._tensor.Tensor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "forward of CachedMultipleNegativesSymmetricRankingLoss", "ret_type": "torch._tensor.Tensor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_config_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss.get_config_dict", "name": "get_config_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_config_dict of CachedMultipleNegativesSymmetricRankingLoss", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mini_batch_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss.mini_batch_size", "name": "mini_batch_size", "setter_type": null, "type": "builtins.int"}}, "model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss.model", "name": "model", "setter_type": null, "type": "sentence_transformers.SentenceTransformer.SentenceTransformer"}}, "random_states": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss.random_states", "name": "random_states", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["sentence_transformers.losses.CachedMultipleNegativesRankingLoss.RandContext"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "scale": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss.scale", "name": "scale", "setter_type": null, "type": "builtins.float"}}, "show_progress_bar": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss.show_progress_bar", "name": "show_progress_bar", "setter_type": null, "type": "builtins.bool"}}, "similarity_fct": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss.similarity_fct", "name": "similarity_fct", "setter_type": null, "type": {".class": "UnboundType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "torch._tensor.Tensor"], "expr": null, "expr_fallback": null, "name": "callable"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "RandContext": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.CachedMultipleNegativesRankingLoss.RandContext", "kind": "Gdef"}, "SentenceTransformer": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.SentenceTransformer.SentenceTransformer", "kind": "Gdef"}, "StaticEmbedding": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.models.StaticEmbedding.StaticEmbedding", "kind": "Gdef"}, "Tensor": {".class": "SymbolTableNode", "cross_ref": "torch._tensor.Tensor", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_backward_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["grad_output", "sentence_features", "loss_obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss._backward_hook", "name": "_backward_hook", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["grad_output", "sentence_features", "loss_obj"], "arg_types": ["torch._tensor.Tensor", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "torch._tensor.Tensor"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_backward_hook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "nn": {".class": "SymbolTableNode", "cross_ref": "torch.nn", "kind": "Gdef"}, "nullcontext": {".class": "SymbolTableNode", "cross_ref": "contextlib.nullcontext", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "torch": {".class": "SymbolTableNode", "cross_ref": "torch", "kind": "Gdef"}, "tqdm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.tqdm", "name": "tqdm", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.tqdm", "source_any": null, "type_of_any": 3}}}, "util": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.util", "kind": "Gdef"}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/losses/CachedMultipleNegativesSymmetricRankingLoss.py"}