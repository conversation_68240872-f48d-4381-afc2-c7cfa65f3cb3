{".class": "MypyFile", "_fullname": "sentence_transformers.losses", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AdaptiveLayerLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.AdaptiveLayerLoss.AdaptiveLayerLoss", "kind": "Gdef"}, "AnglELoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.AnglELoss.AnglELoss", "kind": "Gdef"}, "BatchAllTripletLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.BatchAllTripletLoss.BatchAllTripletLoss", "kind": "Gdef"}, "BatchHardSoftMarginTripletLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.BatchHardSoftMarginTripletLoss.BatchHardSoftMarginTripletLoss", "kind": "Gdef"}, "BatchHardTripletLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.BatchHardTripletLoss.BatchHardTripletLoss", "kind": "Gdef"}, "BatchHardTripletLossDistanceFunction": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.BatchHardTripletLoss.BatchHardTripletLossDistanceFunction", "kind": "Gdef"}, "BatchSemiHardTripletLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.BatchSemiHardTripletLoss.BatchSemiHardTripletLoss", "kind": "Gdef"}, "CachedGISTEmbedLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.CachedGISTEmbedLoss.CachedGISTEmbedLoss", "kind": "Gdef"}, "CachedMultipleNegativesRankingLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.CachedMultipleNegativesRankingLoss.CachedMultipleNegativesRankingLoss", "kind": "Gdef"}, "CachedMultipleNegativesSymmetricRankingLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss.CachedMultipleNegativesSymmetricRankingLoss", "kind": "Gdef"}, "CoSENTLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.CoSENTLoss.CoSENTLoss", "kind": "Gdef"}, "ContrastiveLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.ContrastiveLoss.ContrastiveLoss", "kind": "Gdef"}, "ContrastiveTensionDataLoader": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionDataLoader", "kind": "Gdef"}, "ContrastiveTensionLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLoss", "kind": "Gdef"}, "ContrastiveTensionLossInBatchNegatives": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.ContrastiveTensionLoss.ContrastiveTensionLossInBatchNegatives", "kind": "Gdef"}, "CosineSimilarityLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.CosineSimilarityLoss.CosineSimilarityLoss", "kind": "Gdef"}, "DenoisingAutoEncoderLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.DenoisingAutoEncoderLoss.DenoisingAutoEncoderLoss", "kind": "Gdef"}, "DistillKLDivLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.DistillKLDivLoss.DistillKLDivLoss", "kind": "Gdef"}, "GISTEmbedLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.GISTEmbedLoss.GISTEmbedLoss", "kind": "Gdef"}, "MSELoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.MSELoss.MSELoss", "kind": "Gdef"}, "MarginMSELoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.MarginMSELoss.MarginMSELoss", "kind": "Gdef"}, "Matryoshka2dLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.Matryoshka2dLoss.Matryoshka2dLoss", "kind": "Gdef"}, "MatryoshkaLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.MatryoshkaLoss.MatryoshkaLoss", "kind": "Gdef"}, "MegaBatchMarginLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.MegaBatchMarginLoss.MegaBatchMarginLoss", "kind": "Gdef"}, "MultipleNegativesRankingLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.MultipleNegativesRankingLoss.MultipleNegativesRankingLoss", "kind": "Gdef"}, "MultipleNegativesSymmetricRankingLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.MultipleNegativesSymmetricRankingLoss.MultipleNegativesSymmetricRankingLoss", "kind": "Gdef"}, "OnlineContrastiveLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.OnlineContrastiveLoss.OnlineContrastiveLoss", "kind": "Gdef"}, "SiameseDistanceMetric": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.ContrastiveLoss.SiameseDistanceMetric", "kind": "Gdef"}, "SoftmaxLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.SoftmaxLoss.SoftmaxLoss", "kind": "Gdef"}, "TripletDistanceMetric": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.TripletLoss.TripletDistanceMetric", "kind": "Gdef"}, "TripletLoss": {".class": "SymbolTableNode", "cross_ref": "sentence_transformers.losses.TripletLoss.TripletLoss", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sentence_transformers.losses.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sentence_transformers.losses.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/losses/__init__.py"}