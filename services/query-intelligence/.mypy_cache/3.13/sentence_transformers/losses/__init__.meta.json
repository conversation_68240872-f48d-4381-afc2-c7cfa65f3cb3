{"data_mtime": 1752049735, "dep_lines": [4, 6, 7, 8, 9, 10, 14, 15, 16, 17, 20, 21, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 2, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["sentence_transformers.losses.CoSENTLoss", "sentence_transformers.losses.AdaptiveLayerLoss", "sentence_transformers.losses.AnglELoss", "sentence_transformers.losses.BatchAllTripletLoss", "sentence_transformers.losses.BatchHardSoftMarginTripletLoss", "sentence_transformers.losses.BatchHardTripletLoss", "sentence_transformers.losses.BatchSemiHardTripletLoss", "sentence_transformers.losses.CachedGISTEmbedLoss", "sentence_transformers.losses.CachedMultipleNegativesRankingLoss", "sentence_transformers.losses.CachedMultipleNegativesSymmetricRankingLoss", "sentence_transformers.losses.ContrastiveLoss", "sentence_transformers.losses.ContrastiveTensionLoss", "sentence_transformers.losses.CosineSimilarityLoss", "sentence_transformers.losses.DenoisingAutoEncoderLoss", "sentence_transformers.losses.DistillKLDivLoss", "sentence_transformers.losses.GISTEmbedLoss", "sentence_transformers.losses.MarginMSELoss", "sentence_transformers.losses.Matryoshka2dLoss", "sentence_transformers.losses.MatryoshkaLoss", "sentence_transformers.losses.MegaBatchMarginLoss", "sentence_transformers.losses.MSELoss", "sentence_transformers.losses.MultipleNegativesRankingLoss", "sentence_transformers.losses.MultipleNegativesSymmetricRankingLoss", "sentence_transformers.losses.OnlineContrastiveLoss", "sentence_transformers.losses.SoftmaxLoss", "sentence_transformers.losses.TripletLoss", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "d5bf1646517274d1ad4e7b064f223af7ab006abd", "id": "sentence_transformers.losses", "ignore_all": true, "interface_hash": "0d04fbe249bb3530a57b25e981f4601026fa17b6", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/losses/__init__.py", "plugin_data": null, "size": 2698, "suppressed": [], "version_id": "1.16.1"}