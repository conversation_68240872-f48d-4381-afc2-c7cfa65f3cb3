{"data_mtime": 1752049734, "dep_lines": [11, 14, 3, 10, 13, 13, 14, 1, 4, 5, 6, 8, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 9], "dep_prios": [5, 10, 5, 10, 10, 10, 20, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["torch.utils.checkpoint", "sentence_transformers.models.StaticEmbedding", "collections.abc", "torch.nn", "sentence_transformers.SentenceTransformer", "sentence_transformers.util", "sentence_transformers.models", "__future__", "contextlib", "functools", "typing", "torch", "sentence_transformers", "builtins", "_frozen_importlib", "abc", "numpy", "sentence_transformers.fit_mixin", "sentence_transformers.models.InputModule", "sentence_transformers.models.Module", "sentence_transformers.peft_mixin", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.container", "torch.nn.modules.loss", "torch.nn.modules.module", "torch.random", "torch.utils", "transformers", "transformers.utils", "transformers.utils.dummy_tokenizers_objects", "transformers.utils.import_utils", "types"], "hash": "6a04df43b210d5c7216bbe8378b4875af976791c", "id": "sentence_transformers.losses.CachedMultipleNegativesRankingLoss", "ignore_all": true, "interface_hash": "6b0a7cc94d1322a94821ff29829c55fbdaff31ea", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/losses/CachedMultipleNegativesRankingLoss.py", "plugin_data": null, "size": 14493, "suppressed": ["tqdm"], "version_id": "1.16.1"}