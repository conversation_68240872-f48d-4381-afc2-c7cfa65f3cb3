{"data_mtime": 1752049734, "dep_lines": [15, 21, 21, 25, 29, 10, 15, 21, 22, 23, 24, 25, 26, 27, 28, 29, 35, 36, 37, 1, 6, 7, 8, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 5, 5, 20, 5, 5, 5, 20, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 30, 30, 30, 30], "dependencies": ["sentence_transformers.cross_encoder.CrossEncoder", "sentence_transformers.datasets.ParallelSentencesDataset", "sentence_transformers.datasets.SentencesDataset", "sentence_transformers.readers.InputExample", "sentence_transformers.sparse_encoder.SparseEncoder", "sentence_transformers.backend", "sentence_transformers.cross_encoder", "sentence_transformers.datasets", "sentence_transformers.LoggingHandler", "sentence_transformers.model_card", "sentence_transformers.quantization", "sentence_transformers.readers", "sentence_transformers.sampler", "sentence_transformers.SentenceTransformer", "sentence_transformers.similarity_functions", "sentence_transformers.sparse_encoder", "sentence_transformers.trainer", "sentence_transformers.training_args", "sentence_transformers.util", "__future__", "importlib", "os", "warnings", "builtins", "_frozen_importlib", "abc", "importlib.util", "typing"], "hash": "b0b74c5e4ad7b4889219486706b525fdbebae5c9", "id": "sentence_transformers", "ignore_all": true, "interface_hash": "d1cdeb0a06a87e2142d723ef64fe25bb8f0cf5eb", "mtime": 1751921842, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/sentence_transformers/__init__.py", "plugin_data": null, "size": 2688, "suppressed": [], "version_id": "1.16.1"}