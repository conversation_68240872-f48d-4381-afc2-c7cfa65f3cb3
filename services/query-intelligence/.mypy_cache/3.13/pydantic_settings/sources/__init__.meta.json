{"data_mtime": **********, "dep_lines": [11, 12, 13, 24, 25, 26, 27, 28, 29, 30, 31, 3, 32, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["pydantic_settings.sources.providers.aws", "pydantic_settings.sources.providers.azure", "pydantic_settings.sources.providers.cli", "pydantic_settings.sources.providers.dotenv", "pydantic_settings.sources.providers.env", "pydantic_settings.sources.providers.gcp", "pydantic_settings.sources.providers.json", "pydantic_settings.sources.providers.pyproject", "pydantic_settings.sources.providers.secrets", "pydantic_settings.sources.providers.toml", "pydantic_settings.sources.providers.yaml", "pydantic_settings.sources.base", "pydantic_settings.sources.types", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "77c718e24056db881aef87bd704b5354843675b4", "id": "pydantic_settings.sources", "ignore_all": true, "interface_hash": "94f4f3ff2a0e1bb8000f9c9c9925f1f6e7d69b19", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/pydantic_settings/sources/__init__.py", "plugin_data": null, "size": 2052, "suppressed": [], "version_id": "1.16.1"}