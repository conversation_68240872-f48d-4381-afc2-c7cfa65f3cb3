{"data_mtime": **********, "dep_lines": [7, 11, 12, 3, 10, 14, 1, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 25, 5, 25, 25, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic_settings.sources.providers.env", "google.auth.credentials", "google.cloud.secretmanager", "collections.abc", "google.auth", "pydantic_settings.main", "__future__", "functools", "typing", "builtins", "_collections_abc", "_frozen_importlib", "abc", "google", "google.api_core", "google.api_core.client_info", "google.api_core.client_options", "google.api_core.gapic_v1", "google.api_core.gapic_v1.client_info", "google.auth._credentials_base", "google.auth._default", "google.cloud", "google.cloud.secretmanager_v1", "google.cloud.secretmanager_v1.services", "google.cloud.secretmanager_v1.services.secret_manager_service", "google.cloud.secretmanager_v1.services.secret_manager_service.client", "google.cloud.secretmanager_v1.services.secret_manager_service.transports", "google.cloud.secretmanager_v1.services.secret_manager_service.transports.base", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_settings.sources.base", "types"], "hash": "297b7e3ef0390b90d460261aede450c1c0805170", "id": "pydantic_settings.sources.providers.gcp", "ignore_all": true, "interface_hash": "27f7b2ca4ec63832ce888ff04ccea862335d2dfd", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/query-intelligence-8JKVUpXY-py3.13/lib/python3.13/site-packages/pydantic_settings/sources/providers/gcp.py", "plugin_data": null, "size": 5644, "suppressed": [], "version_id": "1.16.1"}