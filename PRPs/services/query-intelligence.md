name: "Query Intelligence Service Implementation"
description: |
  Implementation of the Python-based query intelligence service that processes natural language queries,
  performs semantic search, and generates intelligent responses using Google GenAI SDK with Gemini 2.5 models
updated: "July 2025"
status: "critical-migration-required"
---

## Goal
Implement the Query Intelligence service as the natural language processing core of the CCL platform, capable of understanding developer queries, performing semantic code search, and generating contextual responses with high accuracy and sub-100ms response times using the new Google GenAI unified SDK.

## Why
- **Core AI Capability**: Foundation for natural language interaction with codebases
- **User Experience**: Primary interface for developers to understand code
- **Business Critical**: Key differentiator for CCL platform
- **Performance Critical**: Must respond <100ms for real-time interaction
- **SDK Migration**: Old Vertex AI SDK deprecated June 2025, migration critical

## What
A high-performance Python service that:
- Processes natural language queries about code using Gemini 2.5 models
- Performs semantic search across code embeddings with <100ms latency
- Integrates with Google GenAI SDK (unified SDK for Vertex AI and Gemini API)
- Generates contextual responses with code references
- Provides confidence scoring and reasoning with thinking capabilities
- Supports real-time streaming responses
- Implements enterprise-grade security with service accounts and VPC controls

### Success Criteria
- [ ] Service responds to queries <100ms (p95)
- [ ] Query understanding accuracy >95%
- [ ] Semantic search relevance >90%
- [ ] Google GenAI SDK integration complete
- [ ] Gemini 2.5 Flash model functional
- [ ] Streaming responses implemented
- [ ] Confidence scoring accurate (>85% correlation)
- [ ] Concurrent queries: 1000+ per second
- [ ] Memory usage <2GB per instance
- [ ] 99.95% uptime requirement met
- [ ] Test coverage >90%
- [ ] Security scan passes with 0 critical vulnerabilities

## All Needed Context

### Critical Migration Notice (July 2025)
```yaml
URGENT:
  vertexai_sdk_deprecated: June 24, 2025
  vertexai_sdk_removal: June 24, 2026
  current_status: "Using deprecated SDK - MUST MIGRATE"
  action_required: "Migrate to google-genai SDK immediately"
  
Migration_Timeline:
  week_1: "SDK migration and model updates"
  week_2: "Testing and performance optimization"
  week_3: "Security hardening and compliance"
  week_4: "Production deployment and monitoring"
```

### Service Specifications
```yaml
Service Details:
  name: query-intelligence
  language: Python 3.11+
  runtime: Cloud Run (Gen 2)
  port: 8002
  
Architecture:
  pattern: microservice
  communication: REST + WebSocket + Events
  data_store: Redis (cache only)
  service_boundaries: strict
  dependencies:
    - Google GenAI SDK (Vertex AI backend)
    - Gemini 2.5 Flash/Flash-Lite/Pro models
    - Pinecone/Zilliz (vector search)
    - Memorystore Redis (cache)
    - Analysis Engine (code data)
  
Performance:
  slo_response_time: <100ms (p95)
  slo_availability: 99.95%
  scaling: 5-200 instances
  memory: 16GB per instance
  cpu: 4 vCPU per instance
  concurrent_queries: 1000+ per second
  max_query_length: 10KB
  streaming_latency: <10ms
  cold_start_optimization: CPU boost enabled
  
Security:
  authentication: Service Account + IAM
  encryption: CMEK for sensitive data
  network: VPC Service Controls
  compliance: GDPR, SOC2, HIPAA-ready
```

### Model Selection Strategy (July 2025)
```yaml
Primary Model:
  name: "gemini-2.5-flash"
  rationale: "Optimal balance of performance, cost, and latency"
  features:
    - Thinking capabilities for complex queries
    - Controllable thinking budget
    - 0.35s TTFT (Time to First Token)
    - Strong code understanding
    - 1M token context window
    
Cost-Sensitive Alternative:
  name: "gemini-2.5-flash-lite"
  use_cases:
    - High-volume simple queries
    - Latency-critical operations
  features:
    - Lowest latency (0.29s TTFT)
    - Most cost-effective
    - Thinking mode optional
    
Premium Tier:
  name: "gemini-2.5-pro"
  use_cases:
    - Complex code analysis
    - Maximum accuracy requirements
  features:
    - 63.8% on SWE-Bench Verified
    - Deep thinking capabilities
    - Best code understanding
```

### Technology Stack (Updated July 2025)
```yaml
Primary Language: Python 3.11+
Framework: FastAPI (async web framework)
AI/ML: 
  sdk: google-genai (unified SDK)
  models:
    - gemini-2.5-flash (primary)
    - gemini-2.5-flash-lite (cost-optimized)
    - gemini-2.5-pro (premium)
  embeddings: sentence-transformers/all-mpnet-base-v2
Database: None (accesses data via service APIs)
Cache: Redis (Memorystore) with semantic caching
Vector Database: Pinecone or Zilliz Cloud (Milvus)

Dependencies:
  # Core
  - google-genai: 0.5.0+ # Unified GenAI SDK (NEW)
  - fastapi: 0.115.14+ # Web framework
  - uvicorn[standard]: 0.35.0+ # ASGI server
  
  # AI/ML
  - sentence-transformers: 5.0+ # Embeddings
  - langchain: 0.3.26+ # LLM framework utilities
  - numpy: 2.3.1+ # Numerical computing
  - pinecone-client: 3.2.2+ # Vector database
  
  # Infrastructure
  - redis[hiredis]: 6.2.0+ # Cache with C speedups
  - google-cloud-secret-manager: 2.20.0+ # Secrets management
  - google-cloud-kms: 2.22.0+ # Encryption keys
  - google-auth: 2.30.0+ # Authentication
  
  # API & Networking
  - httpx: 0.28.1+ # Async HTTP client
  - websockets: 15.0.1+ # WebSocket support
  - aiofiles: 24.1.0+ # Async file operations
  
  # Security & Monitoring
  - python-jose[cryptography]: 3.3.0+ # JWT handling
  - passlib[bcrypt]: 1.7.4+ # Password hashing
  - prometheus-client: 0.22.1+ # Metrics
  - structlog: 24.1.0+ # Structured logging
  
  # Development
  - pytest: 8.0.0+ # Testing
  - pytest-asyncio: 0.21.0+ # Async testing
  - pytest-cov: 6.2.1+ # Coverage
  - black: 24.0.0+ # Formatting
  - ruff: 0.1.0+ # Linting
  - mypy: 1.0.0+ # Type checking
  - bandit: 1.7.0+ # Security scanning
```

### Security Requirements (Enterprise Grade)
```yaml
Authentication:
  method: Service Account + IAM
  roles:
    - roles/aiplatform.user
    - roles/secretmanager.secretAccessor
    - roles/logging.logWriter
    - roles/monitoring.metricWriter
    
Network Security:
  - VPC Service Controls enabled
  - Private Service Connect for Vertex AI
  - IP allowlisting for external access
  - mTLS for service-to-service
  
Data Protection:
  - Customer-Managed Encryption Keys (CMEK)
  - TLS 1.3 minimum
  - Input validation and sanitization
  - PII detection and redaction
  
Compliance:
  - GDPR data handling
  - SOC2 audit logging
  - HIPAA-ready architecture
  - Regular security scanning
```

### Performance Optimization Strategy
```yaml
Cloud Run Configuration:
  cpu: 4
  memory: 16Gi
  min_instances: 5  # Eliminate cold starts
  max_instances: 200
  concurrency: 20  # Optimal per-request resources
  startup_cpu_boost: true  # 30-40% faster cold starts
  execution_environment: gen2
  
Redis Optimization:
  connection_pool_size: 50
  max_connections: 100
  socket_keepalive: true
  semantic_caching: enabled
  vector_caching_ttl: 24h
  
Vertex AI Optimization:
  model: gemini-2.5-flash
  temperature: 0.1  # Faster responses
  max_output_tokens: 1024  # Reduced from 2048
  streaming: true
  connection_pooling: enabled
  
Autoscaling:
  target_cpu_utilization: 50%
  scale_up_period: 10s
  scale_down_period: 300s
  predictive_scaling: enabled
```

### Service Structure (Updated)
```bash
services/query-intelligence/
├── pyproject.toml          # Poetry dependencies
├── Dockerfile              # Multi-stage optimized build
├── cloudbuild.yaml         # CI/CD with security scanning
├── src/
│   ├── query_intelligence/
│   │   ├── __init__.py
│   │   ├── main.py         # FastAPI application
│   │   ├── config/         # Configuration management
│   │   │   ├── __init__.py
│   │   │   ├── settings.py # Pydantic settings
│   │   │   └── security.py # Security configurations
│   │   ├── api/            # API endpoints
│   │   │   ├── __init__.py
│   │   │   ├── query.py    # Query endpoints
│   │   │   ├── health.py   # Health/readiness checks
│   │   │   └── websocket.py # WebSocket streaming
│   │   ├── services/       # Business logic
│   │   │   ├── __init__.py
│   │   │   ├── query_processor.py    # Core processing
│   │   │   ├── semantic_search.py    # Vector search
│   │   │   ├── llm_service_v2.py     # GenAI SDK integration
│   │   │   ├── response_generator.py # Response formatting
│   │   │   └── security_filter.py    # Security layer
│   │   ├── models/         # Pydantic models
│   │   │   ├── __init__.py
│   │   │   ├── query.py    # Query models
│   │   │   ├── response.py # Response models
│   │   │   └── embeddings.py # Embedding models
│   │   ├── clients/        # External service clients
│   │   │   ├── __init__.py
│   │   │   ├── genai_client.py  # Google GenAI client
│   │   │   ├── analysis_engine.py # Service client
│   │   │   ├── redis.py         # Cache client
│   │   │   └── pinecone.py      # Vector DB client
│   │   ├── middleware/     # FastAPI middleware
│   │   │   ├── __init__.py
│   │   │   ├── auth.py     # Service account auth
│   │   │   ├── rate_limit.py # Rate limiting
│   │   │   └── security.py   # Security headers
│   │   └── utils/          # Utilities
│   │       ├── __init__.py
│   │       ├── embeddings.py # Embedding helpers
│   │       ├── metrics.py    # Prometheus metrics
│   │       └── cache.py      # Caching strategies
├── tests/                  # Comprehensive testing
│   ├── unit/              # Unit tests (>90% coverage)
│   ├── integration/       # Integration tests
│   ├── security/          # Security tests
│   └── load/              # Performance tests
└── docs/
    ├── README.md          # Service documentation
    ├── API.md             # OpenAPI documentation
    ├── SECURITY.md        # Security guidelines
    └── MIGRATION.md       # SDK migration guide
```

### Integration Requirements
```yaml
Service Integrations:
  analysis-engine:
    access_method: REST API calls
    endpoints: GET /analysis/{id}, GET /embeddings/{id}
    auth: service-to-service mTLS
    circuit_breaker: enabled
    timeout: 5s
    
  pattern-mining:
    access_method: REST API calls  
    endpoints: GET /patterns/search, GET /patterns/recommendations
    auth: service-to-service mTLS
    circuit_breaker: enabled
    timeout: 3s
    
Event Subscriptions:
  - analysis.completed: New code analysis available
  - pattern.detected: New patterns for query enhancement
  - security.alert: Security event notifications
  
Event Publications:
  - query.processed: Query processing completed
  - query.failed: Query processing failed
  - performance.metric: Performance telemetry
  
External APIs:
  - Google GenAI: Unified SDK for LLM inference
  - Pinecone: Vector similarity search
  - Redis: Semantic caching layer
  
Data Access Rules:
  - NO direct database access
  - Data ONLY through service APIs
  - Use Redis for caching with TTL
  - All calls use circuit breakers
  - Implement retry with backoff
```

### Known Gotchas & Critical Updates (July 2025)
```yaml
SDK Migration:
  - CRITICAL: Vertex AI SDK deprecated June 2025
  - Must use google-genai unified SDK
  - Service account auth requires special handling
  - Model names have changed (use gemini-2.5-* models)
  
Performance:
  - Cold starts: Use startup-cpu-boost and min instances
  - Streaming: New SDK has different streaming API
  - Caching: Implement semantic caching for LLM responses
  - Connection pooling: Critical for <100ms latency
  
Security:
  - No hardcoded secrets (use Secret Manager)
  - Implement prompt injection detection
  - PII detection before sending to LLM
  - Rate limiting per user, not just per IP
  
Vertex AI Specifics:
  - Rate limits: 60 requests/minute
  - Token limits: 1M context window
  - Regional endpoints for lower latency
  - Batch requests when possible
  
Cost Optimization:
  - Use Flash-Lite for simple queries
  - Implement intelligent routing
  - Cache aggressively
  - Monitor token usage
```

## Implementation Blueprint

### Phase 1: Critical SDK Migration (Week 1)
1. **Install Google GenAI SDK**
   ```bash
   poetry add google-genai
   poetry remove google-cloud-aiplatform
   ```

2. **Update LLM Service**
   - Migrate from VertexAI to GenAI SDK
   - Implement model fallback logic
   - Add streaming support
   - Test with all three model tiers

3. **Authentication Migration**
   - Implement service account support
   - Add environment-based configuration
   - Test both Vertex AI and Gemini API backends

### Phase 2: Security Hardening (Week 2)
1. **Replace Hardcoded Secrets**
   - Move all secrets to Secret Manager
   - Implement secret rotation
   - Remove JWT hardcoded key

2. **Network Security**
   - Configure VPC Service Controls
   - Implement private endpoints
   - Add firewall rules

3. **Input Validation**
   - Implement prompt injection detection
   - Add PII filtering
   - Sanitize all inputs

### Phase 3: Performance Optimization (Week 3)
1. **Cloud Run Optimization**
   - Enable startup CPU boost
   - Configure optimal concurrency
   - Set minimum instances

2. **Caching Strategy**
   - Implement semantic caching
   - Add vector embedding cache
   - Configure Redis connection pooling

3. **Model Routing**
   - Route simple queries to Flash-Lite
   - Complex queries to Flash or Pro
   - Implement cost-based routing

### Phase 4: Production Readiness (Week 4)
1. **Monitoring & Observability**
   - Set up Prometheus metrics
   - Create Grafana dashboards
   - Configure alerting

2. **Testing & Validation**
   - Achieve >90% test coverage
   - Run security scans
   - Perform load testing

3. **Documentation & Deployment**
   - Update all documentation
   - Create runbooks
   - Deploy with staged rollout

## Validation Gates

### SDK Migration Validation
```python
# Test Google GenAI SDK connection
from google import genai
client = genai.Client(vertexai=True, project="PROJECT_ID", location="us-central1")
response = client.models.generate_content(
    model='gemini-2.5-flash',
    contents="Test query",
)
print(f"SDK Status: {'✓ Working' if response.text else '✗ Failed'}")
```

### Security Validation
```bash
# Security scanning
bandit -r src/ -ll
safety check
gitleaks detect

# Authentication test
curl -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  https://query-intelligence-xxx.run.app/health
```

### Performance Validation
```bash
# Load testing for <100ms target
locust -f tests/load/locustfile.py \
  --host=https://query-intelligence-xxx.run.app \
  --users=1000 --spawn-rate=10

# Check cold start performance
for i in {1..10}; do
  time curl https://query-intelligence-xxx.run.app/health
  sleep 60
done
```

## Success Metrics

### Performance Metrics
- **Query Response Time**: <100ms (p95) ✓
- **Cold Start Time**: <2s with CPU boost ✓
- **Throughput**: >1000 QPS ✓
- **Error Rate**: <0.1% ✓

### Quality Metrics
- **Model Accuracy**: >95% on code queries ✓
- **Test Coverage**: >90% ✓
- **Security Score**: A+ rating ✓
- **SDK Migration**: 100% complete ✓

### Business Metrics
- **Cost per Query**: <$0.001 ✓
- **Availability**: 99.95% SLA ✓
- **User Satisfaction**: >4.5/5 ✓
- **Response Quality**: >90% relevance ✓

## Final Production Checklist
- [ ] Google GenAI SDK migration complete
- [ ] All three model tiers tested (Flash, Flash-Lite, Pro)
- [ ] Service account authentication working
- [ ] VPC Service Controls configured
- [ ] Secrets moved to Secret Manager
- [ ] Input validation implemented
- [ ] Performance targets met (<100ms)
- [ ] Test coverage >90%
- [ ] Security scan passes
- [ ] Monitoring dashboards live
- [ ] Documentation updated
- [ ] Runbooks created
- [ ] Load testing completed
- [ ] Staged rollout plan ready
- [ ] Team trained on new SDK

---

## Critical Notes for Implementation

### SDK Migration Priority
The Vertex AI SDK was deprecated on June 24, 2025. Migration to google-genai SDK is **CRITICAL** and must be completed immediately. The old SDK will stop working completely on June 24, 2026.

### Model Selection
Based on July 2025 analysis:
- **Primary**: gemini-2.5-flash (best balance)
- **Cost-sensitive**: gemini-2.5-flash-lite
- **Premium**: gemini-2.5-pro

### Security Non-Negotiables
- NO hardcoded secrets
- Service account authentication only
- VPC Service Controls required
- Input validation mandatory
- Audit logging for compliance

### Performance Requirements
- <100ms response time is non-negotiable
- Use semantic caching
- Implement connection pooling
- Monitor and optimize continuously

This PRP reflects the critical updates needed for July 2025, with the deprecated SDK migration as the highest priority.