# 🚀 Analysis Engine Documentation

## Overview

The Analysis Engine is a high-performance, cloud-native code analysis service built in Rust that powers the CCL (Codebase Context Layer) platform. It provides AST-based pattern detection, multi-language support, and real-time analysis capabilities for codebases of any size.

## 📋 Table of Contents

### Getting Started
- [Quick Start Guide](#quick-start)
- [Installation & Setup](#installation)
- [Configuration](#configuration)

### Core Documentation
- [📐 Architecture Guide](./architecture/README.md) - System design and component overview
- [🔌 API Documentation](./api/README.md) - REST and WebSocket API reference
- [💻 Developer Guide](./guides/developer-guide.md) - Development setup and debugging
- [🚀 Operations Guide](./guides/operations-guide.md) - Deployment and monitoring
- [⚡ Performance Tuning Guide](./guides/performance-tuning.md) - Optimization strategies
- [🔧 Troubleshooting Guide](./troubleshooting/README.md) - Common issues and solutions
- [🌍 Language Support Guide](./guides/language-support.md) - Supported languages and parsers
- [🔒 Security Guide](./guides/security-guide.md) - Security best practices

## 🎯 Key Features

### 1. **Multi-Language AST Analysis**
- Currently supports 19 languages:
  - **Tree-sitter based (16)**: Rust, JavaScript, TypeScript, Python, Go, Java, C, C++, HTML, CSS, JSON, YAML, PHP, Ruby, Bash, Markdown
  - **Custom adapters (3)**: SQL, XML, TOML
- Tree-sitter based parsing for accurate AST generation
- Custom adapter pattern for languages with version conflicts
- Thread-safe concurrent parsing with RwLock
- Memory-safe operations with comprehensive error handling

### 2. **Real-time Pattern Detection**
- AST-based pattern matching (not string-based)
- Support for structural and semantic patterns
- WebSocket progress tracking for long-running analyses

### 3. **Enterprise-Scale Performance**
- Analyzes 1M lines of code in <5 minutes
- API response times <100ms (p95)
- Handles 100+ concurrent analyses
- Memory-optimized for large codebases

### 4. **Production-Ready Cloud Architecture**
- Google Cloud Platform integration with circuit breakers
- Vertex AI for embeddings generation with resilience patterns
- Spanner for transactional data with connection pooling
- Cloud Storage for artifacts with intelligent caching
- Pub/Sub for event-driven communication
- Redis for caching with git commit hash validation

### 5. **Security & Compliance**
- JWT-based authentication
- Per-user rate limiting
- API key validation
- SOC2, HIPAA, GDPR, CCPA compliant design

## 🚀 Quick Start

### Prerequisites
- Rust 1.70+ with cargo
- Google Cloud SDK
- Redis (optional, for caching)
- Access to GCP services (Spanner, Cloud Storage, Vertex AI)

### Local Development
```bash
# Clone the repository
git clone https://github.com/your-org/episteme.git
cd episteme/services/analysis-engine

# Copy environment template
cp .env.example .env
# Edit .env with your configuration

# Install dependencies and build
cargo build --release

# Run tests
cargo test

# Start the service
cargo run --release
```

### Docker Development
```bash
# Build the container
docker build -t analysis-engine .

# Run with environment variables
docker run -p 8001:8001 \
  -e RUST_LOG=info \
  -e GCP_PROJECT_ID=your-project \
  -e SPANNER_INSTANCE_ID=your-instance \
  -e SPANNER_DATABASE_ID=your-database \
  analysis-engine
```

## 📡 API Overview

### REST Endpoints

#### Health Check
```http
GET /health
```

#### Analyze Repository
```http
POST /api/v1/analyze
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "repository_url": "https://github.com/org/repo",
  "branch": "main",
  "patterns": ["auth", "security"],
  "languages": ["rust", "python"]
}
```

#### Get Analysis Results
```http
GET /api/v1/analyses/{analysis_id}
Authorization: Bearer <jwt-token>
```

### WebSocket Progress Tracking
```javascript
const ws = new WebSocket('ws://localhost:8001/ws/progress/{analysis_id}');

ws.onmessage = (event) => {
  const progress = JSON.parse(event.data);
  console.log(`Progress: ${progress.percentage}% - ${progress.message}`);
};
```

## 🏗️ Architecture Overview

The Analysis Engine follows a modular, service-oriented architecture:

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   API Gateway   │────▶│  Analysis Core  │────▶│  Parser Engine  │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │                       │                        │
         ▼                       ▼                        ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│ Authentication  │     │ Pattern Detector │     │   Tree-sitter   │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │                       │                        
         ▼                       ▼                        
┌─────────────────┐     ┌─────────────────┐     
│  Rate Limiting  │     │   Embeddings    │     
└─────────────────┘     └─────────────────┘     
         │                       │
         ▼                       ▼
┌─────────────────────────────────────────┐
│          Google Cloud Platform          │
│  (Spanner, Storage, Vertex AI, Pub/Sub) │
└─────────────────────────────────────────┘
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `RUST_LOG` | Logging level | `info` | No |
| `PORT` | Service port | `8001` | No |
| `GCP_PROJECT_ID` | Google Cloud project ID | - | Yes |
| `SPANNER_INSTANCE_ID` | Spanner instance ID | - | Yes |
| `SPANNER_DATABASE_ID` | Spanner database ID | - | Yes |
| `STORAGE_BUCKET` | Cloud Storage bucket | - | Yes |
| `PUBSUB_TOPIC` | Pub/Sub topic for events | - | Yes |
| `VERTEX_AI_LOCATION` | Vertex AI region | `us-central1` | No |
| `REDIS_URL` | Redis connection URL | - | No |
| `JWT_SECRET` | JWT signing secret | - | Yes |
| `RATE_LIMIT_WINDOW` | Rate limit window (seconds) | `3600` | No |
| `MAX_FILE_SIZE` | Maximum file size (bytes) | `104857600` | No |

### Performance Tuning

Key configuration options for performance:

```toml
# In Cargo.toml for compile-time optimizations
[profile.release]
lto = true
codegen-units = 1
opt-level = 3

# Runtime configuration
ANALYSIS_BATCH_SIZE=100       # Files processed per batch
PARSER_TIMEOUT=30            # Seconds per file
WORKER_THREADS=8             # Parallel processing threads
MEMORY_LIMIT_GB=4            # Maximum memory usage
```

## 📊 Monitoring & Observability

### Metrics
- Request latency (p50, p95, p99)
- Analysis duration by repository size
- Memory usage patterns
- Cache hit rates
- Rate limit violations
- Language parser performance

### Logging
Structured logging with correlation IDs:
```rust
INFO analysis_started request_id=abc123 repo=github.com/org/repo files=1523
INFO pattern_detected request_id=abc123 pattern=authentication count=15
INFO analysis_completed request_id=abc123 duration_ms=4523 patterns_found=42
```

### Health Endpoints
- `/health` - Basic health check
- `/health/ready` - Readiness probe (checks dependencies)
- `/health/live` - Liveness probe

## 🚨 Common Issues

### 1. Authentication Errors
- Ensure GCP credentials are properly configured
- Check JWT token expiration
- Verify API key permissions in Spanner

### 2. Performance Issues
- Monitor memory usage for large repositories
- Check file size limits for streaming
- Verify batch processing configuration

### 3. Language Support
- Some languages require additional parser setup
- Check Tree-sitter compatibility for version conflicts
- See [Language Support Guide](./guides/language-support.md)

## 🤝 Contributing

See [Developer Guide](./guides/developer-guide.md) for detailed development setup and contribution guidelines.

## 📄 License

Copyright © 2024 CCL Platform. All rights reserved.

---

For more detailed information, explore the specific guides linked above or contact the Analysis Engine team.